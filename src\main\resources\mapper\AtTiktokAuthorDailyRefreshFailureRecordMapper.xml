<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.AtTiktokAuthorDailyRefreshFailureRecordMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.AtTiktokAuthorDailyRefreshFailureRecordDO">
        <id column="id" property="id" />
        <result column="author_id" property="authorId" />
        <result column="unique_id" property="uniqueId" />
        <result column="sec_uid" property="secUid" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, author_id, unique_id, sec_uid, create_time, update_time
    </sql>

  

  
</mapper>
