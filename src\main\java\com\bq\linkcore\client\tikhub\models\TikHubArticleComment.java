package com.bq.linkcore.client.tikhub.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/22 14:09
 * @className OneBoundXHSArticle
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TikHubArticleComment {

    /**
     * 评论id
     */
    private String commentId;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 评论语言（en，英语）
     */
    private String comment_language;

    /**
     * 发布时间
     */
    private Integer publishTime;

    /**
     * 点赞数
     */
    private Integer likeCount;

    private String commenterId;
    private String commenterName;
    private String commenterUniqueId;
    private String commenterSecUid;
    private String commenterDesc;
    /**
     * 用户地区（US，美国）
     */
    private String commenterRegion;
    private String commenterAvatar;

    /**
     * 回复数（336条）
     */
    private Integer replayCommentTotal;
}
