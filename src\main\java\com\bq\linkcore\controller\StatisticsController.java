package com.bq.linkcore.controller;

import com.bq.data.base.bean.BaseEnterpriseUser;
import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.vo.WorkDashboardRespVo;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ServiceException;
import com.bq.linkcore.services.IArticleService;
import com.bq.linkcore.services.IStatisticsService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.bq.linkcore.common.ResponseMsg.ERROR_UNKNOWN_TOKEN;
import static com.bq.linkcore.common.ResponseMsg.FAIL;

@Api(value = "作品相关接口", tags = "作品相关接口")
@RestController
@Slf4j
@RequestMapping("/stat")
public class StatisticsController extends BaseController {

    @Resource
    private IStatisticsService statisticsService;

    @ApiOperation(value = "刷新达人统计数据")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回信息", response = WorkDashboardRespVo.class)})
    @GetMapping("/author/refresh")
    public ResponseData refreshAuthorStatistics(@RequestParam("atUniqueId") String atUniqueId,
                                                @RequestParam("recordDay") String recordDay) {
        try {
            log.info("/author/refresh, atUniqueId={} recordDay={}", atUniqueId, recordDay);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }

            return statisticsService.refreshAuthorStatistics(baseUser.getId(), atUniqueId, recordDay);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "刷新达人统计数据")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回信息", response = WorkDashboardRespVo.class)})
    @GetMapping("/article/refresh")
    public ResponseData refreshArticleStatistics(@RequestParam("atUniqueId") String atUniqueId,
                                                 @RequestParam("workId") String workId,
                                                 @RequestParam("recordDay") String recordDay) {
        try {
            log.info("/article/refresh, atUniqueId={} recordDay={}", atUniqueId, recordDay);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }

            return statisticsService.refreshWorkStatistics(baseUser.getId(), atUniqueId, workId, recordDay);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }


}
