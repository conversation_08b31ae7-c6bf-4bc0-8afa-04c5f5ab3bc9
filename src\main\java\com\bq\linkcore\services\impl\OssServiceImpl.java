package com.bq.linkcore.services.impl;

import com.bq.data.base.bean.ResponseData;
import com.bq.data.oss.code.Storage;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.services.IOssService;
import com.bq.linkcore.utils.TikTokImageFormatChecker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.Instant;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @date 2025/7/21 1:33
 * @className OssServiceImpl
 * @description
 */
@Slf4j
@Service
public class OssServiceImpl implements IOssService {
    @Autowired
    private Storage storage;

    @Override
    public ResponseData updateUrlFile(String url) {
        String objectName = uploadImageFromUrl(url);
        return RD.ok(objectName);
    }

    /**
     * 从 URL 下载图片并上传到 OSS
     * @param imageUrl 图片 URL
     * @return OSS 访问 URL
     */
    public String uploadImageFromUrl(String imageUrl) {
        // 1. 从 URL 下载图片到字节数组
        byte[] imageData = downloadImage(imageUrl);
        if (imageData == null || imageData.length == 0) {
            throw new RuntimeException("下载图片失败: " + imageUrl);
        }

        // 2. 获取图片格式并生成唯一文件名
        String ext = detectImageFormat(imageUrl, imageData);
        String fileName = generateOSSFileName(ext);

        String objectKey = "tiktok/image/" + fileName;
        return storage.uploadFileByNetUrl("public-image-2025", objectKey, imageUrl, fileName, ext);
    }


    /**
     * 从 URL 下载图片
     */
    private static byte[] downloadImage(String imageUrl) {
        HttpURLConnection connection = null;
        InputStream inputStream = null;
        ByteArrayOutputStream outputStream = null;

        try {
            URL url = new URL(imageUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(10000);
            connection.setRequestProperty("User-Agent", "Mozilla/5.0");

            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw new IOException("HTTP错误: " + responseCode);
            }

            // 替代 readAllBytes() 的方法
            inputStream = new BufferedInputStream(connection.getInputStream());
            outputStream = new ByteArrayOutputStream();

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            return outputStream.toByteArray();
        } catch (Exception e) {
            throw new RuntimeException("图片下载失败: " + e.getMessage(), e);
        } finally {
            try {
                if (inputStream != null) inputStream.close();
            } catch (IOException ignored) {}

            try {
                if (outputStream != null) outputStream.close();
            } catch (IOException ignored) {}

            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 检测图片格式
     */
    private static String detectImageFormat(String imageUrl, byte[] imageData) {
        // 1. 尝试从 URL 扩展名判断
        String extension = getExtensionFromUrl(imageUrl);
        if (extension != null) {
            return extension.toUpperCase();
        }

        // 2. 尝试从文件头判断
        if (imageData.length > 4) {
            // JPEG: FF D8 FF
            if (imageData[0] == (byte) 0xFF && imageData[1] == (byte) 0xD8 && imageData[2] == (byte) 0xFF) {
                return "JPEG";
            }
            // PNG: 89 50 4E 47
            if (imageData[0] == (byte) 0x89 && imageData[1] == 0x50 &&
                    imageData[2] == 0x4E && imageData[3] == 0x47) {
                return "PNG";
            }
            // GIF: 47 49 46 38
            if (imageData[0] == 0x47 && imageData[1] == 0x49 &&
                    imageData[2] == 0x46 && imageData[3] == 0x38) {
                return "GIF";
            }
            // WebP: RIFF....WEBP
            if (imageData.length > 12 &&
                    imageData[0] == 'R' && imageData[1] == 'I' &&
                    imageData[2] == 'F' && imageData[3] == 'F' &&
                    imageData[8] == 'W' && imageData[9] == 'E' &&
                    imageData[10] == 'B' && imageData[11] == 'P') {
                return "WEBP";
            }
        }

        return "JPEG"; // 默认使用JPEG
    }

    /**
     * 从 URL 提取扩展名
     */
    private static String getExtensionFromUrl(String imageUrl) {
        try {
            String path = new URL(imageUrl).getPath();
            int dotIndex = path.lastIndexOf('.');
            if (dotIndex > 0) {
                String ext = path.substring(dotIndex + 1).toLowerCase();
                // 移除查询参数
                int paramIndex = ext.indexOf('?');
                if (paramIndex > 0) {
                    ext = ext.substring(0, paramIndex);
                }
                // 过滤无效扩展名
                if (ext.length() <= 5 && !ext.contains("/") && !ext.contains("\\")) {
                    return ext;
                }
            }
        } catch (Exception e) {
            // 忽略解析错误
        }
        return null;
    }

    /**
     * 生成 OSS 文件名（带时间戳和UUID）
     */
    private static String generateOSSFileName(String format) {
        String extension = format.toLowerCase();
        if (extension.equals("jpeg")) extension = "jpg";

        long timestamp = System.currentTimeMillis();
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);

        return "tt_" + timestamp + "_" + uuid + "." + extension;
    }
}
