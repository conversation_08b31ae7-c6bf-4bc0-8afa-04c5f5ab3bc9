package com.bq.linkcore.bean.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel(value = "用户")
public class AccountInfoVo {
    private String uniqueId;
    private String platform;
    private String authorName;
    private String homeUrl;
    private String authorAvatar;
    private String secUid;
    private String description;
    @ApiModelProperty(value = "总粉丝数")
    private Integer followerCount;
    @ApiModelProperty(value = "总点赞数")
    private Integer likeCount;
    @ApiModelProperty(value = "作品数")
    private Integer followingCount;
    @ApiModelProperty(value = "作品数")
    private Integer workCount;
    @ApiModelProperty(value = "分类")
    private String categoryName;
    @ApiModelProperty(value = "注册地址")
    private String registryLocation;
    private Integer isVerified;
    private Integer registerTime;
    private Integer latestPublishTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Long createTime;
    @ApiModelProperty(value = "上次同步时间")
    private Long lastSyncTime;
}
