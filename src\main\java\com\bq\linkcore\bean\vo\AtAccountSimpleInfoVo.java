package com.bq.linkcore.bean.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AtAccountSimpleInfoVo {

    private String atUniqueId;
    private String name;
    private String desc;
    private Integer fansCount;
    private Integer likeCount;
    private Integer resourceCount;
    private Long playCount;
    private Integer commentCount;
    private Integer shareCount;
    private Integer collectCount;
    private String platform;

    private String uniqueId;
    private String homeUrl;
    private String avatar;
    private String secUid;
    private String description;
    @ApiModelProperty(value = "作品数")
    private Integer followingCount;
    @ApiModelProperty(value = "作品数")
    private Integer workCount;
    @ApiModelProperty(value = "分类")
    private String categoryName;
    @ApiModelProperty(value = "注册地址")
    private String registryLocation;
    private Integer isVerified;
    private Integer registerTime;
    private Integer latestPublishTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Long createTime;
    @ApiModelProperty(value = "上次同步时间")
    private Long lastSyncTime;
}
