package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-15 02:16:14
 * @ClassName: TiktokArticleMusicsDO
 * @Description: TikTok作品背景音乐表
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("tiktok_article_musics")
public class TiktokArticleMusicsDO implements Serializable{

    private static final long serialVersionUID = 1L;

    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 音乐ID
     */
    private String musicId;

    /**
     * 音乐标题
     */
    private String title;

    /**
     * 音乐作者名
     */
    private String authorName;

    /**
     * 音乐作者头像
     */
    private String authorAvatar;

    /**
     * 音乐时长(秒)
     */
    private Integer duration;

    /**
     * 是否受版权保护(1:是,0:否)
     */
    private Integer isCopyrighted;

    /**
     * 是否原创(1:是,0:否)
     */
    private Integer original;

    /**
     * 音乐页面URL
     */
    private String url;




}
