<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.UserTokenRelMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.UserTokenRelDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="token" property="token" />
        <result column="ssotoken" property="ssotoken" />
    </resultMap>
    <sql id="Base_Column_List">
        id, user_id, token, ssotoken
    </sql>

  

  
</mapper>
