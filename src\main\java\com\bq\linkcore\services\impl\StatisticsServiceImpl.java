package com.bq.linkcore.services.impl;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.entity.AtTiktokAuthorProfileHistoryDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorStatDailyDO;
import com.bq.linkcore.biz.ArticleCommentBiz;
import com.bq.linkcore.biz.AtTtAuthorPoolBiz;
import com.bq.linkcore.biz.AtTtAuthorWorkBiz;
import com.bq.linkcore.biz.AtTtStatBiz;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.services.IStatisticsService;
import com.bq.linkcore.services.impl.monitor.provider.tiktok.TiktokDataStorageHelper;
import com.bq.linkcore.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.jni.Local;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2025/7/22 11:40
 * @className StatisticsServiceImpl
 * @description
 */
@Slf4j
@Service
public class StatisticsServiceImpl implements IStatisticsService {
    @Autowired
    private TiktokDataStorageHelper tiktokDataStorageHelper;

    @Resource
    private AtTtAuthorPoolBiz atTtAuthorPoolBiz;

    @Resource
    private AtTtAuthorWorkBiz atTtAuthorWorkBiz;

    @Resource
    private ArticleCommentBiz articleCommentBiz;

    @Resource
    private AtTtStatBiz ttStatBiz;

    @Override
    public ResponseData refreshAuthorStatistics(Long userId, String atUniqueId, String recordDay) {
        LocalDateTime recordDateTime = DateTimeUtil.parseDateString(recordDay);
        LocalDateTime nextDateTime = recordDateTime.plusDays(1);

        String prevDay = DateTimeUtil.formatDateString(recordDateTime);
        String nextDay = DateTimeUtil.formatDateString(nextDateTime);

        AtTiktokAuthorProfileHistoryDO prevAuthorProfileDO = queryAuthorByUniqueIdAndRecordDay(atUniqueId, prevDay);
        if (prevAuthorProfileDO == null) {
            return RD.ok("prevAuthorProfileDO 数据为空");
        }

        AtTiktokAuthorProfileHistoryDO nextAuthorProfileDO = queryAuthorByUniqueIdAndRecordDay(atUniqueId, nextDay);
        if (nextAuthorProfileDO == null) {
            return RD.ok("nextAuthorProfileDO 数据为空");
        }

        calcDaily(recordDay, prevAuthorProfileDO, nextAuthorProfileDO);

        return RD.ok();
    }

    @Override
    public ResponseData refreshWorkStatistics(Long userId, String atUniqueId, String workId, String recordDay) {
        return null;
    }

    public AtTiktokAuthorProfileHistoryDO queryAuthorByUniqueIdAndRecordDay(String uniqueId, String recordDay) {
        AtTiktokAuthorProfileHistoryDO authorProfileHistoryDO = atTtAuthorPoolBiz.queryAuthorByUniqueIdAndRecordDay(uniqueId, recordDay, 1);
        if (authorProfileHistoryDO == null) {
            authorProfileHistoryDO = atTtAuthorPoolBiz.queryAuthorByUniqueIdAndRecordDay(uniqueId, recordDay, 0);
        }

        return authorProfileHistoryDO;
    }

    private void calcDaily(String recordDay, AtTiktokAuthorProfileHistoryDO ytdHistoryDO, AtTiktokAuthorProfileHistoryDO historyDO) {
        AtTiktokAuthorStatDailyDO authorStatDailyDO = new AtTiktokAuthorStatDailyDO();
        authorStatDailyDO.setAuthorId(historyDO.getAuthorId());
        authorStatDailyDO.setUniqueId(historyDO.getUniqueId());
        authorStatDailyDO.setStatDay(recordDay);
        authorStatDailyDO.setVideoCount(historyDO.getVideoCount() - ytdHistoryDO.getVideoCount());
        authorStatDailyDO.setFollowerCount(historyDO.getFollowerCount() - ytdHistoryDO.getFollowerCount());
        authorStatDailyDO.setFollowingCount(historyDO.getFollowingCount() - ytdHistoryDO.getFollowingCount());
        authorStatDailyDO.setHeartCount(historyDO.getHeartCount() - ytdHistoryDO.getHeartCount());
        authorStatDailyDO.setFriendCount(historyDO.getFriendCount() - ytdHistoryDO.getFriendCount());
        authorStatDailyDO.setStatTime(LocalDateTime.now());
        authorStatDailyDO.setIsDel(0);

        ttStatBiz.insertAuthorProfileStatDaily(authorStatDailyDO);
    }


}
