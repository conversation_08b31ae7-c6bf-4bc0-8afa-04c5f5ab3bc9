package com.bq.linkcore.client.emailSend;


import com.bq.linkcore.config.MailSendingConfig;

/**
 * @ClassName: MailSending
 * @Description:
 * @author: rendong.ck@naturobot
 * @date: 2021年10月10日 11:01 上午
 */
public class MailSending implements Runnable{
    //收件人
    private String recipient;
    //主题
    private String title;
    //内容
    private String body;
    //附件路径
    private String attachment;

    private MailSendingConfig mailSendingConfig;

    public MailSending(MailSendingConfig mailSendingConfig, String recipient, String title, String body, String attachment) {
        this.mailSendingConfig = mailSendingConfig;
        this.recipient = recipient;
        this.title = title;
        this.body = body;
        this.attachment = attachment;
    }

    @Override
    public void run() {
        MailSendingUtil.send(mailSendingConfig,recipient, title, body, attachment);
    }
}