package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.TiktokCountryInfoDO;
import com.bq.linkcore.dao.mapper.TiktokCountryInfoMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class TiktokCountryBiz {

    @Resource
    private TiktokCountryInfoMapper tiktokCountryInfoMapper;

    public int insert(TiktokCountryInfoDO industryInfoDO) {
        return tiktokCountryInfoMapper.insert(industryInfoDO);
    }

    public int update(TiktokCountryInfoDO industryInfoDO) {
        return tiktokCountryInfoMapper.updateById(industryInfoDO);
    }

    public TiktokCountryInfoDO query(String countryId) {
        LambdaQueryWrapper<TiktokCountryInfoDO> queryWrapper = new LambdaQueryWrapper<TiktokCountryInfoDO>()
                .eq(TiktokCountryInfoDO::getCountryId, countryId);

        return tiktokCountryInfoMapper.selectOne(queryWrapper);
    }

}
