package com.bq.linkcore.client.tikhub.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ArticleDetailModel {

    /**
     * 发布账号ID
     */
    private String authorId;

    /**
     * 作者账号加密ID-可用于拼接主页
     */
    private String authorIdentity;

    /**
     * 作者头像
     */
    private String authorAvatar;

    /**
     * 作者昵称
     */
    private String authorName;

    /**
     * 发布者主页地址
     */
    private String authorUrl;

    /**
     * 作品唯一标识
     */
    private String workId;

    /**
     * 三方生成的一个作品对应的 唯一ID，不清楚唯一性
     */
    private String workUuid;

    /**
     * 作品链接
     */
    private String url;

    /**
     * 下载链接
     */
    private String downloadUrl;

    /**
     * 作品长链接，有效期一个月
     */
    private String longUrl;

    /**
     * 摘要
     */
    private String digest;

    /**
     * 作品标题标题
     */
    private String title;

    /**
     * 第一帧图片链接
     */
    private String thumbnailLink;

    /**
     * 作品内容
     */
    private String content;

    /**
     * 图片集,多个用;隔开
     */
    private String imgUrls;

    /**
     * 视频url 集合
     */
    private String videoUrls;

    /**
     * 音乐链接
     */
    private String musicUrl;

    /**
     * 音乐作者
     */
    private String musicAuthorName;

    /**
     * 音乐ID
     */
    private String musicId;

    /**
     * 音乐名字
     */
    private String musicName;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 发布日期
     */
    private String publishDay;

    /**
     * 发布地理位置
     */
    private String locationIp;

    /**
     * 阅读量
     */
    private Integer readCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 转发数
     */
    private Integer shareCount;

    /**
     * 收藏数
     */
    private Integer collectCount;

}
