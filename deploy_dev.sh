#!/bin/bash

# 修改APP_NAME为云效上的应用名
APP_NAME=link-core
VERSION_NAME=0.0.1
JAR_NAME=${APP_NAME}-${VERSION_NAME}.jar

echo ${JAR_NAME}

PROG_NAME=$0
ACTION=$1

APP_PORT=9077

HEALTH_CHECK_URL=http://127.0.0.1:${APP_PORT}  # 应用健康检查URL
HEALTH_CHECK_FILE_DIR=/opt/bq_data/${APP_NAME}/status   # 脚本会在这个目录下生成nginx-status文件

APP_HOME=/opt/bq_data/${APP_NAME} # 从package.tgz中解压出来的jar包放到这个目录下
JAR_PATH=${APP_HOME}/${JAR_NAME} # jar包的名字
JAVA_OUT=${APP_HOME}/logs/start.log  #应用的启动日志

# 创建出相关目录
mkdir -p ${HEALTH_CHECK_FILE_DIR}
mkdir -p ${APP_HOME}
mkdir -p ${APP_HOME}/logs
usage() {
    echo "Usage: $PROG_NAME {start|stop|restart}"
    exit 2
}

health_check() {
    echo "check ${HEALTH_CHECK_URL} success"
}

start_application() {
    echo "starting java process"
    nohup java -jar -Dspring.profiles.active=data-dev ${JAR_NAME} >/dev/null 2>&1 &
    echo "started java process"
}

stop_application() {
   pid=`ps -ef | grep java | grep ${JAR_NAME} | grep -v grep |grep -v 'deploy_dev.sh'| awk '{print$2}'`
   if [[ ! $pid ]];then
      echo -e "\rno java process"
      return
   fi

   echo "stop java process"
   times=60
   for e in $(seq 60)
   do
        sleep 1
        COSTTIME=$(($times - $e ))

        pid=`ps -ef | grep java | grep ${JAR_NAME} | grep -v grep |grep -v 'deploy_dev.sh'| awk '{print$2}'`
        if [[ $pid ]];then
            kill -9 $pid
            echo -e  "\r        -- stopping java lasts `expr $COSTTIME` seconds."
        else
            echo -e "\rjava process has exited"
            break;
        fi
   done
   echo ""
}

start() {
    start_application
    health_check
}

stop() {
    stop_application
}

case "$ACTION" in
    start)
        start
    ;;
    stop)
        stop
    ;;
    restart)
        stop
        start
    ;;
    *)
        usage
    ;;
esac