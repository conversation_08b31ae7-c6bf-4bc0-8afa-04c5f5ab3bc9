package com.bq.linkcore.bean.vo;

import com.bq.data.base.bean.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
public class AuthorWorkFilterReqVo extends Page {
    @ApiModelProperty(value = "账号唯一ID")
    private String atUniqueId;
    @ApiModelProperty(value = "开始时间")
    private Long startTime;
    @ApiModelProperty(value = "结束时间")
    private Long endTime;
    @ApiModelProperty(value = "话题标签")
    private String topicTag;
    @ApiModelProperty(value = "常用BGM")
    private String musicId;
    @ApiModelProperty(value = "是否为爆款数据 0: 不为爆款 1: 为爆款")
    private Integer hotVideo;
    @ApiModelProperty(value = "排序字段 0:发布时间, 1:点赞数, 2:评论数, 3:转发数, 4:收藏数")
    private Integer sortFields = 0;
    @ApiModelProperty(value = "排序字段 0: 倒序 1: 正序")
    private Integer sortOrder = 0;
    @ApiModelProperty(value = "搜索关键词")
    private String search;
}
