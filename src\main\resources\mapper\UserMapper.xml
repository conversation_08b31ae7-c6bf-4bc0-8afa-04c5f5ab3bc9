<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.UserMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.UserDO">
        <id column="id" property="id" />
        <result column="phone" property="phone" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="email" property="email" />
        <result column="avatar" property="avatar" />
        <result column="nickname" property="nickname" />
        <result column="is_del" property="isDel" />
        <result column="remark" property="remark" />
        <result column="ram_account" property="ramAccount" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, phone, username, password, email, avatar, nickname, is_del, remark, ram_account, creator, create_time, updater, update_time
    </sql>

  

  
</mapper>
