package com.bq.linkcore.services.impl.aiAction.models;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 账号诊断结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "账号诊断结果")
public class AccountDiagnosisVo {
    
    @ApiModelProperty(value = "账号唯一ID")
    private String uniqueId;
    
    @ApiModelProperty(value = "账号基本信息")
    private AccountBasicInfo accountInfo;
    
    @ApiModelProperty(value = "账号统计数据")
    private AccountStatistics statistics;
    
    @ApiModelProperty(value = "诊断报告内容")
    private String diagnosisReport;
    
    @ApiModelProperty(value = "近期作品示例")
    private List<VideoSample> recentVideos;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AccountBasicInfo {
        @ApiModelProperty(value = "作者昵称")
        private String authorName;
        
        @ApiModelProperty(value = "个人简介")
        private String description;
        
        @ApiModelProperty(value = "粉丝数")
        private Integer followerCount;
        
        @ApiModelProperty(value = "总作品数")
        private Integer videoCount;
        
        @ApiModelProperty(value = "总获赞数")
        private Integer heartCount;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AccountStatistics {
        @ApiModelProperty(value = "近30天发布作品数")
        private Integer recentVideoCount;
        
        @ApiModelProperty(value = "历史最高单条播放量")
        private Integer maxPlayCount;
        
        @ApiModelProperty(value = "平均播放量")
        private Integer avgPlayCount;
        
        @ApiModelProperty(value = "平均点赞数")
        private Integer avgLikeCount;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VideoSample {
        @ApiModelProperty(value = "视频标题")
        private String title;
        
        @ApiModelProperty(value = "播放量")
        private Integer playCount;
        
        @ApiModelProperty(value = "视频链接")
        private String url;
    }
}
