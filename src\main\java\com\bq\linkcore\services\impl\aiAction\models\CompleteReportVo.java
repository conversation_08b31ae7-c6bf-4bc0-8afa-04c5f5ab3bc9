package com.bq.linkcore.services.impl.aiAction.models;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 完整报告结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "完整报告结果")
public class CompleteReportVo {
    
    @ApiModelProperty(value = "账号唯一ID")
    private String uniqueId;
    
    @ApiModelProperty(value = "报告生成时间")
    private LocalDateTime generateTime;
    
    @ApiModelProperty(value = "总处理时间(毫秒)")
    private Long totalProcessingTime;
    
    @ApiModelProperty(value = "账号诊断结果")
    private ReportResultVo<AccountDiagnosisVo> accountDiagnosis;
    
    @ApiModelProperty(value = "内容选题分析结果")
    private ReportResultVo<ContentAnalysisVo> contentAnalysis;
    
    @ApiModelProperty(value = "爆款视频分析结果")
    private ReportResultVo<HitVideoAnalysisVo> hitVideoAnalysis;

    @ApiModelProperty(value = "爆款模板")
    private ReportResultVo<ScriptTemplateAnalysisVo> hotScriptTemplate;
    
    @ApiModelProperty(value = "报告状态")
    private ReportStatus status;
    
    @ApiModelProperty(value = "执行摘要")
    private ExecutionSummary executionSummary;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionSummary {
        @ApiModelProperty(value = "成功模块数")
        private Integer successModules;
        
        @ApiModelProperty(value = "失败模块数")
        private Integer failedModules;
        
        @ApiModelProperty(value = "总模块数")
        private Integer totalModules;
        
        @ApiModelProperty(value = "成功率")
        private Double successRate;
        
        @ApiModelProperty(value = "执行详情")
        private String executionDetails;
    }
    
    public enum ReportStatus {
        SUCCESS("SUCCESS", "报告生成成功"),
        PARTIAL_SUCCESS("PARTIAL_SUCCESS", "部分模块成功"),
        FAILED("FAILED", "报告生成失败");
        
        private final String code;
        private final String description;
        
        ReportStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
