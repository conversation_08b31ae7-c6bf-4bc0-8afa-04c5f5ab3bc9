package com.bq.linkcore.client.speechmatics;

import com.bq.linkcore.client.speechmatics.model.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * Speechmatics API 客户端
 */
@Slf4j
@Component
public class SpeechmaticsClient {

    @Resource(name = "commonRestTemplate")
    private RestTemplate restTemplate;


    private final ObjectMapper objectMapper;

    @Value("${speechmatics.api.base-url:https://asr.api.speechmatics.com/v2}")
    private String baseUrl;

    @Value("${speechmatics.api.token:HzKT1DaWZqf9JEs4vTVsJWZAyPE0E0hk}")
    private String apiToken;

    public SpeechmaticsClient() {
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 添加转录任务（指定语言）
     *
     * @param fileUrl 音频/视频文件URL
     * @param language 语言代码（如 "en", "zh"）
     * @return 任务创建响应
     */
    public SpeechmaticsResponse<CreateJobModel> createTranscriptionJob(String fileUrl, String language) {
        try {
            log.info("开始创建Speechmatics转录任务, fileUrl: {}, language: {}", fileUrl, language);

            // 构建请求URL
            String url = baseUrl + "/jobs/";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + apiToken);
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 构建配置JSON
            TranscriptionConfigModel config = TranscriptionConfigModel.createCustomConfig(fileUrl, language);
            String configJson = objectMapper.writeValueAsString(config);

            // 构建multipart请求体
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("config", configJson);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送请求
            ResponseEntity<CreateJobModel> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    CreateJobModel.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.info("成功创建Speechmatics转录任务, jobId: {}", response.getBody().getId());
                return SpeechmaticsResponse.success(response.getBody());
            } else {
                log.error("创建Speechmatics转录任务失败, statusCode: {}", response.getStatusCode());
                return SpeechmaticsResponse.error("创建任务失败", response.getStatusCode().value());
            }

        } catch (Exception e) {
            log.error("创建Speechmatics转录任务异常, fileUrl: {}, language: {}", fileUrl, language, e);
            return SpeechmaticsResponse.error("创建任务异常: " + e.getMessage());
        }
    }

    /**
     * 检查任务状态
     *
     * @param jobId 任务ID
     * @return 任务状态响应
     */
    public SpeechmaticsResponse<CheckJsonModel> checkJobStatus(String jobId) {
        try {
            log.info("开始检查Speechmatics任务状态, jobId: {}", jobId);

            // 构建请求URL
            String url = baseUrl + "/jobs/" + jobId;

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + apiToken);
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> requestEntity = new HttpEntity<>(headers);

            // 发送请求
            ResponseEntity<CheckJsonModel> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    requestEntity,
                    CheckJsonModel.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.info("成功获取Speechmatics任务状态, jobId: {}, status: {}",
                        jobId, response.getBody().getJob().getStatus());
                return SpeechmaticsResponse.success(response.getBody());
            } else {
                log.error("获取Speechmatics任务状态失败, jobId: {}, statusCode: {}", jobId, response.getStatusCode());
                return SpeechmaticsResponse.error("获取任务状态失败", response.getStatusCode().value());
            }

        } catch (Exception e) {
            log.error("检查Speechmatics任务状态异常, jobId: {}", jobId, e);
            return SpeechmaticsResponse.error("检查任务状态异常: " + e.getMessage());
        }
    }

    /**
     * 获取转录结果
     *
     * @param jobId 任务ID
     * @return 转录结果响应
     */
    public SpeechmaticsResponse<String> getTranscriptionResult(String jobId) {
        try {
            log.info("开始获取Speechmatics转录结果, jobId: {}", jobId);

            String url = baseUrl + "/jobs/" + jobId + "/transcript";

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + apiToken);
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> requestEntity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    requestEntity,
                    String.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.info("成功获取Speechmatics转录结果, jobId: {}", jobId);
                return SpeechmaticsResponse.success(response.getBody());
            } else {
                log.error("获取Speechmatics转录结果失败, jobId: {}, statusCode: {}", jobId, response.getStatusCode());
                return SpeechmaticsResponse.error("获取转录结果失败", response.getStatusCode().value());
            }

        } catch (Exception e) {
            log.error("获取Speechmatics转录结果异常, jobId: {}", jobId, e);
            return SpeechmaticsResponse.error("获取转录结果异常: " + e.getMessage());
        }
    }

    /**
     * 删除任务
     *
     * @param jobId 任务ID
     * @return 删除结果响应
     */
    public SpeechmaticsResponse<String> deleteJob(String jobId) {
        try {
            log.info("开始删除Speechmatics任务, jobId: {}", jobId);

            // 构建请求URL
            String url = baseUrl + "/jobs/" + jobId;

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + apiToken);

            HttpEntity<String> requestEntity = new HttpEntity<>(headers);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.DELETE,
                    requestEntity,
                    String.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("成功删除Speechmatics任务, jobId: {}", jobId);
                return SpeechmaticsResponse.success("任务删除成功");
            } else {
                log.error("删除Speechmatics任务失败, jobId: {}, statusCode: {}", jobId, response.getStatusCode());
                return SpeechmaticsResponse.error("删除任务失败", response.getStatusCode().value());
            }

        } catch (Exception e) {
            log.error("删除Speechmatics任务异常, jobId: {}", jobId, e);
            return SpeechmaticsResponse.error("删除任务异常: " + e.getMessage());
        }
    }
}