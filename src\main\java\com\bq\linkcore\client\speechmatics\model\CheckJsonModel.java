package com.bq.linkcore.client.speechmatics.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CheckJsonModel {
    @JsonProperty("job")
    private JobDetails job;

    public JobDetails getJob() {
        return job;
    }

    @Data
    public static class JobDetails {
        @JsonProperty("id")
        private String id;
        @JsonProperty("status")
        private String status;
        @JsonProperty("created_at")
        private String createdAt;
        @JsonProperty("data_name")
        private String dataName;
        @JsonProperty("duration")
        private Integer duration;
    }

}
