package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-23 20:24:54
 * @ClassName: TiktokProductRecordDO
 * @Description: TikTok达人信息更新记录表
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("tiktok_product_record")
public class TiktokProductRecordDO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 商品关键字
     */
    private String keyword;

    /**
     * 商品类型编号
     */
    private Integer productType;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品封面图标链接
     */
    private String cover;

    /**
     * img: 商品图片列表（多个图片标识符）
     */
    private String images;

    /**
     * 市场价
     */
    private String marketPrice;

    /**
     * 实际价格
     */
    private String price;

    /**
     * 货币类型（如 "USD"）
     */
    private String currency;

    /**
     * 商品详情页 URL
     */
    private String url;

    /**
     * 商品状态（如 90 可能表示上架状态）
     */
    private Integer productStatus;

    /**
     * 是否在店铺中（如 0 表示不在）
     */
    private Integer inShop;

    /**
     * 商品来源（如 "TikTok Shop"）
     */
    private String source;

    /**
     * 卖家 ID
     */
    private String sellerUd;

    /**
     * 广告标签（如 "#sponsored"）
     */
    @TableField("adLabel_name")
    private String adlabelName;

    /**
     * 平台标识（如 5 可能代表 TikTok Shop）
     */
    private Integer platform;

    /**
     * 商品类目
     */
    private String categories;

    /**
     * 业务类型（如 0 表示普通商品）
     */
    private Integer bizType;

    /**
     * sku列表
     */
    private String skus;

    /**
     * 创建者
     */
    private Long creator;

    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;

    /**
     * 数据更新人
     */
    private Long updater;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;




}
