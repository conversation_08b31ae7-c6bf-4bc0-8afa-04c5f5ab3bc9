package com.bq.linkcore.client;

import com.bq.linkcore.client.emailSend.MailSending;
import com.bq.linkcore.client.emailSend.MailSendingPool;
import com.bq.linkcore.config.MailSendingConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class EmailClient {

    @Resource
    private MailSendingConfig mailSendingConfig;


    public void sendVerifyCode(String contact, String code){
        String title = "Your Code - " + code;

        String context = buildEmailHtml(code);
        MailSendingPool mailSendingPool = MailSendingPool.getInstance();
        mailSendingPool.addThread(new MailSending(mailSendingConfig, contact,title,context,null));
    }

    /**
     * 构建HTML邮件模板
     * @param code 验证码
     * @return HTML字符串
     */
    private String buildEmailHtml(String code) {
        return "<div style=\"font-family: Helvetica, 'Hiragino Sans GB', 'Microsoft Yahei', '微软雅黑', Arial, sans-serif; margin: 0; padding: 0; background-color: #f7f7f7;\">"
                + "    <div style=\"width: 100%; max-width: 600px; margin: 0 auto; padding: 40px 0; background-color: #fff;\">"
                + "        <div style=\"text-align: center; padding: 0 30px;\">"
                + "            <h2 style=\"font-size: 24px; color: #333;\">您的安全验证码</h2>"
                + "            <p style=\"font-size: 16px; color: #666;\">请使用以下验证码完成验证:</p>"
                + "            <div style=\"font-size: 36px; color: #007bff; font-weight: bold; margin: 20px 0; letter-spacing: 5px;\">"
                + "                " + code + ""
                + "            </div>"
                + "            <p style=\"font-size: 14px; color: #999;\">验证码有效期: " + 5 + "分钟</p>"
                + "            <hr style=\"border: none; border-top: 1px solid #eee; margin: 30px 0;\"/>"
                + "            <div style=\"text-align: left; font-size: 12px; color: #999;\">"
                + "                <p>&#x1F4A1; 请勿向任何人透露此验证码</p>"
                + "                <p>&#x23F3; 验证码过期后需重新获取</p>"
//                + "                <p>&#x2753; 如有疑问请联系客服: <a href=\"mailto:<EMAIL>\" style=\"color: #007bff; text-decoration: none;\"><EMAIL></a></p>"
                + "            </div>"
                + "        </div>"
                + "    </div>"
                + "    <div style=\"text-align: center; font-size: 12px; color: #aaa; padding: 20px 0;\">"
                + "        如果您未请求此验证码，请忽略此邮件。这可能表示有人尝试访问您的账户。"
                + "    </div>"
                + "</div>";
    }


}
