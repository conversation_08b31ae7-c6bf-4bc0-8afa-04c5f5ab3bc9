<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.TiktokHashtagInfoMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.TiktokHashtagInfoDO">
        <id column="id" property="id" />
        <result column="hashtag_id" property="hashtagId" />
        <result column="hashtag_name" property="hashtagName" />
        <result column="country_id" property="countryId" />
        <result column="industry_id" property="industryId" />
        <result column="is_promoted" property="isPromoted" />
        <result column="trend" property="trend" />
        <result column="creators" property="creators" />
        <result column="publish_cnt" property="publishCnt" />
        <result column="video_views" property="videoViews" />
        <result column="rank" property="rank" />
        <result column="rank_diff" property="rankDiff" />
        <result column="rank_diff_type" property="rankDiffType" />
        <result column="r_period" property="rPeriod" />
        <result column="r_country_code" property="rCountryCode" />
        <result column="r_sort_by" property="rSortBy" />
        <result column="r_industry_id" property="rIndustryId" />
        <result column="r_filter_by" property="rFilterBy" />
        <result column="record_day" property="recordDay" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, hashtag_id, hashtag_name, country_id, industry_id, is_promoted, trend, creators, publish_cnt, video_views, rank, rank_diff, rank_diff_type, r_period, r_country_code, r_sort_by, r_industry_id, r_filter_by, record_day, create_time, update_time
    </sql>

  

  
</mapper>
