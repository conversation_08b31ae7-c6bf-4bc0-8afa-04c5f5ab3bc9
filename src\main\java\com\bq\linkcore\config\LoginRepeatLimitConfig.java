package com.bq.linkcore.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * @Description 用户登录验证码、密码错误次数限制
 * <AUTHOR>
 * @Data 2022/10/18 16:58
 */

@Data
@Component
@Configuration
@RefreshScope
public class LoginRepeatLimitConfig {

    @Value("${messageCode.repeat.limit:3}")
    private Integer codeRepeatLimit;

    @Value("${messageCode.repeat.timeout:5}")
    private Integer codeRepeatTimeout;

    @Value("${password.repeat.limit:5}")
    private Integer pwdRepeatLimit;

    @Value("${password.repeat.timeout:5}")
    private Integer pwdTimeout;

}
