<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.TiktokIndustryInfoMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.TiktokIndustryInfoDO">
        <id column="id" property="id" />
        <result column="industry_id" property="industryId" />
        <result column="industry_value" property="industryValue" />
        <result column="industry_label" property="industryLabel" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, industry_id, industry_value, industry_label, create_time, update_time
    </sql>

  

  
</mapper>
