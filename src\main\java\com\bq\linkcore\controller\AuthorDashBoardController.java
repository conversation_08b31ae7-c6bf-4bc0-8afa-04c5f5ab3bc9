package com.bq.linkcore.controller;

import com.bq.data.base.bean.BaseEnterpriseUser;
import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.vo.*;
import com.bq.linkcore.common.PageResultVO;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ServiceException;
import com.bq.linkcore.services.IAuthorDashboardService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.bq.linkcore.common.ResponseMsg.ERROR_UNKNOWN_TOKEN;
import static com.bq.linkcore.common.ResponseMsg.FAIL;

@Api(value = "作者监控dashboard接口", tags = "作者监控dashboard接口")
@RestController
@Slf4j
@RequestMapping("/author/dashboard")
public class AuthorDashBoardController extends BaseController {

    @Resource
    private IAuthorDashboardService authorDashboardService;


    @ApiOperation(value = "获取达人主页以及互动数据")
    @ApiResponses( value = {@ApiResponse(code=200,message="返回信息",response= AtTtIncrStatVo.class)})
    @GetMapping("/authorInfo")
    public ResponseData queryTxAuthorInfo(@RequestParam("atUniqueId") String atUniqueId) {
        try {
            log.info("/author/dashboard/authorInfo, atUniqueId={}", atUniqueId);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return authorDashboardService.queryTxAuthorInfo(baseUser.getId(), atUniqueId);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "获取达人互动数据趋势图")
    @ApiResponses( value = {@ApiResponse(code=200,message="返回信息",response= AuthorInteractiveTingVo.class)})
    @GetMapping("/interactiveChart")
    public ResponseData<AuthorInteractiveTingVo> queryTxAccountInteractiveChart(
            @ApiParam(value = "达人唯一ID", required = true) @RequestParam("atUniqueId") String atUniqueId,
            @ApiParam(value = "日期类型：7-近7天，15-近15天，30-近30天", required = true) @RequestParam("datType") Integer datType) {
        try {
            log.info("/author/dashboard/interactiveChart, atUniqueId={}, datType={}", atUniqueId, datType);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return authorDashboardService.queryTxAccountInteractiveChart(baseUser.getId(), atUniqueId, datType);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "获取达人作品发布频率柱状图")
    @ApiResponses( value = {@ApiResponse(code=200,message="返回信息",response= AuthorWorkReleaseVo.class)})
    @PostMapping("/workReleaseFrequency")
    public ResponseData<AuthorWorkReleaseVo> queryTxWorkReleaseFrequency(
            @ApiParam(value = "作品发布频率查询参数", required = true) @RequestBody AtWorkFrequencyRuleVo vo) {
        try {
            log.info("/author/dashboard/workReleaseFrequency, vo={}", vo);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return authorDashboardService.queryTxWorkReleaseFrequency(baseUser.getId(), vo);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "获取达人作品标签排行")
    @ApiResponses( value = {@ApiResponse(code=200,message="返回信息",response= PageResultVO.class)})
    @GetMapping("/workTagRanking")
    public ResponseData<PageResultVO<AtTagRankingVo>> queryTxWorkTagRanking(
            @ApiParam(value = "达人唯一ID", required = true) @RequestParam("atUniqueId") String atUniqueId,
            @ApiParam(value = "排序类型：1-点赞数，2-评论数，3-转发数，4-收藏数", required = true) @RequestParam("sortType") String sortType) {
        try {
            log.info("/author/dashboard/workTagRanking, atUniqueId={}, sortType={}", atUniqueId, sortType);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return authorDashboardService.queryTxWorkTagRanking(baseUser.getId(), atUniqueId, sortType);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "获取监控达人作品列表")
    @ApiResponses( value = {@ApiResponse(code=200,message="返回信息",response= PageResultVO.class)})
    @PostMapping("/authorWorkList")
    public ResponseData<PageResultVO<AtAuthorWorkInfoVo>> queryTxAuthorWorkList(
            @ApiParam(value = "作品列表查询参数", required = true) @RequestBody AuthorWorkFilterReqVo vo) {
        try {
            log.info("/author/dashboard/authorWorkList, vo={}", vo);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return authorDashboardService.queryTxAuthorWorkList(baseUser.getId(), vo);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "获取达人标签列表（用于筛选）")
    @ApiResponses( value = {@ApiResponse(code=200,message="返回信息",response= List.class)})
    @GetMapping("/topicTagList")
    public ResponseData<List<TagFilterVo>> queryTxTopicTagList(
            @ApiParam(value = "达人唯一ID", required = true) @RequestParam("atUniqueId") String atUniqueId) {
        try {
            log.info("/author/dashboard/topicTagList, atUniqueId={}", atUniqueId);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return authorDashboardService.queryTxTopicTagList(baseUser.getId(), atUniqueId);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "获取达人音乐列表（用于筛选）")
    @ApiResponses( value = {@ApiResponse(code=200,message="返回信息",response= List.class)})
    @GetMapping("/musicList")
    public ResponseData<List<MusicFilterVo>> queryTxMusicList(
            @ApiParam(value = "达人唯一ID", required = true) @RequestParam("atUniqueId") String atUniqueId) {
        try {
            log.info("/author/dashboard/musicList, atUniqueId={}", atUniqueId);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return authorDashboardService.queryTxMusicList(baseUser.getId(), atUniqueId);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }

}
