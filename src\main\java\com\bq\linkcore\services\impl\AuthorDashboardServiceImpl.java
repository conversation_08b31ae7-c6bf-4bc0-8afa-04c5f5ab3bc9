package com.bq.linkcore.services.impl;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.entity.AtAccountUserSettingDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorPoolDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorStatDailyDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorWorkHistoryDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorWorkRecordDO;
import com.bq.linkcore.bean.entity.TiktokArticleMusicsDO;
import com.bq.linkcore.bean.entity.TiktokArticleTagRelDO;
import com.bq.linkcore.bean.vo.*;
import com.bq.linkcore.biz.AtTtAuthorPoolBiz;
import com.bq.linkcore.biz.AtTtAuthorWorkBiz;
import com.bq.linkcore.biz.AtTtSearchBiz;
import com.bq.linkcore.biz.AtTtStatBiz;
import com.bq.linkcore.biz.AtUserAccountBiz;
import com.bq.linkcore.common.PageResultVO;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ResponseMsg;
import com.bq.linkcore.services.IAuthorDashboardService;
import com.bq.linkcore.services.impl.monitor.provider.tiktok.TiktokMonitoringProvider;
import com.bq.linkcore.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

@Slf4j
@Service
public class AuthorDashboardServiceImpl implements IAuthorDashboardService {

    @Resource
    private AtTtSearchBiz ttSearchBiz;

    @Resource
    private AtTtStatBiz ttStatBiz;

    @Resource
    private AtTtAuthorPoolBiz ttAuthorPoolBiz;

    @Autowired
    private TiktokMonitoringProvider tiktokMonitoringProvider;

    @Resource
    private AtUserAccountBiz atUserAccountBiz;

    @Resource
    private AtTtAuthorWorkBiz ttAuthorWorkBiz;


    @Override
    public ResponseData<AtTtIncrStatVo> queryTxAuthorInfo(Long userId, String atUniqueId) {
        try {
            log.info("开始查询用户作者信息, userId: {}, atUniqueId: {}", userId, atUniqueId);

            if (userId == null || StringUtils.isBlank(atUniqueId)) {
                log.warn("参数不能为空, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.ERROR_INVALID_PARAMS);
            }

            AtTiktokAuthorPoolDO author = ttAuthorPoolBiz.queryAuthorPoolByUniqueId(atUniqueId);
            if (author == null) {
                log.warn("作者池中未找到账户信息: {}", atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "未找到账户信息");
            }

            AtAccountUserSettingDO userAccount = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
            if (userAccount == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            AtTtIncrStatVo result = new AtTtIncrStatVo();

            AtAccountSimpleInfoVo accountInfo = buildAccountSimpleInfo(author, userAccount);
            result.setAccountInfo(accountInfo);

            // 4.2 获取今天和昨天的日期
            String today = DateTimeUtil.formatDateString(LocalDateTime.now());
            String yesterday = DateTimeUtil.formatDateString(LocalDateTime.now().minusDays(1));

            TtIncrStatItemVo todayStats = buildIncrStatItem(atUniqueId, today);
            result.setToday(todayStats);

            TtIncrStatItemVo yesterdayStats = buildIncrStatItem(atUniqueId, yesterday);
            result.setYesterday(yesterdayStats);

            log.info("成功查询用户作者信息, userId: {}, atUniqueId: {}", userId, atUniqueId);
            return RD.ok(result);

        } catch (Exception e) {
            log.error("查询用户作者信息异常, userId: {}, atUniqueId: {}", userId, atUniqueId, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "查询用户作者信息失败");
        }
    }

    @Override
    public ResponseData<AuthorInteractiveTingVo> queryTxAccountInteractiveChart(Long userId, String atUniqueId, Integer datType) {
        try {
            log.info("开始查询账户互动数据趋势图, userId: {}, atUniqueId: {}, datType: {}", userId, atUniqueId, datType);

            // 参数校验
            if (userId == null || StringUtils.isBlank(atUniqueId) || datType == null) {
                log.warn("参数不能为空, userId: {}, atUniqueId: {}, datType: {}", userId, atUniqueId, datType);
                return RD.fail(ResponseMsg.ERROR_INVALID_PARAMS);
            }

            // 校验用户是否有权限查看该账户
            AtAccountUserSettingDO userAccount = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
            if (userAccount == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            // 根据datType生成日期列表
            List<LocalDateTime> dateTimeList = generateDateTimeList(datType);
            if (dateTimeList == null || dateTimeList.isEmpty()) {
                log.warn("不支持的日期类型: {}", datType);
                return RD.fail(ResponseMsg.FAIL.getCode(), "不支持的日期类型");
            }

            // 构建返回结果
            AuthorInteractiveTingVo result = new AuthorInteractiveTingVo();

            // 设置横坐标日期
            List<String> dateStrings = dateTimeList.stream()
                    .map(DateTimeUtil::formatDateString)
                    .collect(java.util.stream.Collectors.toList());
            result.setDateTime(dateStrings);

            // 查询每天的统计数据
            List<TtIncrStatItemVo> statItems = new ArrayList<>();
            for (LocalDateTime dateTime : dateTimeList) {
                String statDay = DateTimeUtil.formatDateString(dateTime);
                TtIncrStatItemVo statItem = buildIncrStatItemForChart(atUniqueId, statDay);
                statItems.add(statItem);
            }
            result.setStatItem(statItems);

            log.info("成功查询账户互动数据趋势图, userId: {}, atUniqueId: {}, datType: {}", userId, atUniqueId, datType);
            return RD.ok(result);

        } catch (Exception e) {
            log.error("查询账户互动数据趋势图异常, userId: {}, atUniqueId: {}, datType: {}", userId, atUniqueId, datType, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "查询账户互动数据趋势图失败");
        }
    }

    @Override
    public ResponseData<AuthorWorkReleaseVo> queryTxWorkReleaseFrequency(Long userId, AtWorkFrequencyRuleVo vo) {
        try {
            log.info("开始查询作品发布频率柱状图, userId: {}, vo: {}", userId, vo);

            // 参数校验
            if (userId == null || vo == null || StringUtils.isBlank(vo.getAtUniqueId()) || StringUtils.isBlank(vo.getDateType())) {
                log.warn("参数不能为空, userId: {}, vo: {}", userId, vo);
                return RD.fail(ResponseMsg.ERROR_INVALID_PARAMS);
            }

            // 校验用户是否有权限查看该账户
            AtAccountUserSettingDO userAccount = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, vo.getAtUniqueId());
            if (userAccount == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, vo.getAtUniqueId());
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            // 根据dateType生成日期范围
            List<LocalDateTime> dateTimeList = generateDateTimeListForWorkRelease(vo.getDateType());
            if (dateTimeList == null || dateTimeList.isEmpty()) {
                log.warn("不支持的日期类型: {}", vo.getDateType());
                return RD.fail(ResponseMsg.FAIL.getCode(), "不支持的日期类型");
            }

            // 构建返回结果
            AuthorWorkReleaseVo result = new AuthorWorkReleaseVo();

            // 设置横坐标日期
            List<String> dateStrings = dateTimeList.stream()
                    .map(DateTimeUtil::formatDateString)
                    .collect(java.util.stream.Collectors.toList());
            result.setDateTime(dateStrings);

            // 统计每天的发布数量
            List<Integer> releaseCountList = new ArrayList<>();
            for (LocalDateTime dateTime : dateTimeList) {
                String recordDay = DateTimeUtil.formatDateString(dateTime);
                int releaseCount = countWorkReleaseByUniqueIdAndRecordDay(vo.getAtUniqueId(), recordDay);
                releaseCountList.add(releaseCount);
            }
            result.setReleaseCount(releaseCountList);

            log.info("成功查询作品发布频率柱状图, userId: {}, atUniqueId: {}, dateType: {}", userId, vo.getAtUniqueId(), vo.getDateType());
            return RD.ok(result);

        } catch (Exception e) {
            log.error("查询作品发布频率柱状图异常, userId: {}, vo: {}", userId, vo, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "查询作品发布频率柱状图失败");
        }
    }

    @Override
    public ResponseData<PageResultVO<AtTagRankingVo>> queryTxWorkTagRanking(Long userId, String atUniqueId, String sortType) {
        try {
            log.info("开始查询作品标签排行, userId: {}, atUniqueId: {}, sortType: {}", userId, atUniqueId, sortType);

            // 1. 参数校验
            if (userId == null || StringUtils.isBlank(atUniqueId) || StringUtils.isBlank(sortType)) {
                log.warn("参数不能为空, userId: {}, atUniqueId: {}, sortType: {}", userId, atUniqueId, sortType);
                return RD.fail(ResponseMsg.ERROR_INVALID_PARAMS);
            }

            // 2. 校验sortType参数
            if (!"1".equals(sortType) && !"2".equals(sortType) && !"3".equals(sortType) && !"4".equals(sortType)) {
                log.warn("不支持的排序类型: {}", sortType);
                return RD.fail(ResponseMsg.FAIL.getCode(), "不支持的排序类型");
            }

            // 3. 校验用户是否有权限查看该账户
            AtAccountUserSettingDO userAccount = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
            if (userAccount == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            // 4. 查询该作者的所有作品记录
            List<AtTiktokAuthorWorkRecordDO> workRecordList = ttAuthorWorkBiz.queryAuthorWorkRecordListByUniqueId(atUniqueId);
            if (workRecordList == null || workRecordList.isEmpty()) {
                log.info("该作者暂无作品记录, atUniqueId: {}", atUniqueId);
                PageResultVO<AtTagRankingVo> emptyResult = new PageResultVO<>();
                emptyResult.setData(new ArrayList<>());
                emptyResult.setTotalCount(0);
                emptyResult.setHasNextPage(false);
                return RD.ok(emptyResult);
            }

            // 5. 提取所有作品ID
            List<String> workIds = workRecordList.stream()
                    .map(AtTiktokAuthorWorkRecordDO::getWorkId)
                    .collect(java.util.stream.Collectors.toList());

            // 6. 查询所有作品的标签关联记录
            List<TiktokArticleTagRelDO> tagRelList = ttAuthorWorkBiz.queryArticleTagRelListByWorkIds(workIds);
            if (tagRelList == null || tagRelList.isEmpty()) {
                log.info("该作者的作品暂无标签记录, atUniqueId: {}", atUniqueId);
                PageResultVO<AtTagRankingVo> emptyResult = new PageResultVO<>();
                emptyResult.setData(new ArrayList<>());
                emptyResult.setTotalCount(0);
                return RD.ok(emptyResult);
            }

            // 7. 构建标签排行数据
            List<AtTagRankingVo> tagRankingList = buildTagRankingList(workRecordList, tagRelList, sortType);

            // 8. 构建分页结果（这里简化处理，返回所有结果）
            PageResultVO<AtTagRankingVo> pageResult = new PageResultVO<>();
            pageResult.setData(tagRankingList);
            pageResult.setTotalCount(tagRankingList.size());

            log.info("成功查询作品标签排行, userId: {}, atUniqueId: {}, sortType: {}, 结果数量: {}",
                    userId, atUniqueId, sortType, tagRankingList.size());
            return RD.ok(pageResult);

        } catch (Exception e) {
            log.error("查询作品标签排行异常, userId: {}, atUniqueId: {}, sortType: {}", userId, atUniqueId, sortType, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "查询作品标签排行失败");
        }
    }

    @Override
    public ResponseData<PageResultVO<AtAuthorWorkInfoVo>> queryTxAuthorWorkList(Long userId, AuthorWorkFilterReqVo vo) {
        try {
            log.info("开始查询达人作品列表, userId: {}, vo: {}", userId, vo);

            // 1. 参数校验
            if (userId == null || vo == null || StringUtils.isBlank(vo.getAtUniqueId())) {
                log.warn("参数不能为空, userId: {}, vo: {}", userId, vo);
                return RD.fail(ResponseMsg.ERROR_INVALID_PARAMS);
            }

            // 2. 校验用户是否有权限查看该账户
            AtAccountUserSettingDO userAccount = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, vo.getAtUniqueId());
            if (userAccount == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, vo.getAtUniqueId());
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            // 3. 设置默认分页参数
            if (vo.getPageNo() == null || vo.getPageNo() <= 0) {
                vo.setPageNo(1);
            }
            if (vo.getPageSize() == null || vo.getPageSize() <= 0) {
                vo.setPageSize(10);
            }

            // 4. 使用PageHelper进行分页查询
            PageHelper.startPage(vo.getPageNo(), vo.getPageSize());
            List<AtTiktokAuthorWorkRecordDO> workRecordList = ttAuthorWorkBiz.queryAuthorWorkRecordListWithFilters(
                    vo.getAtUniqueId(),
                    vo.getStartTime(),
                    vo.getEndTime(),
                    vo.getTopicTag(),
                    vo.getMusicId(),
                    vo.getSearch(),
                    vo.getSortFields(),
                    vo.getSortOrder()
            );

            // 5. 获取分页信息
            PageInfo<AtTiktokAuthorWorkRecordDO> pageInfo = new PageInfo<>(workRecordList);

            // 6. 转换为VO对象
            List<AtAuthorWorkInfoVo> workInfoList = workRecordList.stream()
                    .map(this::convertToAtAuthorWorkInfoVo)
                    .collect(Collectors.toList());

            // 7. 构建分页结果
            PageResultVO<AtAuthorWorkInfoVo> result = new PageResultVO<>();
            result.initialize(vo.getPageNo(), vo.getPageSize());
            result.setData(workInfoList);
            result.setTotalCount((int) pageInfo.getTotal());

            log.info("成功查询达人作品列表, userId: {}, atUniqueId: {}, 总数: {}, 当前页数据: {}",
                    userId, vo.getAtUniqueId(), pageInfo.getTotal(), workInfoList.size());
            return RD.ok(result);

        } catch (Exception e) {
            log.error("查询达人作品列表异常, userId: {}, vo: {}", userId, vo, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "查询达人作品列表失败");
        }
    }

    @Override
    public ResponseData<List<TagFilterVo>> queryTxTopicTagList(Long userId, String atUniqueId) {
        try {
            log.info("开始查询达人标签列表, userId: {}, atUniqueId: {}", userId, atUniqueId);

            // 1. 参数校验
            if (userId == null || StringUtils.isBlank(atUniqueId)) {
                log.warn("参数不能为空, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.ERROR_INVALID_PARAMS);
            }

            // 2. 校验用户是否有权限查看该账户
            AtAccountUserSettingDO userAccount = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
            if (userAccount == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            // 3. 查询该达人的所有不重复标签
            List<String> distinctTags = ttAuthorWorkBiz.queryDistinctTagsByUniqueId(atUniqueId);
            if (distinctTags == null || distinctTags.isEmpty()) {
                log.info("该达人暂无标签数据, atUniqueId: {}", atUniqueId);
                return RD.ok(new ArrayList<TagFilterVo>());
            }

            // 4. 转换为TagFilterVo列表
            List<TagFilterVo> tagFilterList = new ArrayList<>();
            for (int i = 0; i < distinctTags.size(); i++) {
                String tag = distinctTags.get(i);
                TagFilterVo tagFilterVo = new TagFilterVo();
                tagFilterVo.setTagId((long) (i + 1)); // 使用索引+1作为tagId
                tagFilterVo.setTag(tag);
                tagFilterList.add(tagFilterVo);
            }

            log.info("成功查询达人标签列表, userId: {}, atUniqueId: {}, 标签数量: {}", userId, atUniqueId, tagFilterList.size());
            return RD.ok(tagFilterList);

        } catch (Exception e) {
            log.error("查询达人标签列表异常, userId: {}, atUniqueId: {}", userId, atUniqueId, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "查询达人标签列表失败");
        }
    }

    @Override
    public ResponseData<List<MusicFilterVo>> queryTxMusicList(Long userId, String atUniqueId) {
        try {
            log.info("开始查询达人配乐列表, userId: {}, atUniqueId: {}", userId, atUniqueId);

            // 1. 参数校验
            if (userId == null || StringUtils.isBlank(atUniqueId)) {
                log.warn("参数不能为空, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.ERROR_INVALID_PARAMS);
            }

            // 2. 校验用户是否有权限查看该账户
            AtAccountUserSettingDO userAccount = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
            if (userAccount == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            // 3. 查询该作者的所有作品记录
            List<AtTiktokAuthorWorkRecordDO> workRecordList = ttAuthorWorkBiz.queryAuthorWorkRecordListByUniqueId(atUniqueId);
            if (workRecordList == null || workRecordList.isEmpty()) {
                log.info("该作者暂无作品记录, atUniqueId: {}", atUniqueId);
                return RD.ok(new ArrayList<>());
            }

            // 4. 提取所有作品的音乐ID（去重）
            List<String> musicIds = workRecordList.stream()
                    .map(AtTiktokAuthorWorkRecordDO::getMusicId)
                    .filter(musicId -> musicId != null && !musicId.trim().isEmpty())
                    .distinct()
                    .collect(Collectors.toList());

            if (musicIds.isEmpty()) {
                log.info("该作者的作品暂无配乐信息, atUniqueId: {}", atUniqueId);
                return RD.ok(new ArrayList<>());
            }

            // 5. 根据音乐ID列表查询音乐详情
            List<TiktokArticleMusicsDO> musicList = ttAuthorWorkBiz.queryArticleMusicListByMusicIds(musicIds);
            if (musicList == null || musicList.isEmpty()) {
                log.info("未找到对应的音乐详情, atUniqueId: {}, musicIds: {}", atUniqueId, musicIds);
                return RD.ok(new ArrayList<>());
            }

            // 6. 转换为MusicFilterVo列表
            List<MusicFilterVo> musicFilterList = musicList.stream()
                    .map(this::convertToMusicFilterVo)
                    .collect(Collectors.toList());

            log.info("成功查询达人配乐列表, userId: {}, atUniqueId: {}, 结果数量: {}",
                    userId, atUniqueId, musicFilterList.size());
            return RD.ok(musicFilterList);

        } catch (Exception e) {
            log.error("查询达人配乐列表异常, userId: {}, atUniqueId: {}", userId, atUniqueId, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "查询达人配乐列表失败");
        }
    }

    /**
     * 构建账号简单信息
     *
     * @param author      作者池信息
     * @param userAccount 用户账户设置信息
     * @return 账号简单信息VO
     */
    private AtAccountSimpleInfoVo buildAccountSimpleInfo(AtTiktokAuthorPoolDO author, AtAccountUserSettingDO userAccount) {
        AtAccountSimpleInfoVo accountInfo = new AtAccountSimpleInfoVo();
        accountInfo.setAtUniqueId(author.getUniqueId());

        // 优先使用用户设置的备注名称，如果没有则使用作者名称
        String displayName = userAccount.getNickname() != null && !userAccount.getNickname().trim().isEmpty()
                ? userAccount.getNickname() : author.getAuthorName();
        accountInfo.setName(displayName);

        accountInfo.setDesc(author.getDesc());
        accountInfo.setFansCount(author.getFollowerCount());
        accountInfo.setLikeCount(author.getHeartCount());
        accountInfo.setResourceCount(author.getVideoCount());
        accountInfo.setPlatform("tiktok");

        // 从作者池获取基础数据，播放量、评论数、分享数、收藏数需要从统计表获取
        accountInfo.setPlayCount(0L);
        accountInfo.setCommentCount(0);
        accountInfo.setShareCount(0);
        accountInfo.setCollectCount(0);

        return accountInfo;
    }

    /**
     * 构建增量统计项
     *
     * @param uniqueId 用户唯一ID
     * @param statDay  统计日期
     * @return 增量统计项VO
     */
    private TtIncrStatItemVo buildIncrStatItem(String uniqueId, String statDay) {
        TtIncrStatItemVo statItem = new TtIncrStatItemVo();

        try {
            // 获取作者主页统计数据（at_tiktok_author_stat_daily表包含了所有需要的统计数据）
            AtTiktokAuthorStatDailyDO authorStat = ttStatBiz.queryAuthorProfileStatDailyByUniqueIdAndStatDay(uniqueId, statDay);

            if (authorStat != null) {
                // 设置粉丝增量（这里需要计算增量，暂时设置为当前值）
                statItem.setAddFansCount(authorStat.getFollowerCount() != null ? authorStat.getFollowerCount() : 0);

                // 设置点赞增量
                statItem.setAddLikeCount(authorStat.getHeartCount() != null ? authorStat.getHeartCount() : 0);

                // 设置资源增量（视频数）
                statItem.setAddResourceCount(authorStat.getVideoCount() != null ? authorStat.getVideoCount() : 0);

                // 设置播放量增量
                statItem.setAddPlayCount(authorStat.getPlayCount() != null ? authorStat.getPlayCount().longValue() : 0L);

                // 设置关注数
                statItem.setFollowingCount(authorStat.getFollowingCount() != null ? authorStat.getFollowingCount() : 0);
                statItem.setAddFollowingCount(0); // 关注增量暂时设为0

                statItem.setAddCommentCount(authorStat.getCommentCount());
                statItem.setAddShareCount(authorStat.getShareCount());
                statItem.setAddCollectCount(authorStat.getCollectCount());

                // 使用资源数设置为视频数（表示有数据的作品数量）
                statItem.setAddUsingResourceCount(authorStat.getVideoCount() != null ? authorStat.getVideoCount() : 0);

            } else {
                // 如果没有统计数据，设置默认值
                setDefaultStatValues(statItem);
            }

        } catch (Exception e) {
            log.error("构建增量统计项异常, uniqueId: {}, statDay: {}", uniqueId, statDay, e);
            // 异常时返回默认值
            setDefaultStatValues(statItem);
        }

        return statItem;
    }

    /**
     * 设置默认统计值
     *
     * @param statItem 统计项
     */
    private void setDefaultStatValues(TtIncrStatItemVo statItem) {
        statItem.setAddFansCount(0);
        statItem.setAddLikeCount(0);
        statItem.setAddResourceCount(0);
        statItem.setAddUsingResourceCount(0);
        statItem.setAddPlayCount(0L);
        statItem.setAddCommentCount(0);
        statItem.setAddShareCount(0);
        statItem.setAddCollectCount(0);
        statItem.setFollowingCount(0);
        statItem.setAddFollowingCount(0);
    }

    /**
     * 根据日期类型生成日期列表
     *
     * @param datType 日期类型 (7, 15, 30)
     * @return 日期列表
     */
    private List<LocalDateTime> generateDateTimeList(Integer datType) {
        List<LocalDateTime> dateTimeList = new ArrayList<>();
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);

        switch (datType) {
            case 7:
                // 最近7天（包含今天）
                for (int i = 6; i >= 0; i--) {
                    dateTimeList.add(today.minusDays(i));
                }
                break;
            case 15:
                // 最近15天（包含今天）
                for (int i = 14; i >= 0; i--) {
                    dateTimeList.add(today.minusDays(i));
                }
                break;
            case 30:
                // 最近30天（包含今天）
                for (int i = 29; i >= 0; i--) {
                    dateTimeList.add(today.minusDays(i));
                }
                break;
            default:
                log.warn("不支持的日期类型: {}", datType);
                return null;
        }

        return dateTimeList;
    }

    /**
     * 为图表构建增量统计项（与原方法类似，但专门用于趋势图）
     *
     * @param uniqueId 用户唯一ID
     * @param statDay  统计日期
     * @return 增量统计项VO
     */
    private TtIncrStatItemVo buildIncrStatItemForChart(String uniqueId, String statDay) {
        TtIncrStatItemVo statItem = new TtIncrStatItemVo();

        try {
            // 获取作者主页统计数据
            AtTiktokAuthorStatDailyDO authorStat = ttStatBiz.queryAuthorProfileStatDailyByUniqueIdAndStatDay(uniqueId, statDay);

            if (authorStat != null) {
                // 对于趋势图，我们显示当日的绝对值而不是增量
                // 如果需要显示增量，需要与前一天的数据进行比较
                statItem.setAddFansCount(authorStat.getFollowerCount() != null ? authorStat.getFollowerCount() : 0);
                statItem.setAddLikeCount(authorStat.getHeartCount() != null ? authorStat.getHeartCount() : 0);
                statItem.setAddResourceCount(authorStat.getVideoCount() != null ? authorStat.getVideoCount() : 0);
                statItem.setAddPlayCount(authorStat.getPlayCount() != null ? authorStat.getPlayCount().longValue() : 0L);
                statItem.setFollowingCount(authorStat.getFollowingCount() != null ? authorStat.getFollowingCount() : 0);
                statItem.setAddFollowingCount(0); // 关注增量暂时设为0
                statItem.setAddCommentCount(authorStat.getCommentCount() != null ? authorStat.getCommentCount() : 0);
                statItem.setAddShareCount(authorStat.getShareCount() != null ? authorStat.getShareCount() : 0);
                statItem.setAddCollectCount(authorStat.getCollectCount() != null ? authorStat.getCollectCount() : 0);
                statItem.setAddUsingResourceCount(authorStat.getVideoCount() != null ? authorStat.getVideoCount() : 0);
            } else {
                // 如果没有统计数据，设置默认值
                setDefaultStatValues(statItem);
            }

        } catch (Exception e) {
            log.error("构建图表增量统计项异常, uniqueId: {}, statDay: {}", uniqueId, statDay, e);
            // 异常时返回默认值
            setDefaultStatValues(statItem);
        }

        return statItem;
    }

    /**
     * 根据日期类型生成日期列表（用于作品发布频率统计）
     *
     * @param dateType 日期类型 ("7"-近一周, "14"-近两周)
     * @return 日期列表
     */
    private List<LocalDateTime> generateDateTimeListForWorkRelease(String dateType) {
        List<LocalDateTime> dateTimeList = new ArrayList<>();
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);

        switch (dateType) {
            case "7":
                // 最近7天（包含今天）
                for (int i = 6; i >= 0; i--) {
                    dateTimeList.add(today.minusDays(i));
                }
                break;
            case "14":
                // 最近14天（包含今天）
                for (int i = 13; i >= 0; i--) {
                    dateTimeList.add(today.minusDays(i));
                }
                break;
            default:
                log.warn("不支持的日期类型: {}", dateType);
                return null;
        }

        return dateTimeList;
    }

    /**
     * 统计指定用户在指定日期的作品发布数量
     *
     * @param uniqueId  用户唯一ID
     * @param recordDay 记录日期 (yyyy-MM-dd格式)
     * @return 发布数量
     */
    private int countWorkReleaseByUniqueIdAndRecordDay(String uniqueId, String recordDay) {
        try {
            // 查询该用户在指定日期的所有作品历史记录
            List<AtTiktokAuthorWorkHistoryDO> workHistoryList =
                ttAuthorWorkBiz.queryAuthorWorkHistoryListByUniqueIdAndRecordDay(uniqueId, recordDay);

            if (workHistoryList == null) {
                return 0;
            }

            return workHistoryList.size();
        } catch (Exception e) {
            log.error("统计作品发布数量异常, uniqueId: {}, recordDay: {}", uniqueId, recordDay, e);
            return 0;
        }
    }

    /**
     * 构建标签排行列表
     *
     * @param workRecordList 作品记录列表
     * @param tagRelList     标签关联记录列表
     * @param sortType       排序类型 (1:点赞数, 2:评论数, 3:转发数, 4:收藏数)
     * @return 标签排行列表
     */
    private List<AtTagRankingVo> buildTagRankingList(List<AtTiktokAuthorWorkRecordDO> workRecordList,
                                                     List<TiktokArticleTagRelDO> tagRelList,
                                                     String sortType) {
        try {
            // 1. 构建workId到作品记录的映射
            Map<String, AtTiktokAuthorWorkRecordDO> workRecordMap = workRecordList.stream()
                    .collect(Collectors.toMap(AtTiktokAuthorWorkRecordDO::getWorkId, work -> work));

            // 2. 按标签分组统计指标值
            Map<String, Integer> tagStatMap = new HashMap<>();

            for (TiktokArticleTagRelDO tagRel : tagRelList) {
                String tag = tagRel.getTag();
                String workId = tagRel.getWorkId();

                AtTiktokAuthorWorkRecordDO workRecord = workRecordMap.get(workId);
                if (workRecord == null) {
                    continue;
                }

                // 根据sortType获取对应的指标值
                Integer statValue = getStatValueBySortType(workRecord, sortType);
                if (statValue == null) {
                    statValue = 0;
                }

                // 累加该标签的指标值
                tagStatMap.put(tag, tagStatMap.getOrDefault(tag, 0) + statValue);
            }

            // 3. 转换为AtTagRankingVo列表并按count降序排序
            List<AtTagRankingVo> tagRankingList = tagStatMap.entrySet().stream()
                    .map(entry -> {
                        AtTagRankingVo vo = new AtTagRankingVo();
                        vo.setTag(entry.getKey());
                        vo.setCount(entry.getValue());
                        return vo;
                    })
                    .sorted((a, b) -> Integer.compare(b.getCount(), a.getCount())) // 降序排列
                    .collect(Collectors.toList());

            return tagRankingList;

        } catch (Exception e) {
            log.error("构建标签排行列表异常, sortType: {}", sortType, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据排序类型获取对应的统计值
     *
     * @param workRecord 作品记录
     * @param sortType   排序类型 (1:点赞数, 2:评论数, 3:转发数, 4:收藏数)
     * @return 统计值
     */
    private Integer getStatValueBySortType(AtTiktokAuthorWorkRecordDO workRecord, String sortType) {
        switch (sortType) {
            case "1": // 点赞数
                return workRecord.getLikeCount();
            case "2": // 评论数
                return workRecord.getCommentCount();
            case "3": // 转发数
                return workRecord.getShareCount();
            case "4": // 收藏数
                return workRecord.getCollectCount();
            default:
                log.warn("不支持的排序类型: {}", sortType);
                return 0;
        }
    }

    /**
     * 将TiktokArticleMusicsDO转换为MusicFilterVo
     *
     * @param musicDO 音乐实体对象
     * @return MusicFilterVo对象
     */
    private MusicFilterVo convertToMusicFilterVo(TiktokArticleMusicsDO musicDO) {
        if (musicDO == null) {
            return null;
        }

        MusicFilterVo musicFilterVo = new MusicFilterVo();
        musicFilterVo.setMusicId(musicDO.getMusicId());

        // 构建音乐名称，包含标题和作者信息
        String musicName = musicDO.getTitle();
        if (musicDO.getAuthorName() != null && !musicDO.getAuthorName().trim().isEmpty()) {
            musicName = musicDO.getTitle() + " - " + musicDO.getAuthorName();
        }
        musicFilterVo.setMusicName(musicName);

        return musicFilterVo;
    }

    /**
     * 将AtTiktokAuthorWorkRecordDO转换为AtAuthorWorkInfoVo
     *
     * @param workRecord 作品记录实体对象
     * @return AtAuthorWorkInfoVo对象
     */
    private AtAuthorWorkInfoVo convertToAtAuthorWorkInfoVo(AtTiktokAuthorWorkRecordDO workRecord) {
        if (workRecord == null) {
            return null;
        }

        AtAuthorWorkInfoVo workInfoVo = new AtAuthorWorkInfoVo();

        // 基本信息
        workInfoVo.setWorkId(workRecord.getWorkId());
        workInfoVo.setAuthorId(workRecord.getAuthorId());
        workInfoVo.setUniqueId(workRecord.getUniqueId());
        workInfoVo.setSecUid(workRecord.getSecUid());
        workInfoVo.setUrl(workRecord.getUrl());
        workInfoVo.setCategoryType(workRecord.getCategoryType());
        workInfoVo.setThumbnailLink(workRecord.getThumbnailLink());
        workInfoVo.setIsAd(workRecord.getIsAd());

        // 内容信息
        workInfoVo.setTitle(workRecord.getTitle());
        workInfoVo.setContent(workRecord.getContent());
        workInfoVo.setHashtags(workRecord.getHashtags());

        // 互动数据
        workInfoVo.setLikeCount(workRecord.getLikeCount());
        workInfoVo.setPlayCount(workRecord.getPlayCount());
        workInfoVo.setCommentCount(workRecord.getCommentCount());
        workInfoVo.setShareCount(workRecord.getShareCount());

        // 媒体信息
        workInfoVo.setVideoUrl(workRecord.getVideoId()); // 这里可能需要根据实际情况调整
        workInfoVo.setPublishTime(workRecord.getPublishTime());

        return workInfoVo;
    }
}
