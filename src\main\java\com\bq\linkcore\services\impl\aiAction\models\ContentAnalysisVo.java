package com.bq.linkcore.services.impl.aiAction.models;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 内容选题分析结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "内容选题分析结果")
public class ContentAnalysisVo {
    
    @ApiModelProperty(value = "账号唯一ID")
    private String uniqueId;
    
    @ApiModelProperty(value = "总作品数")
    private Integer totalVideoCount;
    
    @ApiModelProperty(value = "总播放量")
    private Long totalPlayCount;
    
    @ApiModelProperty(value = "内容类型分析结果")
    private List<ContentItemVo> contentList;

    @ApiModelProperty(value = "内容类型分析结果(新格式)")
    private List<ContentType> contentTypes;
    
    @ApiModelProperty(value = "分析报告JSON")
    private String analysisReportJson;
    
    @ApiModelProperty(value = "分析报告原始文本")
    private String analysisReportText;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContentItemVo {

        @ApiModelProperty(value = "内容分析")
        private String content;

        @ApiModelProperty(value = "相关视频URL")
        private List<String> relatedVideos;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContentType {

        @ApiModelProperty(value = "内容分析")
        private String content;

        @ApiModelProperty(value = "相关视频URL")
        private List<String> relatedVideos;
    }
}
