package com.bq.linkcore.services.impl;

import com.bq.data.base.bean.ResponseData;
import com.bq.data.base.exception.SystemException;
import com.bq.linkcore.bean.dto.TenantUserInfoDTO;
import com.bq.linkcore.bean.entity.*;
import com.bq.linkcore.bean.vo.TenantAddReqVo;
import com.bq.linkcore.bean.vo.UserLoginRspVo;
import com.bq.linkcore.biz.RoleLineBiz;
import com.bq.linkcore.biz.TenantBiz;
import com.bq.linkcore.biz.UserBiz;
import com.bq.linkcore.biz.UserTenantRelBiz;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ServiceException;
import com.bq.linkcore.services.ITenantService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static com.bq.linkcore.common.Constants.role_tenant_creator;
import static com.bq.linkcore.common.ResponseMsg.ERROR_TENANT_REPEAT_EXIST;
import static com.bq.linkcore.common.ResponseMsg.FAIL;


@Service
public class TenantServiceImpl implements ITenantService {

    @Resource
    private RoleLineBiz roleLineBiz;

    @Resource
    private TenantBiz tenantBiz;

    @Resource
    private UserTenantRelBiz userTenantRelBiz;

    @Resource
    private UserBiz userBiz;

    @Autowired
    private UserServiceImpl userService;


    @Override
    public ResponseData addTxTenant(TenantAddReqVo vo) {


        if (tenantBiz.queryTenantByName(vo.getTenantName()) > 0) {
            return RD.fail(ERROR_TENANT_REPEAT_EXIST);
        }

        // 创建
        TenantDO tenantDO = buildTenantDO(vo.getTenantName(), -1L);
        tenantBiz.insertTenant(tenantDO);

        // 初始化两者  默认角色
        insertRoleDO("创建者", tenantDO.getCode(), "", 1, role_tenant_creator);

        if (vo.getType() == 1) {
            UserDO userDO = userBiz.selectUserByEmail(vo.getEmail());
            if (userDO == null) {
                throw new ServiceException(FAIL.getCode(), "该用户已经存在");
            }

            UserTenantRelDO userTenantRel = userTenantRelBiz.queryUserTenantRel(userDO.getId());
            if (userTenantRel != null) {
                throw new ServiceException(FAIL.getCode(), "该用户存在于其他企业");
            }
            userTenantRelBiz.insertUserTenantRel(userDO.getId(), tenantDO.getCode());

            UserRoleRelDO userRoleRelDO = roleLineBiz.queryTenantUserRoleRel(userDO.getId(), tenantDO.getCode());
            if (userRoleRelDO != null) {
                throw new ServiceException(FAIL.getCode(), "该用户已绑定角色");
            }

            roleLineBiz.insertUserRoleRel(role_tenant_creator, userDO.getId(), tenantDO.getCode());

            return RD.ok();
        }

        TenantUserInfoDTO infoDTO = new TenantUserInfoDTO();
        infoDTO.setPhone(vo.getPhone());
        infoDTO.setUsername(vo.getNickname());
        infoDTO.setEmail(vo.getEmail());
        infoDTO.setTenantCode(tenantDO.getCode());
        infoDTO.setRoleCode(role_tenant_creator);
        infoDTO.setPassword("123456");
        UserLoginRspVo userLoginRspVo = userService.insertTxTenantUser(infoDTO);

        return RD.ok(userLoginRspVo);
    }

    private RoleDO insertRoleDO(String roleName,
                                String tenantCode,
                                String roleRemark,
                                Integer roleType,
                                String roleCode) {

        LocalDateTime now = LocalDateTime.now();
        RoleDO roleDO = RoleDO.builder()
                .roleName(roleName)
                .roleCode(roleCode)
                .roleRemark(roleRemark)
                .roleType(roleType)
                .tenantCode(tenantCode)
                .createTime(now)
                .updateTime(now)
                .creator(-1L)
                .updater(-1L)
                .isDel(0)
                .build();

        int i = roleLineBiz.insertRole(roleDO);
        return roleDO;
    }

    private TenantDO buildTenantDO(String tenantName, Long userId) {

        LocalDateTime now = LocalDateTime.now();
        if (StringUtils.isEmpty(tenantName)) {
            throw new SystemException(FAIL.getCode(), "租户名称不能为空");
        }

        return TenantDO.builder()
                .name(tenantName)
                .code(UUID.randomUUID().toString())
                .isAppointAgent(0)
                .createTime(now)
                .creator(userId)
                .build();
    }



}
