package com.bq.linkcore.controller;

import com.bq.data.base.bean.BaseEnterpriseUser;
import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.vo.TranscriptVideoReqVo;
import com.bq.linkcore.bean.vo.TranscriptsRespVo;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ServiceException;
import com.bq.linkcore.services.IAIActionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.bq.linkcore.common.ResponseMsg.ERROR_UNKNOWN_TOKEN;
import static com.bq.linkcore.common.ResponseMsg.FAIL;

@Api(value = "操作/分析视频，作者等操作", tags = "操作/分析视频，作者等操作")
@RestController
@Slf4j
@RequestMapping("/ai/action")
public class ActionController extends BaseController{

    @Resource
    private IAIActionService actionService;


    @ApiOperation(value = "添加视频转写任务")
    @ApiResponses( value = {@ApiResponse(code=200,message="返回信息",response= TranscriptsRespVo.class)})
    @PostMapping("/addTranscriptsTask")
    public ResponseData addTranscriptsTask(@Valid @RequestBody TranscriptVideoReqVo vo) {
        try {
            log.info("/action/addTranscriptsTask, vo={}", vo);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return actionService.addTxVideoTranscriptsTask(baseUser.getId(), vo);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "获取视频转写结果")
    @ApiResponses( value = {@ApiResponse(code=200,message="返回信息",response= TranscriptsRespVo.class)})
    @PostMapping("/queryWorkTaskResult")
    public ResponseData queryTxAuthorInfo(@RequestBody @Valid TranscriptVideoReqVo vo) {
        try {
            log.info("/action/queryTaskResult, vo={}", vo);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return actionService.queryTxTranscriptsTask(baseUser.getId(), vo);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "添加账号拆解")
    @ApiResponses( value = {@ApiResponse(code=200,message="返回信息",response= TranscriptsRespVo.class)})
    @GetMapping("/addAuthorDisassembly")
    public ResponseData addTxAccountDisassemblyTask(@RequestParam("atUniqueId") String atUniqueId) {
        try {
            log.info("/action/addAuthorDisassembly, vo={}", atUniqueId);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return actionService.addTxAccountDisassemblyTask(baseUser.getId(), atUniqueId);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "获取账号拆解任务结果")
    @ApiResponses( value = {@ApiResponse(code=200,message="返回信息",response= TranscriptsRespVo.class)})
    @GetMapping("/queryAuthorTaskResult")
    public ResponseData queryTxAccountDisassemblyResult(@RequestParam("atUniqueId") String atUniqueId) {
        try {
            log.info("/action/queryAuthorTaskResult, vo={}", atUniqueId);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return actionService.queryTxAccountDisassemblyResult(baseUser.getId(), atUniqueId);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }

}
