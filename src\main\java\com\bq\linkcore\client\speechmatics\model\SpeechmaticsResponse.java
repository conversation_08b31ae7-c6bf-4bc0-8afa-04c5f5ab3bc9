package com.bq.linkcore.client.speechmatics.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Speechmatics API 通用响应模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpeechmaticsResponse<T> {
    
    /**
     * 响应是否成功
     */
    private boolean success;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * HTTP状态码
     */
    private int statusCode;
    
    /**
     * 创建成功响应
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> SpeechmaticsResponse<T> success(T data) {
        return SpeechmaticsResponse.<T>builder()
                .success(true)
                .data(data)
                .statusCode(200)
                .build();
    }
    
    /**
     * 创建失败响应
     * @param errorMessage 错误信息
     * @param statusCode HTTP状态码
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> SpeechmaticsResponse<T> error(String errorMessage, int statusCode) {
        return SpeechmaticsResponse.<T>builder()
                .success(false)
                .errorMessage(errorMessage)
                .statusCode(statusCode)
                .build();
    }
    
    /**
     * 创建失败响应（默认500状态码）
     * @param errorMessage 错误信息
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> SpeechmaticsResponse<T> error(String errorMessage) {
        return error(errorMessage, 500);
    }
}
