<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.TiktokArticleMusicsMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.TiktokArticleMusicsDO">
        <id column="id" property="id" />
        <result column="music_id" property="musicId" />
        <result column="title" property="title" />
        <result column="author_name" property="authorName" />
        <result column="author_avatar" property="authorAvatar" />
        <result column="duration" property="duration" />
        <result column="is_copyrighted" property="isCopyrighted" />
        <result column="original" property="original" />
        <result column="url" property="url" />
    </resultMap>
    <sql id="Base_Column_List">
        id, music_id, title, author_name, author_avatar, duration, is_copyrighted, original, url
    </sql>

  

  
</mapper>
