package com.bq.linkcore.config;

import com.bq.linkcore.client.tikhub.tiktok.TikHubTiktokAccountRequester;
import com.bq.linkcore.client.tikhub.tiktok.TikHubTiktokArticleRequester;
import com.bq.linkcore.services.impl.aiAction.ReportGenerator;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
@Slf4j
public class InitRunner implements CommandLineRunner {

    @Autowired
    private TikHubTiktokArticleRequester tikHubTiktokArticleRequester;

    @Autowired
    private TikHubTiktokAccountRequester tikHubTiktokAccountRequester;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private ReportGenerator reportGenerator;

    @Override
    public void run(String... args) throws Exception {
        String uniqueId = "bleacherreport";
        String secUid = "MS4wLjABAAAAGoV19oAGwcauFTYcEN-cNm5MysSbu3Tzfaka3ZUbCmWAfz94GhZLiu9ogAephQrj";
        Integer count = 1;

        // List<TikHubArticle> tikHubArticleList = tikHubTiktokAccountRequester.getUserArticlesByWebV1(uniqueId, secUid, count);
        // List<TikHubArticle> tikHubArticleList = tikHubTiktokAccountRequester.getUserArticlesByAppV3(uniqueId, secUid, count);
        // System.out.println(tikHubArticleList);

        // AuthorInfo authorInfo = tikHubTiktokAccountRequester.getUserProfileWebV1(uniqueId, secUid);
        // AuthorInfo authorInfo = tikHubTiktokAccountRequester.getUserProfileAppV3(uniqueId, secUid);
        // System.out.println(authorInfo);


        // TikHubArticle tikHubArticle = tikHubTiktokArticleRequester.queryArticleWebV1("7527857387424779550");
        // TikHubArticle tikHubArticle = tikHubTiktokArticleRequester.queryArticleAppV3("7527857387424779550");
        // System.out.println(tikHubArticle);

//        // List<TikHubArticleComment> articleComments = tikHubTiktokArticleRequester.queryArticleCommentWebV1("7527857387424779550", 1);
//        List<TikHubArticleComment> articleComments = tikHubTiktokArticleRequester.queryArticleCommentAppV3("7527857387424779550", 1);
//        System.out.println(articleComments);
    }

}
