package com.bq.linkcore.bean.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date: 2025-07-19 02:27:34
 * @ClassName: AtTiktokAuthorUpdateRecordDTO
 * @Description: TikTok达人信息更新记录表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AtTiktokAuthorUpdateRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 发布账号ID
     */
    private String authorId;

    /**
     * 用户名
     */
    private String uniqueId;

    /**
     * 作者账号加密ID(secUid)
     */
    private String secUid;

    /**
     * status，0=等待更新；1=更新中；2=已更新
     */
    private Integer status;

    /**
     * 创建者
     */
    private Long creator;

    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;

    /**
     * 数据更新人
     */
    private Long updater;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;


}
