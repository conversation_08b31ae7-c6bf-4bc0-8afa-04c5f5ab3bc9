package com.bq.linkcore.client.tikhub.tiktok;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.bq.linkcore.client.tikhub.TikHubHttpRequester;
import com.bq.linkcore.client.tikhub.models.AuthorInfo;
import com.bq.linkcore.client.tikhub.models.TikHubArticle;
import com.bq.linkcore.client.tikhub.models.TikHubArticleProduct;
import com.bq.linkcore.client.tikhub.models.TikHubArticleVideo;
import com.bq.linkcore.common.PlatformEnum;
import com.bq.linkcore.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/29 16:32
 * @className TikHubDouYinClient
 * @description
 */
@Slf4j
@Component
public class TikHubTiktokAccountRequester {
    @Autowired
    private TikHubHttpRequester tikHubHttpRequester;

    /**
     * 获取用户主页数据
     * @param uniqueId
     * @param secUid
     * @return
     */
    public AuthorInfo getUserProfileWebV1(String uniqueId, String secUid) {
        String url = "/api/v1/tiktok/web/fetch_user_profile?";
        if (StringUtils.isNotBlank(uniqueId)) {
            url += "uniqueId=" + uniqueId;
        } else {
            if (StringUtils.isNotBlank(secUid)) {
                url += "secUid=" + secUid;
            } else {
                return null;
            }
        }

        JSONObject object = tikHubHttpRequester.callGet(url);
        if (object == null) {
            object = tikHubHttpRequester.callGet(url);
        }

        if (object == null) {
            return null;
        }

        AuthorInfo authorInfo = new AuthorInfo();
        try {
            JSONObject data = object.getJSONObject("data");
            Integer code = data.getInteger("statusCode");
            if (code != 0) {
                return null;
            }

            JSONObject userInfo = data.getJSONObject("userInfo");
            if (userInfo == null) {
                return null;
            }

            JSONObject user = userInfo.getJSONObject("user");
            authorInfo.setAuthorId(StringUtil.parseStr(user, "id"));
            authorInfo.setUniqueId(StringUtil.parseStr(user, "uniqueId"));
            authorInfo.setAuthorName(StringUtil.parseStr(user, "nickname"));
            authorInfo.setAuthorAvatar(StringUtil.parseStr(user, "avatarThumb"));
            authorInfo.setSecUid(StringUtil.parseStr(user, "secUid"));
            authorInfo.setAuthorUrl("https://www.tiktok.com/@" + StringUtil.parseStr(user, "uniqueId"));
            authorInfo.setDesc(StringUtil.parseStr(user, "signature"));
            authorInfo.setPlatform(PlatformEnum.tiktok.getCode());
            authorInfo.setRegisterTime(StringUtil.parseInt(user, "createTime"));
            authorInfo.setIsVerified(StringUtil.booleanToInt(StringUtil.parseBoolean(user, "verified")));

            authorInfo.setRegion(StringUtil.parseStr(user, "region"));
            authorInfo.setLanguage(StringUtil.parseStr(user, "language"));
            authorInfo.setPrivateAccount(StringUtil.booleanToInt(StringUtil.parseBoolean(user, "privateAccount")));

            try {
                JSONObject commerceUserInfo = user.getJSONObject("commerceUserInfo");
                Boolean isCommerceUser = StringUtil.parseBoolean(commerceUserInfo, "commerceUser");
                authorInfo.setCommerceUser(StringUtil.booleanToInt(isCommerceUser));

                if (isCommerceUser) {
                    authorInfo.setCategory(StringUtil.parseStr(commerceUserInfo, "category"));
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

            authorInfo.setTtSeller(StringUtil.parseInt(user, "ttSeller"));

            JSONObject stats = userInfo.getJSONObject("stats");
            if (stats != null) {
                authorInfo.setFollowerCount(StringUtil.parseInt(stats, "followerCount"));
                authorInfo.setFollowingCount(StringUtil.parseInt(stats, "followingCount"));
                authorInfo.setHeartCount(StringUtil.parseInt(stats, "heartCount"));
                authorInfo.setVideoCount(StringUtil.parseInt(stats, "videoCount"));
                authorInfo.setFriendCount(StringUtil.parseInt(stats, "friendCount"));
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }

        return authorInfo;
    }

    /**
     * 获取用户主页数据
     * @param uniqueId
     * @param secUid
     * @return
     */
    public AuthorInfo getUserProfileAppV3(String uniqueId, String secUid) {
        String url = "/api/v1/tiktok/app/v3/handler_user_profile?";
        if (StringUtils.isNotBlank(secUid)) {
            url += "sec_user_id=" + secUid;
        } else {
            if (StringUtils.isNotBlank(uniqueId)) {
                url += "unique_id=" + uniqueId;
            } else {
                return null;
            }
        }


        JSONObject object = tikHubHttpRequester.callGet(url);
        if (object == null) {
            object = tikHubHttpRequester.callGet(url);
        }

        if (object == null) {
            return null;
        }

        AuthorInfo authorInfo = new AuthorInfo();
        try {
            JSONObject data = object.getJSONObject("data");
            Integer code = data.getInteger("status_code");
            if (code != 0) {
                return null;
            }

            JSONObject user = data.getJSONObject("user");
            authorInfo.setAuthorId(StringUtil.parseStr(user, "uid"));
            authorInfo.setUniqueId(StringUtil.parseStr(user, "unique_id"));
            authorInfo.setAuthorName(StringUtil.parseStr(user, "nickname"));
            authorInfo.setAuthorAvatar(parseAuthorAvatarAppV3(user));
            authorInfo.setSecUid(StringUtil.parseStr(user, "sec_uid"));
            authorInfo.setAuthorUrl("https://www.tiktok.com/@" + StringUtil.parseStr(user, "unique_id"));
            authorInfo.setDesc(StringUtil.parseStr(user, "signature"));
            authorInfo.setPlatform(PlatformEnum.tiktok.getCode());
            authorInfo.setIsAdVirtual(StringUtil.booleanToInt(StringUtil.parseBoolean(user, "ad_virtual")));
            // authorInfo.setRegisterTime(StringUtil.parseInt(user, "createTime"));
            authorInfo.setIsVerified(StringUtil.parseInt(user, "verification_type"));

            authorInfo.setRegion("");
            authorInfo.setLanguage(parseLanguageAppV3(user));
            authorInfo.setPrivateAccount(StringUtil.parseInt(user, "secret"));
            authorInfo.setCategory(StringUtil.parseStr(user, "category"));
            Boolean isCommerceUser = StringUtil.parseBoolean(user, "with_commerce_entry");
            authorInfo.setCommerceUser(StringUtil.booleanToInt(isCommerceUser));
            authorInfo.setTtSeller(-1);

            authorInfo.setFollowerCount(StringUtil.parseInt(user, "follower_count"));
            authorInfo.setFollowingCount(StringUtil.parseInt(user, "following_count"));
            authorInfo.setHeartCount(StringUtil.parseInt(user, "total_favorited"));
            authorInfo.setVideoCount(StringUtil.parseInt(user, "aweme_count"));
            authorInfo.setFriendCount(-1);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }

        return authorInfo;
    }

    private String parseAuthorAvatarAppV3(JSONObject user) {
        try {
            JSONObject avatar = user.getJSONObject("avatar_thumb");
            JSONArray urlList = avatar.getJSONArray("url_list");
            return urlList.getString(0);
        } catch (Exception e) {
            log.error(e.getMessage() , e);
        }

        return "";
    }

    private String parseLanguageAppV3(JSONObject user) {
        try {
            return user.getString("signature_language");
        } catch (Exception e) {
            log.error(e.getMessage() , e);
        }

        return "";
    }

    /**
     * 获取用户作品列表
     * @param uniqueId
     * @param secUid
     * @param count
     * @return
     */
    public List<TikHubArticle> getUserArticlesByWebV1(String uniqueId, String secUid, Integer count) {
        List<TikHubArticle> tikHubArticleList = new ArrayList<>();

        Long cursor = 0L;
        Integer pageSize = 100;
        Integer coverFormat = 2;
        Integer sort = 0;
        Boolean hasMore = true;

        while (hasMore) {
            JSONObject object = getUserArticlesPageByWebV1(secUid, cursor, pageSize, coverFormat, sort);
            JSONObject data = object.getJSONObject("data");
            cursor = StringUtil.strToLong(data.getString("cursor"));
            hasMore = data.getBoolean("hasMore");

            JSONArray itemList = data.getJSONArray("itemList");
            for (int index = 0; index < itemList.size(); index++) {
                try {
                    JSONObject item = itemList.getJSONObject(index);

                    TikHubArticle tikHubArticle = new TikHubArticle();
                    JSONObject author = item.getJSONObject("author");
                    tikHubArticle.setAuthorId(StringUtil.parseStr(author, "id"));
                    tikHubArticle.setUniqueId(StringUtil.parseStr(author, "uniqueId"));
                    tikHubArticle.setSecUid(StringUtil.parseStr(author, "secUid"));

                    tikHubArticle.setWorkId(StringUtil.parseStr(item, "id"));
                    tikHubArticle.setWorkUuid(StringUtil.parseStr(item, "id"));
                    String workUrl = "https://www.tiktok.com/@" + StringUtil.parseStr(author, "uniqueId") + "/video/" + tikHubArticle.getWorkId();
                    tikHubArticle.setUrl(workUrl);

                    tikHubArticle.setCategoryType(StringUtil.parseInt(item, "CategoryType"));
                    tikHubArticle.setIsAd(StringUtil.booleanToInt(StringUtil.parseBoolean(item, "isAd")));
                    tikHubArticle.setTitle(StringUtil.parseStr(item, "desc"));
                    tikHubArticle.setContent(StringUtil.parseStr(item, "desc"));
                    tikHubArticle.setHashTags(TiktokDataParser.parseHashTag(item));

                    tikHubArticle.setPublishTime(StringUtil.parseInt(item, "createTime"));
                    tikHubArticle.setTextLanguage(StringUtil.parseStr(item, "textLanguage"));

                    JSONObject stats = item.getJSONObject("stats");
                    tikHubArticle.setPlayCount(StringUtil.parseInt(stats, "playCount"));
                    tikHubArticle.setCollectCount(StringUtil.parseInt(stats, "collectCount"));
                    tikHubArticle.setCommentCount(StringUtil.parseInt(stats, "commentCount"));
                    tikHubArticle.setLikeCount(StringUtil.parseInt(stats, "diggCount"));
                    tikHubArticle.setShareCount(StringUtil.parseInt(stats, "shareCount"));

                    TikHubArticleVideo tikHubArticleVideo = TiktokDataParser.parseVideoWebV1(item);
                    tikHubArticle.setVideo(tikHubArticleVideo);
                    tikHubArticle.setMusic(TiktokDataParser.parseMusicWebV1(item));
                    tikHubArticle.setThumbnailLink(tikHubArticleVideo.getCover());

                    tikHubArticleList.add(tikHubArticle);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }

            if (itemList.size() == 0 || tikHubArticleList.size() >= count) {
                break;
            }
        }

        return tikHubArticleList;
    }

    private JSONObject getUserArticlesPageByWebV1(String secUid,
                                                 Long cursor,
                                                 Integer pageSize,
                                                 Integer coverFormat,
                                                 Integer sort) {
        JSONObject object = getUserArticlesPageInnerByWebV1(secUid, cursor, pageSize, coverFormat, sort);
        if (object == null) {
            object = getUserArticlesPageInnerByWebV1(secUid, cursor, pageSize, coverFormat, sort);
        }

        if (object == null) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            object = getUserArticlesPageInnerByWebV1(secUid, cursor, pageSize, coverFormat, sort);
        }

        return object;
    }


    private JSONObject getUserArticlesPageInnerByWebV1(String secUid,
                                                      Long cursor,
                                                      Integer pageSize,
                                                      Integer coverFormat,
                                                      Integer sort) {
        String url = "/api/v1/tiktok/web/fetch_user_post?";
        url += "secUid=" + secUid;
        url += "&cursor=" + cursor;
        url += "&count=" + pageSize;
        url += "&coverFormat=" + coverFormat;
        url += "&post_item_list_request_type=" + sort;

        return tikHubHttpRequester.callGet2(url);
    }


    /**
     * 获取用户作品列表
     * @param uniqueId
     * @param secUid
     * @param count
     * @return
     */
    public List<TikHubArticle> getUserArticlesByAppV3(String uniqueId, String secUid, Integer count) {
        List<TikHubArticle> tikHubArticleList = new ArrayList<>();

        Long cursor = 0L;
        Integer pageSize = 100;
        Integer sort = 0;
        Boolean hasMore = true;

        while (hasMore) {
            JSONObject object = getUserArticlesPageByAppV3(secUid, cursor, pageSize, sort);
            JSONObject data = object.getJSONObject("data");
            cursor = StringUtil.strToLong(data.getString("max_cursor"));
            hasMore = data.getInteger("has_more").equals(1);

            JSONArray itemList = data.getJSONArray("aweme_list");
            for (int index = 0; index < itemList.size(); index++) {
                try {
                    JSONObject item = itemList.getJSONObject(index);

                    TikHubArticle tikHubArticle = new TikHubArticle();
                    JSONObject author = item.getJSONObject("author");
                    tikHubArticle.setAuthorId(StringUtil.parseStr(author, "uid"));
                    tikHubArticle.setUniqueId(StringUtil.parseStr(author, "unique_id"));
                    tikHubArticle.setSecUid(StringUtil.parseStr(author, "sec_uid"));

                    tikHubArticle.setWorkId(StringUtil.parseStr(item, "aweme_id"));
                    tikHubArticle.setWorkUuid(StringUtil.parseStr(item, "aweme_id"));
                    String workUrl = "https://www.tiktok.com/@" + StringUtil.parseStr(author, "unique_id") + "/video/" + tikHubArticle.getWorkId();
                    tikHubArticle.setUrl(workUrl);

                    tikHubArticle.setCategoryType(-1);
                    tikHubArticle.setIsAd(StringUtil.booleanToInt(StringUtil.parseBoolean(item, "is_ads")));
                    tikHubArticle.setTitle(StringUtil.parseStr(item, "desc"));
                    tikHubArticle.setContent(StringUtil.parseStr(item, "desc"));
                    tikHubArticle.setHashTags(TiktokDataParser.parseHashTagAppV3(item));

                    tikHubArticle.setPublishTime(StringUtil.parseInt(item, "create_time"));
                    tikHubArticle.setTextLanguage(StringUtil.parseStr(item, "desc_language"));

                    JSONObject statistics = item.getJSONObject("statistics");
                    tikHubArticle.setPlayCount(StringUtil.parseInt(statistics, "play_count"));
                    tikHubArticle.setCollectCount(StringUtil.parseInt(statistics, "collect_count"));
                    tikHubArticle.setCommentCount(StringUtil.parseInt(statistics, "comment_count"));
                    tikHubArticle.setLikeCount(StringUtil.parseInt(statistics, "digg_count"));
                    tikHubArticle.setShareCount(StringUtil.parseInt(statistics, "share_count"));

                    TikHubArticleVideo tikHubArticleVideo = TiktokDataParser.parseVideoAppV3(item);
                    tikHubArticle.setVideo(tikHubArticleVideo);
                    tikHubArticle.setMusic(TiktokDataParser.parseMusicAppV3(item));
                    tikHubArticle.setThumbnailLink(tikHubArticleVideo.getCover());

                    List<TikHubArticleProduct> tikHubArticleProductList = TiktokDataParser.parseProductAppV3(item);
                    tikHubArticle.setProduct(tikHubArticleProductList);

                    tikHubArticleList.add(tikHubArticle);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }

            if (itemList.size() == 0 || tikHubArticleList.size() >= count) {
                break;
            }
        }

        return tikHubArticleList;
    }

    private JSONObject getUserArticlesPageByAppV3(String secUid,
                                                  Long cursor,
                                                  Integer pageSize,
                                                  Integer sort) {
        JSONObject object = getUserArticlesPageInnerByAppV3(secUid, cursor, pageSize, sort);
        if (object == null) {
            object = getUserArticlesPageInnerByAppV3(secUid, cursor, pageSize, sort);
        }

        if (object == null) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            object = getUserArticlesPageInnerByAppV3(secUid, cursor, pageSize, sort);
        }

        return object;
    }


    private JSONObject getUserArticlesPageInnerByAppV3(String secUid,
                                                       Long cursor,
                                                       Integer pageSize,
                                                       Integer sort) {
        String url = "/api/v1/tiktok/app/v3/fetch_user_post_videos?";
        url += "sec_user_id=" + secUid;
        url += "&max_cursor=" + cursor;
        url += "&count=" + pageSize;
        url += "&sort_type=" + sort;

        return tikHubHttpRequester.callGet2(url);
    }

}
