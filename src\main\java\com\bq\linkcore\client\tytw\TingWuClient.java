package com.bq.linkcore.client.tytw;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.bq.linkcore.client.tytw.models.MeetingAssistanceModel;
import com.bq.linkcore.client.tytw.models.TingWuVideoModel;
import com.bq.linkcore.client.tytw.models.TranscriptionModel;
import com.bq.linkcore.client.tytw.models.VideoTingWuDTO;
import com.bq.linkcore.utils.StringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
@Component
public class TingWuClient {


    private final static RestTemplate restTemplate = new RestTemplate();

    private static final String appKey = "7NumhDqqle80DKSm";

    private static final String accessKeyId = "LTAI5tCyh8XvKFBCUufhcL8y";
    private static final String secret = "******************************";


    public String startTask(String videoUrl) {
        try {
            CommonRequest request = createCommonRequest(
                    "tingwu.cn-beijing.aliyuncs.com",
                    "2023-09-30",
                    ProtocolType.HTTPS,
                    MethodType.PUT,
                    "/openapi/tingwu/v2/tasks"
            );

            request.putQueryParameter("type", "offline");

            JSONObject root = new JSONObject();
            root.put("AppKey", appKey);


            JSONObject input = new JSONObject();
            input.fluentPut("FileUrl", videoUrl)
                    .fluentPut("SourceLanguage", "cn")
                    .fluentPut("TaskKey", "task" + System.currentTimeMillis());

            root.put("Input", input);

            JSONObject parameters = initRequestParameters();
            root.put("Parameters", parameters);

            System.out.println(root.toJSONString());
            request.setHttpContent(root.toJSONString().getBytes(), "utf-8", FormatType.JSON);

            DefaultProfile profile = DefaultProfile.getProfile(
                    "cn-beijing", accessKeyId, secret);
            IAcsClient client = new DefaultAcsClient(profile);
            CommonResponse response = client.getCommonResponse(request);
            System.out.println(response.getData());
            JSONObject body = JSONObject.parseObject(response.getData());
            JSONObject data = (JSONObject) body.get("Data");
            System.out.println("TaskId = " + data.getString("TaskId"));
            return data.getString("TaskId");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }

    }

    public static CommonRequest createCommonRequest(String domain, String version, ProtocolType protocolType, MethodType method, String uri) {
        // 创建API请求并设置参数
        CommonRequest request = new CommonRequest();
        request.setSysDomain(domain);
        request.setSysVersion(version);
        request.setSysProtocol(protocolType);
        request.setSysMethod(method);
        request.setSysUriPattern(uri);
        request.setHttpContentType(FormatType.JSON);
        return request;
    }

    public static JSONObject initRequestParameters() {
        JSONObject parameters = new JSONObject();

        // MP3 抽离
        JSONObject transcoding = new JSONObject();
        transcoding.put("TargetAudioFormat", "mp3");
        transcoding.put("SpectrumEnabled", false);
        parameters.put("Transcoding", transcoding);

        // 语音识别
        JSONObject transcription = new JSONObject();
        transcription.put("DiarizationEnabled", true);
//        JSONObject speaker_count = new JSONObject();
//        speaker_count.put("SpeakerCount", 2);
//        transcription.put("Diarization", speaker_count);
        parameters.put("Transcription", transcription);


        // 章节速览： 可选
        parameters.put("AutoChaptersEnabled", true);

        // 智能纪要： 可选
        parameters.put("MeetingAssistanceEnabled", true);
        JSONObject meetingAssistance = new JSONObject();
        JSONArray mTypes = new JSONArray()
                .fluentAdd("Actions")
                .fluentAdd("KeyInformation");

        meetingAssistance.put("Types", mTypes);
        parameters.put("MeetingAssistance", meetingAssistance);

        // 摘要相关： 可选
        parameters.put("SummarizationEnabled", true);
        JSONObject summarization = new JSONObject();

        /**
         * Paragraph（全文摘要）
         * Conversational（发言总结）
         * QuestionsAnswering（问答回顾）
         * MindMap（思维导图）
         */
        JSONArray sTypes = new JSONArray()
                .fluentAdd("Paragraph")
                .fluentAdd("Conversational")
                .fluentAdd("QuestionsAnswering")
                .fluentAdd("MindMap");

        summarization.put("Types", sTypes);
        parameters.put("Summarization", summarization);

        // PPT抽取： 可选
//        parameters.put("PptExtractionEnabled", true);

        // 口语书面化： 可选
        parameters.put("TextPolishEnabled", true);

        return parameters;
    }

    private static class Sentence {
        long startTime = Long.MAX_VALUE;
        long endTime = Long.MIN_VALUE;
        final StringBuilder text = new StringBuilder();

        // 添加一个词的信息，并更新时间范围
        public void addWord(Map<String, Object> word) {
            this.text.append(word.get("Text"));
            Number start = (Number) word.get("Start");
            Number end = (Number) word.get("End");
            this.startTime = Math.min(this.startTime, start.longValue());
            this.endTime = Math.max(this.endTime, end.longValue());
        }
    }


    public String queryTaskInfo(String taskId) throws ClientException {
        log.info("正在查询任务 {} 的信息...", taskId);
        String queryUrl = String.format("/openapi/tingwu/v2/tasks" + "/%s", taskId);

        CommonRequest request = createCommonRequest(
                "tingwu.cn-beijing.aliyuncs.com", "2023-09-30", ProtocolType.HTTPS, MethodType.GET, queryUrl
        );

        DefaultProfile profile = DefaultProfile.getProfile(
                "cn-beijing",
                "LTAI5tCyh8XvKFBCUufhcL8y",
                "******************************"
        );
        IAcsClient client = new DefaultAcsClient(profile);
        CommonResponse response = client.getCommonResponse(request);
        log.info(response.getData());
        return response.getData();
    }


    /**
     * 检查任务是否完成
     */

    public VideoTingWuDTO checkCompleted(String taskId) {
        try {
            VideoTingWuDTO videoTingWuDTO = new VideoTingWuDTO();
            String taskInfoJson = queryTaskInfo(taskId);

            videoTingWuDTO.setTaskInfo(taskInfoJson);
            videoTingWuDTO.setTaskId(taskId);
            videoTingWuDTO.setIsSuccess(false);
            videoTingWuDTO.setIsCompleted(false);

            JSONObject jsonObject = JSONObject.parseObject(taskInfoJson);
            if (!jsonObject.getString("Code").equals("0")) {
                return videoTingWuDTO;
            }
            JSONObject data = jsonObject.getJSONObject("Data");
            if (data == null){
                return videoTingWuDTO;
            }

            String taskStatus = data.getString("TaskStatus");

            if (StringUtils.equals("COMPLETED", taskStatus)) {
                videoTingWuDTO.setIsCompleted(true);
                videoTingWuDTO.setIsSuccess(true);
                return videoTingWuDTO;
            }

            if (StringUtils.equals("FAILED", data.getString("TaskStatus"))){
                videoTingWuDTO.setIsCompleted(true);
                videoTingWuDTO.setIsSuccess(false);
                return videoTingWuDTO;
            }

            return videoTingWuDTO;

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public TingWuVideoModel getFullReportByTaskId(String taskInfoJson) {
        try {
            TingWuVideoModel videoModel = new TingWuVideoModel();
            videoModel.setTaskInfo(taskInfoJson);

            JSONObject jsonObject = JSONObject.parseObject(taskInfoJson);
            if (!jsonObject.getString("Code").equals("0")) {
                return null;
            }

            JSONObject data = jsonObject.getJSONObject("Data");
            if (data == null || !"COMPLETED".equals(data.getString("TaskStatus"))) {
                log.error("任务尚未成功完成。状态: " + (data != null ? data.get("TaskStatus") : "未知"));
                return null;
            }

            JSONObject resultUrls = data.getJSONObject("Result");
            if (resultUrls == null) {
                log.error("未在任务结果中找到URL。");
                return null;
            }

            // 语音识别
            if (resultUrls.get("Transcription") != null){
                String transcriptionUrl = (String) resultUrls.get("Transcription");
                TranscriptionModel transcriptionModel = getTranscriptionFromUrl(transcriptionUrl);
                videoModel.setTranscriptionModel(transcriptionModel);
            }

            // 要点提炼
            if (resultUrls.get("MeetingAssistance") != null){
                String assistanceUrl = (String) resultUrls.get("MeetingAssistance");
                MeetingAssistanceModel assistanceReport = getMeetingAssistanceFromUrl(assistanceUrl);
                videoModel.setAssistanceReport(assistanceReport);
            }
            //口语书面化
            if (resultUrls.get("TextPolish") != null){
                String textPolishUrl = (String) resultUrls.get("TextPolish");
                String textPolishReport = getTextPolishFromUrl(textPolishUrl);
                videoModel.setTextPolishReport(StringUtil.truncate(textPolishReport));
            }
            // 章节速览
            if (resultUrls.get("AutoChapters") != null){
                String autoCaptureUrl = (String) resultUrls.get("AutoChapters");
                String autoChaptersReport = getAutoChaptersText(autoCaptureUrl);
                videoModel.setAutoChaptersReport(StringUtil.truncate(autoChaptersReport));
            }

            return videoModel;
        } catch (Exception e) {
            log.error("生成完整报告时出错: {}", e.getMessage());
            return null;
        }
    }

    public String getTextPolishFromUrl(String url) {
        if (url == null || url.isEmpty()){
            return null;
        }
        try {
            String jsonContent = getContentFromUrl(url);
            return parseTextPolish(jsonContent);
        } catch (Exception e) {
            log.error("text polish url :{}", e.getMessage());
            return null;
        }
    }


    public String getAutoChaptersText(String url){
        if (url == null || url.isEmpty()){
            return null;
        }
        try {
            String jsonContent = getContentFromUrl(url);
            return parseAutoChaptersText(jsonContent);
        } catch (Exception e) {
            log.error("处理“文本润色”时出错: {}" , e.getMessage());
            return null;
        }
    }

    private String parseAutoChaptersText(String jsonString) throws IOException {

        ObjectMapper objectMapper = new ObjectMapper();
        StringBuilder resultBuilder = new StringBuilder();
        Map<String, Object> root = objectMapper
                .readValue(jsonString, new TypeReference<Map<String, Object>>() {});

        List<Map<String, Object>> textChapters = (List<Map<String, Object>>) root.get("AutoChapters");
        if (textChapters == null || textChapters.isEmpty()) {
            return "获取章节总结失败";
        }

        int paragraphCounter = 1;
        for (Map<String, Object> paragraphData : textChapters) {

            Number start = (Number) paragraphData.get("Start");
            Number end = (Number) paragraphData.get("End");
            String headline = (String) paragraphData.get("Headline");
            String text = (String) paragraphData.get("Summary");

            String formattedTime = String.format("[%s -> %s]",
                    formatMilliseconds(start.longValue()),
                    formatMilliseconds(end.longValue()));

            resultBuilder.append(paragraphCounter++)
                    .append(". ")
                    .append(formattedTime)
                    .append(" ")
                    .append(text)
                    .append("\n\n");
        }

        return resultBuilder.toString();
    }


    private String parseTextPolish(String jsonString) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        StringBuilder resultBuilder = new StringBuilder();

        Map<String, Object> root = objectMapper
                .readValue(jsonString, new TypeReference<Map<String, Object>>() {});
        List<Map<String, Object>> textPolishList
                = (List<Map<String, Object>>) root.get("TextPolish");

        if (textPolishList == null || textPolishList.isEmpty()) {
            return "";
        }

        int paragraphCounter = 1;
        for (Map<String, Object> paragraphData : textPolishList) {
            Number start = (Number) paragraphData.get("Start");
            Number end = (Number) paragraphData.get("End");
            String text = (String) paragraphData.get("FormalParagraphText");

            String formattedTime = String.format("[%s -> %s]",
                    formatMilliseconds(start.longValue()),
                    formatMilliseconds(end.longValue()));

            resultBuilder.append(paragraphCounter++)
                    .append(". ")
                    .append(formattedTime)
                    .append(" ")
                    .append(text)
                    .append("\n\n");
        }

        return resultBuilder.toString();
    }

    /**
     * 从URL获取“句子摘要 (Transcription)”并格式化
     */
    public TranscriptionModel getTranscriptionFromUrl(String url) {
        if (url == null || url.isEmpty()){
            return null;
        }
        try {
            String jsonContent = getContentFromUrl(url);
            return parseTranscription(jsonContent);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从URL获取“会议纪要 (MeetingAssistance)”并格式化
     */
    public MeetingAssistanceModel getMeetingAssistanceFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            log.error("url 为空 :{}", url);
        }
        try {
            String jsonContent = getContentFromUrl(url);
            return parseMeetingAssistance(jsonContent);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    // --- 私有辅助方法 (核心逻辑) ---

    /**
     * (新增) 通用网络请求方法，负责获取URL内容
     */
    private static String getContentFromUrl(String url) throws IOException, URISyntaxException {
        log.info("正在从URL获取内容: " + url.substring(0, Math.min(url.length(), 100)) + "...");

        RestTemplate restTemplate = new RestTemplate();
        URI uri = new URI(url);
        HttpHeaders headers = new HttpHeaders();
        headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(uri, HttpMethod.GET, entity, String.class);
        return response.getBody();
    }

    /**
     * (重构) 核心解析逻辑：解析“句子摘要 (Transcription)”JSON
     */
    private TranscriptionModel parseTranscription(String jsonString) throws IOException {

        TranscriptionModel transcriptionModel = new TranscriptionModel();
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> root
                = objectMapper.readValue(jsonString, new TypeReference<Map<String, Object>>() {});

        Map<String, Object> transcription = (Map<String, Object>) root.get("Transcription");
        if (transcription == null){
            log.error("解析语音识别数据失败");
            return null;
        }

        if (transcription.get("AudioInfo") != null){
            Map<String, Object> videoInfo =  (Map<String, Object>) transcription.get("AudioInfo");
            Long duration = Long.valueOf((Integer)videoInfo.get("Duration"));
            transcriptionModel.setDuration(duration);
        }

        // 使用TreeMap<Integer, Sentence>来存储每个句子的完整信息，并按ID排序
        TreeMap<Integer, Sentence> sentenceDataMap = new TreeMap<>();
        List<Map<String, Object>> paragraphs = (List<Map<String, Object>>) transcription.get("Paragraphs");

        if (paragraphs != null) {
            for (Map<String, Object> paragraph : paragraphs) {
                List<Map<String, Object>> words = (List<Map<String, Object>>) paragraph.get("Words");
                if (words != null) {
                    for (Map<String, Object> word : words) {

                        Integer sentenceId = (Integer) word.get("SentenceId");
                        sentenceDataMap.computeIfAbsent(sentenceId, id -> new Sentence()).addWord(word);
                    }
                }
            }
        }

        if (sentenceDataMap.isEmpty()) {
            return transcriptionModel;
        }

        // 遍历处理完成的句子Map，生成最终带时间戳的字符串
        StringBuilder finalResult = new StringBuilder();
        sentenceDataMap.forEach((id, sentence) -> {
            String formattedTime = String.format("[%s -> %s]",
                    formatMilliseconds(sentence.startTime),
                    formatMilliseconds(sentence.endTime));

            finalResult.append(id).append(". ")
                    .append(formattedTime).append(" ")
                    .append(sentence.text.toString()).append("\n");
        });

        transcriptionModel.setVideoCopyText(finalResult.toString());
        return transcriptionModel;
    }

    /**
     * 核心解析逻辑：解析“会议纪要 (MeetingAssistance)”JSON
     */
    private MeetingAssistanceModel parseMeetingAssistance(String jsonString) throws IOException {

        MeetingAssistanceModel meetingAssistanceModel = new MeetingAssistanceModel();

        ObjectMapper objectMapper = new ObjectMapper();
        StringBuilder resultBuilder = new StringBuilder();

        Map<String, Object> root = objectMapper
                .readValue(jsonString, new TypeReference<Map<String, Object>>() {});

        Map<String, Object> assistance = (Map<String, Object>) root.get("MeetingAssistance");
        if (assistance == null) {
            return null;
        }

        // 提取关键词
        List<String> keywords = (List<String>) assistance.get("Keywords");
        meetingAssistanceModel.setKeywordList(keywords);


        // 提取核心句子
        List<Map<String, Object>> keySentences = (List<Map<String, Object>>) assistance.get("KeySentences");
        if (keySentences != null && !keySentences.isEmpty()) {
            resultBuilder.append("## 语音转换结果如下: \n");
            for (Map<String, Object> sentence : keySentences) {
                Number start = (Number) sentence.get("Start");
                Number end = (Number) sentence.get("End");

                String formattedTime = String.format(
                        "[%s -> %s]",
                        formatMilliseconds(start.longValue()),
                        formatMilliseconds(end.longValue()));

                resultBuilder.append(sentence.get("Id"))
                        .append(". ")
                        .append(formattedTime)
                        .append(" ")
                        .append(sentence.get("Text"))
                        .append("\n");
            }
            resultBuilder.append("\n");
            meetingAssistanceModel.setAbstractString(StringUtil.truncate(resultBuilder.toString()));
        }

        // 提取内容分类
        Map<String, Double> classifications = (Map<String, Double>) assistance.get("Classifications");
        meetingAssistanceModel.setClassifications(classifications);

        return meetingAssistanceModel;
    }

    /**
     * 工具方法：将毫秒数格式化
     */
    private String formatMilliseconds(long millis) {
        if (millis < 0) return "Invalid Time";
        Duration duration = Duration.ofMillis(millis);
        long hours = duration.toHours();
        long minutes = duration.toMinutes() % 60;
        long seconds = duration.getSeconds() % 60;
        long ms = duration.toMillis() % 1000;
        return String.format("%02d:%02d:%02d.%03d", hours, minutes, seconds, ms);
    }

    public static void main(String[] args) {

    }
}
