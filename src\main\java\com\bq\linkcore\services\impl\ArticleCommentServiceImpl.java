package com.bq.linkcore.services.impl;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.dto.TikhubArticleCommentDTO;
import com.bq.linkcore.bean.entity.AtAccountUserSettingDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorSearchResultDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorWorkRecordDO;
import com.bq.linkcore.bean.entity.TikhubArticleCommentDO;
import com.bq.linkcore.biz.*;
import com.bq.linkcore.client.tikhub.models.TikHubArticleComment;
import com.bq.linkcore.client.tikhub.tiktok.TikHubTiktokAccountRequester;
import com.bq.linkcore.client.tikhub.tiktok.TikHubTiktokArticleRequester;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ResponseMsg;
import com.bq.linkcore.services.IArticleCommentService;
import com.bq.linkcore.services.impl.monitor.provider.tiktok.TiktokMonitoringProvider;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.bq.linkcore.common.ResponseMsg.ERROR_FREQUENTLY;
import static com.bq.linkcore.common.ResponseMsg.ERROR_TIKTOK_AUTHOR_URL;

@Slf4j
@Service
public class ArticleCommentServiceImpl implements IArticleCommentService {

    @Resource
    private TikHubTiktokArticleRequester tikHubTiktokArticleRequester;

    @Autowired
    private TiktokMonitoringProvider tiktokMonitoringProvider;

    @Resource
    private AtTtAuthorWorkBiz atTtAuthorWorkBiz;

    @Resource
    private AtUserAccountBiz atUserAccountBiz;

    @Resource
    private UserMonitorRuleBiz userMonitorRuleBiz;

    @Resource
    private ArticleCommentBiz articleCommentBiz;

    @Resource
    private RedissonClient redissonClient;

    private boolean checkApiLimit(Long userId) {
        String key = "API_LIMIT_KEY_ARTICLE_COMMENT_" + userId;
        RBucket<String> bucket = redissonClient.getBucket(key);
        if (bucket.isExists()) {
            return true;
        }

        bucket.set(key, 3, TimeUnit.SECONDS);
        return false;
    }


    @Override
    public ResponseData updateTxComment(Long userId, String uniqueId, String workId) {
        if (checkApiLimit(userId)) {
            return RD.fail(ERROR_FREQUENTLY);
        }

        Integer commentCount = 10;
        tiktokMonitoringProvider.processArtileCommentAsync(workId, commentCount);

        return RD.ok();
    }

    @Override
    public ResponseData queryTxCommentList(Long userId, String uniqueId, String workId) {
        // 检查用户是否已经在监控该账户
        AtAccountUserSettingDO existingUserAccount = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, uniqueId);
        if (existingUserAccount == null) {
            log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, uniqueId);
            return RD.fail(ResponseMsg.FAIL.getCode(), "未存在账户监控");
        }

        AtTiktokAuthorWorkRecordDO workRecordDO = atTtAuthorWorkBiz.queryAuthorWorkRecordByWorkIdAndUniqueId(uniqueId, workId);
        if (workRecordDO == null) {
            log.warn("作品不存在, uniqueId: {}, workId: {}", uniqueId, workId);
            return RD.fail(ResponseMsg.FAIL.getCode(), "作品不存在");
        }

        List<TikhubArticleCommentDO> articleCommentDOList = articleCommentBiz.queryTiktokArticleCommentList(workId);
        List<TikhubArticleCommentDTO> articleCommentDTOList = articleCommentDOList.stream().map(articleCommentDO -> {
            TikhubArticleCommentDTO commentDTO = new TikhubArticleCommentDTO();
            BeanUtils.copyProperties(articleCommentDO, commentDTO);

            return commentDTO;
        }).collect(Collectors.toList());

        return RD.ok(articleCommentDTOList);
    }

    @Override
    public ResponseData queryTxCommentUpdateStatus(Long userId, String uniqueId, String workId) {
        // 检查用户是否已经在监控该账户
        AtAccountUserSettingDO existingUserAccount = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, uniqueId);
        if (existingUserAccount == null) {
            log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, uniqueId);
            return RD.fail(ResponseMsg.FAIL.getCode(), "未存在账户监控");
        }

        AtTiktokAuthorWorkRecordDO workRecordDO = atTtAuthorWorkBiz.queryAuthorWorkRecordByWorkIdAndUniqueId(uniqueId, workId);
        if (workRecordDO == null) {
            log.warn("作品不存在, uniqueId: {}, workId: {}", uniqueId, workId);
            return RD.fail(ResponseMsg.FAIL.getCode(), "作品不存在");
        }

        boolean isUpdating = tiktokMonitoringProvider.queryIsUpdatingComment(workId);
        return RD.ok(isUpdating);
    }
}
