package com.bq.linkcore.bean.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/7/20 2:10
 * @className AuthorTaggingObj
 * @description
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthorTaggingModel {

    /**
     * 发布账号ID
     */
    private String authorId;

    /**
     * 用户名
     */
    private String uniqueId;

    /**
     * 作者账号加密ID(secUid)
     */
    private String secUid;
}
