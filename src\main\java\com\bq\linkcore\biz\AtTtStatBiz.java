package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.AtTiktokAuthorStatDailyDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorWorkStatDailyDO;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorStatDailyMapper;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorWorkStatDailyMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class AtTtStatBiz {

    @Resource
    private AtTiktokAuthorWorkStatDailyMapper tiktokAuthorWorkStatDailyMapper;

    @Resource
    private AtTiktokAuthorStatDailyMapper tiktokAuthorStatDailyMapper;

    // ==================== AtTiktokAuthorWorkStatDaily 相关方法 ====================

    /**
     * 插入TikTok作者作品统计记录
     * @param authorWorkStatDailyDO TikTok作者作品统计实体对象
     * @return 插入影响的行数
     */
    public int insertAuthorWorkStatDaily(AtTiktokAuthorWorkStatDailyDO authorWorkStatDailyDO) {
        return tiktokAuthorWorkStatDailyMapper.insert(authorWorkStatDailyDO);
    }

    /**
     * 根据ID更新TikTok作者作品统计记录
     * @param authorWorkStatDailyDO TikTok作者作品统计实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateAuthorWorkStatDaily(AtTiktokAuthorWorkStatDailyDO authorWorkStatDailyDO) {
        return tiktokAuthorWorkStatDailyMapper.updateById(authorWorkStatDailyDO);
    }

    /**
     * 根据workId查询TikTok作者作品统计记录
     * @param workId 作品唯一标识
     * @return TikTok作者作品统计实体对象，不存在则返回null
     */
    public AtTiktokAuthorWorkStatDailyDO queryAuthorWorkStatDailyByWorkId(String workId) {
        LambdaQueryWrapper<AtTiktokAuthorWorkStatDailyDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkStatDailyDO>()
                .eq(AtTiktokAuthorWorkStatDailyDO::getWorkId, workId);
        return tiktokAuthorWorkStatDailyMapper.selectOne(queryWrapper);
    }

    /**
     * 根据workId和statDay查询TikTok作者作品统计记录
     * @param workId 作品唯一标识
     * @param statDay 统计日期
     * @return TikTok作者作品统计实体对象，不存在则返回null
     */
    public AtTiktokAuthorWorkStatDailyDO queryAuthorWorkStatDailyByWorkIdAndStatDay(String workId, String statDay) {
        LambdaQueryWrapper<AtTiktokAuthorWorkStatDailyDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkStatDailyDO>()
                .eq(AtTiktokAuthorWorkStatDailyDO::getWorkId, workId)
                .eq(AtTiktokAuthorWorkStatDailyDO::getStatDay, statDay);
        return tiktokAuthorWorkStatDailyMapper.selectOne(queryWrapper);
    }

    /**
     * 根据authorId查询TikTok作者作品统计记录列表
     * @param authorId 发布账号ID
     * @return TikTok作者作品统计记录列表
     */
    public List<AtTiktokAuthorWorkStatDailyDO> queryAuthorWorkStatDailyListByAuthorId(String authorId) {
        LambdaQueryWrapper<AtTiktokAuthorWorkStatDailyDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkStatDailyDO>()
                .eq(AtTiktokAuthorWorkStatDailyDO::getAuthorId, authorId)
                .orderByDesc(AtTiktokAuthorWorkStatDailyDO::getStatTime);
        return tiktokAuthorWorkStatDailyMapper.selectList(queryWrapper);
    }

    /**
     * 根据authorId和statDay查询TikTok作者作品统计记录列表
     * @param authorId 发布账号ID
     * @param statDay 统计日期
     * @return TikTok作者作品统计记录列表
     */
    public List<AtTiktokAuthorWorkStatDailyDO> queryAuthorWorkStatDailyListByAuthorIdAndStatDay(String authorId, String statDay) {
        LambdaQueryWrapper<AtTiktokAuthorWorkStatDailyDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkStatDailyDO>()
                .eq(AtTiktokAuthorWorkStatDailyDO::getAuthorId, authorId)
                .eq(AtTiktokAuthorWorkStatDailyDO::getStatDay, statDay)
                .orderByDesc(AtTiktokAuthorWorkStatDailyDO::getStatTime);
        return tiktokAuthorWorkStatDailyMapper.selectList(queryWrapper);
    }

    /**
     * 根据uniqueId查询TikTok作者作品统计记录列表
     * @param uniqueId 作者唯一用户名
     * @return TikTok作者作品统计记录列表
     */
    public List<AtTiktokAuthorWorkStatDailyDO> queryAuthorWorkStatDailyListByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorWorkStatDailyDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkStatDailyDO>()
                .eq(AtTiktokAuthorWorkStatDailyDO::getUniqueId, uniqueId)
                .orderByDesc(AtTiktokAuthorWorkStatDailyDO::getStatTime);
        return tiktokAuthorWorkStatDailyMapper.selectList(queryWrapper);
    }

    /**
     * 根据uniqueId和statDay查询TikTok作者作品统计记录列表
     * @param uniqueId 作者唯一用户名
     * @param statDay 统计日期
     * @return TikTok作者作品统计记录列表
     */
    public List<AtTiktokAuthorWorkStatDailyDO> queryAuthorWorkStatDailyListByUniqueIdAndStatDay(String uniqueId, String statDay) {
        LambdaQueryWrapper<AtTiktokAuthorWorkStatDailyDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkStatDailyDO>()
                .eq(AtTiktokAuthorWorkStatDailyDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorWorkStatDailyDO::getStatDay, statDay)
                .orderByDesc(AtTiktokAuthorWorkStatDailyDO::getStatTime);
        return tiktokAuthorWorkStatDailyMapper.selectList(queryWrapper);
    }

    // ==================== AtTiktokAuthorProfileStatDaily 相关方法 ====================

    /**
     * 插入TikTok作者主页统计记录
     * @param authorProfileStatDailyDO TikTok作者主页统计实体对象
     * @return 插入影响的行数
     */
    public int insertAuthorProfileStatDaily(AtTiktokAuthorStatDailyDO authorProfileStatDailyDO) {
        return tiktokAuthorStatDailyMapper.insert(authorProfileStatDailyDO);
    }

    /**
     * 根据ID更新TikTok作者主页统计记录
     * @param authorProfileStatDailyDO TikTok作者主页统计实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateAuthorProfileStatDaily(AtTiktokAuthorStatDailyDO authorProfileStatDailyDO) {
        return tiktokAuthorStatDailyMapper.updateById(authorProfileStatDailyDO);
    }

    /**
     * 根据authorId查询TikTok作者主页统计记录
     * @param authorId 发布账号ID
     * @return TikTok作者主页统计实体对象，不存在则返回null
     */
    public AtTiktokAuthorStatDailyDO queryAuthorProfileStatDailyByAuthorId(String authorId) {
        LambdaQueryWrapper<AtTiktokAuthorStatDailyDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorStatDailyDO>()
                .eq(AtTiktokAuthorStatDailyDO::getAuthorId, authorId)
                .eq(AtTiktokAuthorStatDailyDO::getIsDel, 0);
        return tiktokAuthorStatDailyMapper.selectOne(queryWrapper);
    }

    /**
     * 根据authorId和statDay查询TikTok作者主页统计记录
     * @param authorId 发布账号ID
     * @param statDay 统计时间日期
     * @return TikTok作者主页统计实体对象，不存在则返回null
     */
    public AtTiktokAuthorStatDailyDO queryAuthorProfileStatDailyByAuthorIdAndStatDay(String authorId, String statDay) {
        LambdaQueryWrapper<AtTiktokAuthorStatDailyDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorStatDailyDO>()
                .eq(AtTiktokAuthorStatDailyDO::getAuthorId, authorId)
                .eq(AtTiktokAuthorStatDailyDO::getStatDay, statDay)
                .eq(AtTiktokAuthorStatDailyDO::getIsDel, 0);
        return tiktokAuthorStatDailyMapper.selectOne(queryWrapper);
    }

    /**
     * 根据uniqueId查询TikTok作者主页统计记录
     * @param uniqueId 用户名
     * @return TikTok作者主页统计实体对象，不存在则返回null
     */
    public AtTiktokAuthorStatDailyDO queryAuthorProfileStatDailyByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorStatDailyDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorStatDailyDO>()
                .eq(AtTiktokAuthorStatDailyDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorStatDailyDO::getIsDel, 0);
        return tiktokAuthorStatDailyMapper.selectOne(queryWrapper);
    }

    /**
     * 根据uniqueId和statDay查询TikTok作者主页统计记录
     * @param uniqueId 用户名
     * @param statDay 统计时间日期
     * @return TikTok作者主页统计实体对象，不存在则返回null
     */
    public AtTiktokAuthorStatDailyDO queryAuthorProfileStatDailyByUniqueIdAndStatDay(String uniqueId, String statDay) {
        LambdaQueryWrapper<AtTiktokAuthorStatDailyDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorStatDailyDO>()
                .eq(AtTiktokAuthorStatDailyDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorStatDailyDO::getStatDay, statDay)
                .eq(AtTiktokAuthorStatDailyDO::getIsDel, 0);
        return tiktokAuthorStatDailyMapper.selectOne(queryWrapper);
    }

    /**
     * 根据authorId查询TikTok作者主页统计记录列表
     * @param authorId 发布账号ID
     * @return TikTok作者主页统计记录列表
     */
    public List<AtTiktokAuthorStatDailyDO> queryAuthorProfileStatDailyListByAuthorId(String authorId) {
        LambdaQueryWrapper<AtTiktokAuthorStatDailyDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorStatDailyDO>()
                .eq(AtTiktokAuthorStatDailyDO::getAuthorId, authorId)
                .eq(AtTiktokAuthorStatDailyDO::getIsDel, 0)
                .orderByDesc(AtTiktokAuthorStatDailyDO::getStatTime);
        return tiktokAuthorStatDailyMapper.selectList(queryWrapper);
    }

    /**
     * 根据uniqueId查询TikTok作者主页统计记录列表
     * @param uniqueId 用户名
     * @return TikTok作者主页统计记录列表
     */
    public List<AtTiktokAuthorStatDailyDO> queryAuthorProfileStatDailyListByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorStatDailyDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorStatDailyDO>()
                .eq(AtTiktokAuthorStatDailyDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorStatDailyDO::getIsDel, 0)
                .orderByDesc(AtTiktokAuthorStatDailyDO::getStatTime);
        return tiktokAuthorStatDailyMapper.selectList(queryWrapper);
    }

}
