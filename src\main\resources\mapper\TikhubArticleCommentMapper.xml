<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.TikhubArticleCommentMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.TikhubArticleCommentDO">
        <id column="id" property="id" />
        <result column="work_id" property="workId" />
        <result column="comment_id" property="commentId" />
        <result column="content" property="content" />
        <result column="comment_language" property="commentLanguage" />
        <result column="publish_time" property="publishTime" />
        <result column="like_count" property="likeCount" />
        <result column="commenter_id" property="commenterId" />
        <result column="commenter_name" property="commenterName" />
        <result column="commenter_unique_id" property="commenterUniqueId" />
        <result column="commenter_sec_uid" property="commenterSecUid" />
        <result column="commenter_url" property="commenterUrl" />
        <result column="commenter_desc" property="commenterDesc" />
        <result column="commenter_region" property="commenterRegion" />
        <result column="commenter_avatar" property="commenterAvatar" />
        <result column="replay_comment_total" property="replayCommentTotal" />
    </resultMap>
    <sql id="Base_Column_List">
        id, work_id, comment_id, content, comment_language, publish_time, like_count, commenter_id, commenter_name, commenter_unique_id, commenter_sec_uid, commenter_url, commenter_desc, commenter_region, commenter_avatar, replay_comment_total
    </sql>

  

  
</mapper>
