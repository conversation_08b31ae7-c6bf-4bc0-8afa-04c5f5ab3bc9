package com.bq.linkcore.services.impl.aiAction.models;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 爆款视频分析结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "爆款视频分析结果")
public class HitVideoAnalysisVo {
    
    @ApiModelProperty(value = "账号唯一ID")
    private String uniqueId;
    
    @ApiModelProperty(value = "爆款阈值")
    private Long hitThreshold;
    
    @ApiModelProperty(value = "爆款视频数量")
    private Integer hitVideoCount;
    
    @ApiModelProperty(value = "爆款模型分析")
    private List<HitModel> hitModels;
    
    @ApiModelProperty(value = "分析报告JSON")
    private String analysisReportJson;
    
    @ApiModelProperty(value = "分析报告原始文本")
    private String analysisReportText;

    @ApiModelProperty(value = "爆款视频列表")
    private List<HitVideo> hitVideos;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HitModel {

        @ApiModelProperty(value = "组装的内容")
        private String content;

        @ApiModelProperty(value = "案例视频URL")
        private List<String> relatedVideos;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HitVideo {
        @ApiModelProperty(value = "视频标题")
        private String title;

        @ApiModelProperty(value = "播放量")
        private Integer playCount;

        @ApiModelProperty(value = "视频链接")
        private String url;

        @ApiModelProperty(value = "点赞数")
        private Integer likeCount;

        @ApiModelProperty(value = "评论数")
        private Integer commentCount;

        @ApiModelProperty(value = "分享数")
        private Integer shareCount;
    }
}
