package com.bq.linkcore.client.tikhub.tiktok;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bq.linkcore.client.tikhub.models.TikHubArticleMusic;
import com.bq.linkcore.client.tikhub.models.TikHubArticleProduct;
import com.bq.linkcore.client.tikhub.models.TikHubArticleVideo;
import com.bq.linkcore.utils.StringUtil;
import com.bq.linkcore.utils.TikTokVideoIdExtractor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/15 13:25
 * @className TiktokDataParser
 * @description
 */
public class TiktokDataParser {

    public static TikHubArticleVideo parseVideoWebV1(JSONObject note) {
        TikHubArticleVideo tikHubArticleVideo = new TikHubArticleVideo();

        JSONObject video = note.getJSONObject("video");
        tikHubArticleVideo.setVideoID(StringUtil.parseStr(video, "videoID"));
        tikHubArticleVideo.setVideoQuality(StringUtil.parseStr(video, "videoQuality"));
        tikHubArticleVideo.setVqscore(StringUtil.parseStr(video, "VQScore"));
        tikHubArticleVideo.setBitrate(StringUtil.parseLong(video, "bitrate"));
        tikHubArticleVideo.setCodecType(StringUtil.parseStr(video, "codecType"));
        tikHubArticleVideo.setDefinition(StringUtil.parseStr(video, "definition"));
        tikHubArticleVideo.setDuration(StringUtil.parseLong(video, "duration")*1000);
        tikHubArticleVideo.setDataSize(StringUtil.parseLong(video, "size"));
        tikHubArticleVideo.setHeight(StringUtil.parseInt(video, "height"));
        tikHubArticleVideo.setWidth(StringUtil.parseInt(video, "width"));
        tikHubArticleVideo.setCover(StringUtil.parseStr(video, "cover"));

        return tikHubArticleVideo;
    }

    public static TikHubArticleMusic parseMusicWebV1(JSONObject note) {
        TikHubArticleMusic articleMusic = new TikHubArticleMusic();
        JSONObject music = note.getJSONObject("music");

        articleMusic.setMusicId(StringUtil.parseStr(music, "id"));
        articleMusic.setTitle(StringUtil.parseStr(music, "title"));
        articleMusic.setAuthorName(StringUtil.parseStr(music, "authorName"));
        articleMusic.setAuthorAvatar(StringUtil.parseStr(music, "coverThumb"));
        articleMusic.setDuration(StringUtil.parseInt(music, "duration")*1000);
        articleMusic.setIsCopyrighted(StringUtil.booleanToInt(StringUtil.parseBoolean(music, "isCopyrighted")));
        articleMusic.setOriginal(StringUtil.booleanToInt(StringUtil.parseBoolean(music, "original")));
        articleMusic.setUrl(StringUtil.parseStr(music, "playUrl"));

        return articleMusic;
    }

    public static List<String> parseHashTag(JSONObject note) {
        List<String> hashTags = new ArrayList<>();
        try {
            JSONArray textExtraList = note.getJSONArray("textExtra");
            for (int index = 0; index < textExtraList.size(); index++) {
                JSONObject extra = textExtraList.getJSONObject(index);
                hashTags.add(extra.getString("hashtagName"));
            }
        } catch (Exception e) {

        }

        return hashTags;
    }

    public static TikHubArticleVideo parseVideoAppV3(JSONObject note) {
        TikHubArticleVideo tikHubArticleVideo = new TikHubArticleVideo();

        try {
            JSONObject video = note.getJSONObject("video");
            try {
                JSONArray bitRateObjects = video.getJSONArray("bit_rate");
                JSONObject bitRateObject = bitRateObjects.getJSONObject(0);

                tikHubArticleVideo.setBitrate(StringUtil.parseLong(bitRateObject, "bit_rate"));
            } catch (Exception e) {
            }

            tikHubArticleVideo.setVideoID(StringUtil.parseStr(video, "videoID"));
            tikHubArticleVideo.setVideoQuality("");
            tikHubArticleVideo.setVqscore("");
            tikHubArticleVideo.setCodecType(StringUtil.parseStr(video, "codecType"));
            tikHubArticleVideo.setDefinition(StringUtil.parseStr(video, "definition"));
            tikHubArticleVideo.setDuration(StringUtil.parseLong(video, "duration"));

            JSONObject playInfo = video.getJSONObject("play_addr");
            tikHubArticleVideo.setDataSize(StringUtil.parseLong(playInfo, "data_size"));
            tikHubArticleVideo.setHeight(StringUtil.parseInt(playInfo, "height"));
            tikHubArticleVideo.setWidth(StringUtil.parseInt(playInfo, "width"));
            JSONArray downloadUrls = playInfo.getJSONArray("url_list");
            for (int i = 0; i < downloadUrls.size(); i++) {
                String u = downloadUrls.getString(i);
                if (u.contains("aweme/v1/play")) {
                    tikHubArticleVideo.setUrl(u);
                    break;
                }
            }

            if (StringUtils.isBlank(tikHubArticleVideo.getVideoID())) {
                tikHubArticleVideo.setVideoID(TikTokVideoIdExtractor.getTiktokVideoIdAppV1(tikHubArticleVideo.getUrl()));
            }

            JSONObject coverObject = video.getJSONObject("cover");
            JSONArray coverUrls = coverObject.getJSONArray("url_list");
            for (int j = 0; j < coverUrls.size(); j++) {
                String u = coverUrls.getString(j);
                if (u.contains("heic?")) {
                    continue;
                }

                tikHubArticleVideo.setCover(u);
            }
        } catch (Exception e) {
        }

        return tikHubArticleVideo;
    }

    public static TikHubArticleMusic parseMusicAppV3(JSONObject note) {
        TikHubArticleMusic articleMusic = new TikHubArticleMusic();
        try {
            JSONObject music = note.getJSONObject("music");

            articleMusic.setMusicId(StringUtil.parseStr(music, "id_str"));
            articleMusic.setTitle(StringUtil.parseStr(music, "title"));
            articleMusic.setAuthorName(StringUtil.parseStr(music, "author"));

            articleMusic.setDuration(StringUtil.parseInt(music, "duration") * 1000);
            articleMusic.setIsCopyrighted(-1);
            articleMusic.setOriginal(StringUtil.booleanToInt(StringUtil.parseBoolean(music, "is_original")));

            try {
                JSONObject coverObject = music.getJSONObject("cover_thumb");
                JSONArray coverUrls = coverObject.getJSONArray("url_list");
                for (int j = 0; j < coverUrls.size(); j++) {
                    String u = coverUrls.getString(j);
                    if (u.contains("heic?")) {
                        continue;
                    }

                    articleMusic.setAuthorAvatar(u);
                }

                JSONObject playUrlObj = music.getJSONObject("play_url");
                articleMusic.setUrl(StringUtil.parseStr(playUrlObj, "uri"));
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }

        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

        return articleMusic;
    }

    public static List<String> parseHashTagAppV3(JSONObject note) {
        List<String> hashTags = new ArrayList<>();
        try {
            JSONArray textExtraList = note.getJSONArray("text_extra");
            for (int index = 0; index < textExtraList.size(); index++) {
                JSONObject extra = textExtraList.getJSONObject(index);
                hashTags.add(extra.getString("hashtag_name"));
            }
        } catch (Exception e) {

        }

        return hashTags;
    }

    public static List<TikHubArticleProduct> parseProductWeb(JSONObject item) {
        return parseProductAppV3(item);
    }

    public static List<TikHubArticleProduct> parseProductAppV3(JSONObject item) {
        JSONArray anchors = item.getJSONArray("anchors");
        List<TikHubArticleProduct> articleProductList = new ArrayList<>();
        try {
            if (anchors == null || anchors.size() == 0) {
                return articleProductList;
            }

            for(int index = 0; index < anchors.size(); index++) {
                try {
                    TikHubArticleProduct tikHubArticleProduct = new TikHubArticleProduct();

                    JSONObject anchor = anchors.getJSONObject(index);
                    String extra = anchor.getString("extra");

                    JSONArray extraArray = JSON.parseArray(extra);
                    JSONObject extraObject = extraArray.getJSONObject(0);
                    tikHubArticleProduct.setProductId(extraObject.getString("id"));
                    tikHubArticleProduct.setKeyword(StringUtil.parseStr(extraObject, "keyword"));
                    tikHubArticleProduct.setProductType(StringUtil.parseInt(extraObject, "type"));

                    String extraData = extraObject.getString("extra");
                    JSONObject extraInfo = JSONObject.parseObject(extraData);

                    JSONArray categories = extraInfo.getJSONArray("categories");
                    if (categories != null) {
                        tikHubArticleProduct.setCategories(JSONArray.toJSONString(categories));
                    }

                    tikHubArticleProduct.setTitle(StringUtil.parseStr(extraInfo, "title"));
                    tikHubArticleProduct.setCover(StringUtil.parseStr(extraInfo, "cover_url"));

                    List<String> images = new ArrayList<>();
                    JSONArray imgUrls = extraInfo.getJSONArray("img_url");
                    if (imgUrls != null) {
                        for (int i = 0; i < imgUrls.size(); i++) {
                            images.add(imgUrls.getString(i));
                        }
                    }

                    tikHubArticleProduct.setImageList(images);

                    tikHubArticleProduct.setMarketPrice(StringUtil.parseDouble(extraInfo, "market_price"));
                    tikHubArticleProduct.setPrice(StringUtil.parseDouble(extraInfo, "price"));
                    tikHubArticleProduct.setCurrency(StringUtil.parseStr(extraInfo, "currency"));

                    String url = "https://shop.tiktok.com/view/product/" + tikHubArticleProduct.getProductId();
                    tikHubArticleProduct.setUrl(url);
                    tikHubArticleProduct.setProductStatus(StringUtil.parseInt(extraInfo, "product_status"));
                    tikHubArticleProduct.setInShop(StringUtil.booleanToInt(StringUtil.parseBoolean(extraInfo, "in_shop")));

                    tikHubArticleProduct.setSellerId(StringUtil.parseStr(extraInfo, "seller_id"));
                    tikHubArticleProduct.setAdLabelName(StringUtil.parseStr(extraInfo, "ad_lable_name"));
                    tikHubArticleProduct.setPlatform(StringUtil.parseInt(extraInfo, "platform"));
                    tikHubArticleProduct.setBizType(StringUtil.parseInt(extraInfo, "biz_type"));

                    List<String> skuIdList = new ArrayList<>();
                    JSONArray skuArray = extraInfo.getJSONArray("sku");
                    if (skuArray != null && skuArray.size() > 0) {
                        for (int j = 0; j < skuArray.size(); j++) {
                            skuIdList.add(skuArray.getString(j));
                        }
                    }

                    tikHubArticleProduct.setSkuList(skuIdList);

                    articleProductList.add(tikHubArticleProduct);
                } catch (Exception e) {
                    System.out.println(e);
                }
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

        return articleProductList;
    }

}
