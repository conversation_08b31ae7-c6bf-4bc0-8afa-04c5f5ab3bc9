<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.AtTiktokAuthorWorkHistoryMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.AtTiktokAuthorWorkHistoryDO">
        <id column="id" property="id" />
        <result column="work_id" property="workId" />
        <result column="work_uuid" property="workUuid" />
        <result column="author_id" property="authorId" />
        <result column="unique_id" property="uniqueId" />
        <result column="sec_uid" property="secUid" />
        <result column="url" property="url" />
        <result column="category_type" property="categoryType" />
        <result column="thumbnail_link" property="thumbnailLink" />
        <result column="is_ad" property="isAd" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="hashtags" property="hashtags" />
        <result column="images" property="images" />
        <result column="publish_time" property="publishTime" />
        <result column="text_language" property="textLanguage" />
        <result column="location_ip" property="locationIp" />
        <result column="play_count" property="playCount" />
        <result column="like_count" property="likeCount" />
        <result column="comment_count" property="commentCount" />
        <result column="share_count" property="shareCount" />
        <result column="collect_count" property="collectCount" />
        <result column="video_id" property="videoId" />
        <result column="music_id" property="musicId" />
        <result column="record_day" property="recordDay" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, work_id, work_uuid, author_id, unique_id, sec_uid, url, category_type, thumbnail_link, is_ad, title, content, hashtags, images, publish_time, text_language, location_ip, play_count, like_count, comment_count, share_count, collect_count, video_id, music_id, record_day, creator, create_time, updater, update_time
    </sql>

  

  
</mapper>
