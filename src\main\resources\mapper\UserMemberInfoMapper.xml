<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.UserMemberInfoMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.UserMemberInfoDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="level" property="level" />
        <result column="max_author_count" property="maxAuthorCount" />
        <result column="max_video_days" property="maxVideoDays" />
        <result column="max_video_monitor_count" property="maxVideoMonitorCount" />
        <result column="live_monitor_duration" property="liveMonitorDuration" />
        <result column="extend" property="extend" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, user_id, level, max_author_count, max_video_days, max_video_monitor_count, live_monitor_duration, extend, start_time, end_time, creator, create_time, updater, update_time
    </sql>

  

  
</mapper>
