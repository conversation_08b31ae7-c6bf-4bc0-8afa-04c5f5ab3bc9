package com.bq.linkcore.client.tikhub.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/22 14:09
 * @className OneBoundXHSArticle
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TikHubArticle {

    /**
     * 发布账号ID
     */
    private String authorId;

    private String uniqueId;
    /**
     * 作者账号加密ID-可用于拼接主页
     * 对应secUid
     */
    private String secUid;

    /**
     * 作者昵称
     */
    private String authorName;

    /**
     * 作者头像
     */
    private String authorAvatar;

    /**
     * 发布者主页地址
     */
    private String authorUrl;


    private String authorDesc;

    /**
     * 作品唯一标识
     */
    private String workId;

    /**
     * 三方生成的一个作品对应的 唯一ID，不清楚唯一性
     */
    private String workUuid;

    /**
     * 作品链接
     */
    private String url;

    /**
     * categoryType: 作品分类
     * 100: 动画与漫画
     * 101: 表演
     * 102: 美容护理
     * 103: 游戏
     * 104: 喜剧
     * 105: 日常生活
     * 106: 家庭
     * 107: 情感关系
     * 108: 戏剧
     * 109: 穿搭
     * 110: 对口型
     * 111: 美食
     * 112: 运动
     * 113: 动物
     * 114: 社会
     * 115: 汽车
     * 116: 教育
     * 117: 健身和健康
     * 118: 科技
     * 119: 唱歌跳舞
     * 120: 全部
     */
    private Integer categoryType;

    /**
     * 第一帧图片链接，cover
     */
    private String thumbnailLink;

    /**
     * 图片集,多个用;隔开
     */
    private List<String> imgUrls;

    /**
     * 广告标识：表示当前视频是否为付费广告内容
     *
     * true：该视频是商业推广内容（带有"Sponsored"标签）
     * false：普通用户创作的有机内容
     */
    private Integer isAd;

    /**
     * 作品标题标题
     */
    private String title;

    /**
     * 话题
     */
    private List<String> hashTags;

    /**
     * 作品内容
     */
    private String content;

    /**
     * 发布时间
     */
    private Integer publishTime;

    private String textLanguage;

    /**
     * 发布地理位置
     */
    private String locationIp="";

    /**
     * 阅读量
     */
    private Integer playCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 转发数
     */
    private Integer shareCount;

    /**
     * 收藏数
     */
    private Integer collectCount;

    /**
     * 视频video
     */
    private TikHubArticleVideo video;

    /**
     * 背景音乐信息
     */
    private TikHubArticleMusic music;
}
