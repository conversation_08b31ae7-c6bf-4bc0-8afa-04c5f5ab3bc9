package com.bq.linkcore.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName: FeedBackConfig
 * @Description:
 * @author: lmy
 * @date: 2021年10月10日 11:07
 */
@Configuration
@Data
@RefreshScope
public class CommercialConfig {
    @Value("${commercial.default.authorCount:5}")
    private String defaultOAuthCount;

    // 一次时间
    @Value("${commercial.default.timeDenominator:1440}")
    private Integer timeDenominator;

    // 默认监控达人最大个数
    @Value("${commercial.default.maxAuthorCount:1}")
    private Integer maxAuthorCount;

    //默认会员天数
    @Value("${commercial.default.membershipDays:3}")
    private Integer membershipDays;

    //默认读取用户30天内创建的视频数据
    @Value("${commercial.default.videoDays:30}")
    private Integer videoDays;

    //默认读取视频评论数量
    @Value("${commercial.default.video.comment.init:100}")
    private Integer videoCommentCount;
}
