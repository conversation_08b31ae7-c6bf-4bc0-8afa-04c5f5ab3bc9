package com.bq.linkcore.controller;

import com.bq.data.base.bean.BaseEnterpriseUser;
import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.entity.UserDO;
import com.bq.linkcore.bean.vo.UserInfoVo;
import com.bq.linkcore.bean.vo.UserLoginReqVo;
import com.bq.linkcore.bean.vo.UserMessageReqVo;
import com.bq.linkcore.common.Constants;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ResponseMsg;
import com.bq.linkcore.common.ServiceException;
import com.bq.linkcore.services.IUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.regex.Pattern;

import static com.bq.linkcore.common.ResponseMsg.*;

@Api(value = "用户", tags = "用户接口")
@RestController
@Slf4j
@RequestMapping("/user")
public class UserController extends BaseController {

    @Autowired
    private IUserService userService;

    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "返回消息类", response = ResponseData.class)
    })
    @PostMapping(value = "/message")
    public ResponseData getMessageCode(@Valid @RequestBody UserMessageReqVo vo) {
        try {
            log.info("/user/message, vo={}", vo);

            // 检查邮箱格式
            if (!Pattern.matches(Constants.REG_EMAIL, vo.getEmail())) {
                return RD.fail(ERROR_INVALID_PHONE.getCode());
            }

            return userService.sendMessageByEmail(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return RD.fail(ResponseMsg.ERROR_PHONE_MESSAGE.getCode());
    }


    @ApiOperation(value = "用户登录", notes = "密码使用密文传输")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回信息", response = ResponseData.class)})
    @PostMapping(value = "/login")
    public ResponseData login(@Valid @RequestBody UserLoginReqVo vo) {
        try {
            log.info("/user/login, vo={}", vo);

            return userService.accountLogin(vo);

        } catch (ServiceException e) {
            log.error(e.getMessage(), e);
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RD.fail();
        }
    }

    @ApiOperation(value = "注册", notes = "邮箱注册")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回信息", response = ResponseData.class)})
    @PostMapping(value = "/register")
    public ResponseData register(@Valid @RequestBody UserLoginReqVo vo) {
        try {
            log.info("/user/register, vo={}", vo);

            boolean ret = userService.checkMessage(vo.getEmail(), vo.getType(), vo.getMessageCode());
            if (!ret) {
                return RD.fail(ERROR_INVALID_MESSAGE.getCode());
            }

            // 判断是否存在
            UserDO userDO = userService.queryTxUserByEmail(vo.getEmail());
            if (userDO != null) {
                return RD.fail(ResponseMsg.ERROR_USER_EXIST);
            }

            return userService.insertTxUser(vo);

        } catch (ServiceException e) {
            log.error(e.getMessage(), e);
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RD.fail();
        }
    }


    @ApiOperation(value = "查询用户", notes = "查询用户")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回信息", response = ResponseData.class)})
    @GetMapping(value = "/info")
    public ResponseData userinfo() {
        try {
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ResponseMsg.ERROR_UNKNOWN_TOKEN.getCode());
            }

            UserInfoVo userInfoVo = userService.queryTxUser(baseUser.getId());
            if (userInfoVo == null) {
                return RD.fail(ERROR_NOT_USER_INFO);
            }

            return RD.ok(userInfoVo);
        } catch (ServiceException e) {
            log.error(e.getMessage(), e);
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RD.fail();
        }
    }

}
