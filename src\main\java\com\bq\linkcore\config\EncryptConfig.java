package com.bq.linkcore.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * @ClassName: UploadFileConfig
 * @Description:
 * @author: lmy
 * @date: 2021年08月17日 4:57 下午
 */

@Configuration
@Slf4j
@Component
@Data
public class EncryptConfig {

    @Value("${encrypt.secretKey}")
    private String secretKey;

    @Value("${encrypt.salt}")
    private String salt;

    @Value("${encrypt.networkSecretKey}")
    private String networkSecretKey;

    @Value("${encrypt.networkSecretIv}")
    private String networkSecretIv;


}