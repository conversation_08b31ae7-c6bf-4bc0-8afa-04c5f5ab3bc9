package com.bq.linkcore.bean.vo;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel(description = "角色相关对象")
@Slf4j
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class RoleVo {

    /**
     * 自增长ID
     */
    private Long id;

    /**
     * 角色关联权限子集详情
     */
//    private List<PermissionVo> rolePermissionVoList;

    /**
     * 角色权限关联列表
     */
    private List<RolePermissionRelationVo> RolePermissionRelationVoList;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色类型：1=内置角色；0=非内置角色
     */
    private Integer roleType;

    /**
     * 角色说明
     */
    private String roleRemark;

    /**
     * 角色所属租户编码
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 删除标记：1=已删除，0=正常
     */
    private Integer isDel;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Long updateUser;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    @Setter
    @Getter
    public class RolePermissionRelationVo {
        /**
         * 关联主键
         */
        private String relateId;

        /**
         * 角色编码
         */
        private String roleCode;

        /**
         * 角色名称
         */
        private String roleName;

        /**
         * 权限编码
         */
        private String permiCode;

        /**
         * 权限名称
         */
        private String permiName;

        /**
         * 租户编码
         */
        private String tenantCode;
    }



}
