package com.bq.linkcore.client.tikhub.tiktok;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bq.linkcore.client.tikhub.TikHubHttpRequester;
import com.bq.linkcore.client.tikhub.models.TikHubArticle;
import com.bq.linkcore.client.tikhub.models.TikHubArticleComment;
import com.bq.linkcore.client.tikhub.models.TikHubArticleVideo;
import com.bq.linkcore.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/29 16:32
 * @className TikHubDouYinClient
 * @description
 */

@Slf4j
@Component
public class TikHubTiktokArticleRequester {
    @Autowired
    private TikHubHttpRequester tikHubHttpRequester;

    /**
     * 查询单个作品的详情数据
     *
     * @param workId
     * @return
     */
    public TikHubArticle queryArticleWebV1(String workId) {
        String url = "/api/v1/tiktok/web/fetch_post_detail?itemId=";
        url += workId;

        JSONObject object = tikHubHttpRequester.callGet(url);
        if (object == null) {
            object = tikHubHttpRequester.callGet(url);
        }

        if (object == null) {
            return null;
        }

        JSONObject data = object.getJSONObject("data");
        JSONObject itemInfo = data.getJSONObject("itemInfo");
        JSONObject item = itemInfo.getJSONObject("itemStruct");

        try {
            TikHubArticle tikHubArticle = new TikHubArticle();
            JSONObject author = item.getJSONObject("author");
            tikHubArticle.setAuthorId(StringUtil.parseStr(author, "id"));
            tikHubArticle.setUniqueId(StringUtil.parseStr(author, "uniqueId"));
            tikHubArticle.setSecUid(StringUtil.parseStr(author, "secUid"));
            tikHubArticle.setAuthorName(StringUtil.parseStr(author, "nickname"));
            tikHubArticle.setAuthorAvatar(StringUtil.parseStr(author, "avatarThumb"));
            tikHubArticle.setAuthorUrl("https://www.tiktok.com/@" + StringUtil.parseStr(author, "uniqueId"));
            tikHubArticle.setAuthorDesc(StringUtil.parseStr(author, "signature"));

            tikHubArticle.setWorkId(StringUtil.parseStr(item, "id"));
            tikHubArticle.setWorkUuid(StringUtil.parseStr(item, "id"));
            String workUrl = "https://www.tiktok.com/@" + StringUtil.parseStr(author, "uniqueId") + "/video/" + tikHubArticle.getWorkId();
            tikHubArticle.setUrl(workUrl);

            tikHubArticle.setCategoryType(StringUtil.parseInt(item, "CategoryType"));
            tikHubArticle.setIsAd(StringUtil.booleanToInt(StringUtil.parseBoolean(item, "isAd")));
            tikHubArticle.setTitle(StringUtil.parseStr(item, "desc"));
            tikHubArticle.setContent(StringUtil.parseStr(item, "desc"));
            tikHubArticle.setHashTags(TiktokDataParser.parseHashTag(item));

            tikHubArticle.setPublishTime(StringUtil.parseInt(item, "createTime"));
            tikHubArticle.setTextLanguage(StringUtil.parseStr(item, "textLanguage"));

            JSONObject stats = item.getJSONObject("stats");
            tikHubArticle.setPlayCount(StringUtil.parseInt(stats, "playCount"));
            tikHubArticle.setCollectCount(StringUtil.parseInt(stats, "collectCount"));
            tikHubArticle.setCommentCount(StringUtil.parseInt(stats, "commentCount"));
            tikHubArticle.setLikeCount(StringUtil.parseInt(stats, "diggCount"));
            tikHubArticle.setShareCount(StringUtil.parseInt(stats, "shareCount"));

            TikHubArticleVideo tikHubArticleVideo = TiktokDataParser.parseVideoWebV1(item);
            tikHubArticle.setVideo(tikHubArticleVideo);
            tikHubArticle.setMusic(TiktokDataParser.parseMusicWebV1(item));
            tikHubArticle.setThumbnailLink(tikHubArticleVideo.getCover());

            return tikHubArticle;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }


    /**
     * 查询单个作品的详情数据
     *
     * @param workId 作品ID
     * @param count  作品数字
     * @return
     */
    public List<TikHubArticleComment> queryArticleCommentWebV1(String workId, Integer count) {
        List<TikHubArticleComment> tikHubArticleComments = new ArrayList<>();

        Integer cursor = 0;
        Integer pageSize = 100;
        String region = "";
        Boolean hasMore = true;

        while (hasMore) {
            try {
                JSONObject object = queryArticleCommentPageByWebV1(workId, cursor, pageSize, region);
                JSONObject data = object.getJSONObject("data");
                cursor = data.getInteger("cursor");
                hasMore = data.getInteger("has_more").equals(1);

                JSONArray comments = data.getJSONArray("comments");
                for (int index = 0; index < comments.size(); index++) {
                    try {
                        JSONObject comment = comments.getJSONObject(index);
                        JSONObject user = comment.getJSONObject("user");

                        TikHubArticleComment tikHubArticleComment = TikHubArticleComment.builder()
                                .commentId(StringUtil.parseStr(comment, "cid"))
                                .content(StringUtil.parseStr(comment, "text"))
                                .comment_language(StringUtil.parseStr(comment, "comment_language"))
                                .publishTime(StringUtil.parseInt(comment, "create_time"))
                                .likeCount(StringUtil.parseInt(comment, "digg_count"))
                                .commenterId(StringUtil.parseStr(user, "uid"))
                                .commenterName(StringUtil.parseStr(user, "nickname"))
                                .commenterUniqueId(StringUtil.parseStr(user, "unique_id"))
                                .commenterSecUid(StringUtil.parseStr(user, "sec_uid"))
                                .commenterDesc("")  // user.signature（空字符串）
                                .commenterRegion("")  // user.region
                                .replayCommentTotal(StringUtil.parseInt(comment, "reply_comment_total"))
                                .build();

                        try {
                            JSONObject avatar = user.getJSONObject("avatar_thumb");
                            JSONArray urlList = avatar.getJSONArray("url_list");
                            tikHubArticleComment.setCommenterAvatar(urlList.getString(0));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }

                        tikHubArticleComments.add(tikHubArticleComment);

                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }

                if (comments.size() == 0 || comments.size() >= count) {
                    break;
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }

        return tikHubArticleComments;
    }

    private JSONObject queryArticleCommentPageByWebV1(String workId,
                                                      Integer cursor,
                                                      Integer pageSize,
                                                      String region) {
        JSONObject object = queryArticleCommentPageInnerByWebV1(workId, cursor, pageSize, region);
        if (object == null) {
            object = queryArticleCommentPageInnerByWebV1(workId, cursor, pageSize, region);
        }

        if (object == null) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            object = queryArticleCommentPageInnerByWebV1(workId, cursor, pageSize, region);
        }

        return object;
    }


    private JSONObject queryArticleCommentPageInnerByWebV1(String workId,
                                                           Integer cursor,
                                                           Integer pageSize,
                                                           String region) {
        String url = "/api/v1/tiktok/web/fetch_post_comment?";
        url += "aweme_id=" + workId;
        url += "&cursor=" + cursor;
        url += "&count=" + pageSize;
        url += "&current_region=" + region;

        return tikHubHttpRequester.callGet(url);
    }


    /**
     * 查询单个作品的详情数据
     *
     * @param workId
     * @return
     */
    public TikHubArticle queryArticleAppV3(String workId) {
        String url = "/api/v1/tiktok/app/v3/fetch_one_video?aweme_id=";
        url += workId;

        JSONObject object = tikHubHttpRequester.callGet(url);
        if (object == null) {
            object = tikHubHttpRequester.callGet(url);
        }

        if (object == null) {
            return null;
        }

        JSONObject data = object.getJSONObject("data");
        JSONArray itemList = data.getJSONArray("aweme_details");
        JSONObject item = itemList.getJSONObject(0);

        try {
            TikHubArticle tikHubArticle = new TikHubArticle();
            JSONObject author = item.getJSONObject("author");
            tikHubArticle.setAuthorId(StringUtil.parseStr(author, "uid"));
            tikHubArticle.setUniqueId(StringUtil.parseStr(author, "unique_id"));
            tikHubArticle.setSecUid(StringUtil.parseStr(author, "sec_uid"));
            tikHubArticle.setAuthorName(StringUtil.parseStr(author, "nickname"));
            try {
                JSONObject avatar = author.getJSONObject("avatar_thumb");
                JSONArray urlList = avatar.getJSONArray("url_list");

                tikHubArticle.setAuthorAvatar(urlList.getString(0));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            tikHubArticle.setAuthorUrl("https://www.tiktok.com/@" + StringUtil.parseStr(author, "unique_id"));
            tikHubArticle.setAuthorDesc(StringUtil.parseStr(author, "signature"));

            tikHubArticle.setWorkId(StringUtil.parseStr(item, "aweme_id"));
            tikHubArticle.setWorkUuid(StringUtil.parseStr(item, "aweme_id"));
            String workUrl = "https://www.tiktok.com/@" + StringUtil.parseStr(author, "unique_id") + "/video/" + tikHubArticle.getWorkId();
            tikHubArticle.setUrl(workUrl);

            tikHubArticle.setCategoryType(-1);
            tikHubArticle.setIsAd(StringUtil.booleanToInt(StringUtil.parseBoolean(item, "is_ads")));
            tikHubArticle.setTitle(StringUtil.parseStr(item, "desc"));
            tikHubArticle.setContent(StringUtil.parseStr(item, "desc"));
            tikHubArticle.setHashTags(TiktokDataParser.parseHashTagAppV3(item));

            tikHubArticle.setPublishTime(StringUtil.parseInt(item, "create_time"));
            tikHubArticle.setTextLanguage(StringUtil.parseStr(item, "desc_language"));

            JSONObject stats = item.getJSONObject("statistics");
            tikHubArticle.setPlayCount(StringUtil.parseInt(stats, "play_count"));
            tikHubArticle.setCollectCount(StringUtil.parseInt(stats, "collect_count"));
            tikHubArticle.setCommentCount(StringUtil.parseInt(stats, "comment_count"));
            tikHubArticle.setLikeCount(StringUtil.parseInt(stats, "digg_count"));
            tikHubArticle.setShareCount(StringUtil.parseInt(stats, "share_count"));

            TikHubArticleVideo tikHubArticleVideo = TiktokDataParser.parseVideoAppV3(item);
            tikHubArticle.setVideo(tikHubArticleVideo);
            tikHubArticle.setMusic(TiktokDataParser.parseMusicAppV3(item));
            tikHubArticle.setThumbnailLink(tikHubArticleVideo.getCover());

            return tikHubArticle;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }


    /**
     * 查询单个作品的详情数据
     *
     * @param workId
     * @param count
     * @return
     */
    public List<TikHubArticleComment> queryArticleCommentAppV3(String workId, Integer count) {
        List<TikHubArticleComment> tikHubArticleComments = new ArrayList<>();

        Integer cursor = 0;
        Integer pageSize = 100;
        Boolean hasMore = true;

        while (hasMore) {
            try {
                JSONObject object = queryArticleCommentPageByAppV3(workId, cursor, pageSize);
                JSONObject data = object.getJSONObject("data");
                cursor = data.getInteger("cursor");
                hasMore = data.getInteger("has_more").equals(1);

                JSONArray comments = data.getJSONArray("comments");
                for (int index = 0; index < comments.size(); index++) {
                    try {
                        JSONObject comment = comments.getJSONObject(index);
                        JSONObject user = comment.getJSONObject("user");

                        TikHubArticleComment tikHubArticleComment = TikHubArticleComment.builder()
                                .commentId(StringUtil.parseStr(comment, "cid"))
                                .content(StringUtil.parseStr(comment, "text"))
                                .comment_language(StringUtil.parseStr(comment, "comment_language"))
                                .publishTime(StringUtil.parseInt(comment, "create_time"))
                                .likeCount(StringUtil.parseInt(comment, "digg_count"))
                                .replayCommentTotal(StringUtil.parseInt(comment, "reply_comment_total"))
                                .commenterId(StringUtil.parseStr(user, "uid"))
                                .commenterName(StringUtil.parseStr(user, "nickname"))
                                .commenterUniqueId(StringUtil.parseStr(user, "unique_id"))
                                .commenterSecUid(StringUtil.parseStr(user, "sec_uid"))
                                .commenterDesc(StringUtil.parseStr(user, "signature"))
                                .commenterRegion(StringUtil.parseStr(user, "region"))
                                .build();

                        try {
                            JSONObject avatar = user.getJSONObject("avatar_thumb");
                            JSONArray urlList = avatar.getJSONArray("url_list");
                            tikHubArticleComment.setCommenterAvatar(urlList.getString(0));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }

                        tikHubArticleComments.add(tikHubArticleComment);

                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }

                if (comments.size() == 0 || comments.size() >= count) {
                    break;
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }

        return tikHubArticleComments;
    }


    private JSONObject queryArticleCommentPageByAppV3(String workId,
                                                      Integer cursor,
                                                      Integer pageSize) {
        JSONObject object = queryArticleCommentPageInnerByAppV3(workId, cursor, pageSize);
        if (object == null) {
            object = queryArticleCommentPageInnerByAppV3(workId, cursor, pageSize);
        }

        if (object == null) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            object = queryArticleCommentPageInnerByAppV3(workId, cursor, pageSize);
        }

        return object;
    }


    private JSONObject queryArticleCommentPageInnerByAppV3(String workId,
                                                           Integer cursor,
                                                           Integer pageSize) {
        String url = "/api/v1/tiktok/app/v3/fetch_video_comments?";
        url += "aweme_id=" + workId;
        url += "&cursor=" + cursor;
        url += "&count=" + pageSize;

        return tikHubHttpRequester.callGet(url);
    }


}
