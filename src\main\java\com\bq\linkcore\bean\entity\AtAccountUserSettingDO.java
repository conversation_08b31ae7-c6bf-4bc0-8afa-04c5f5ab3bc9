package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-15 15:33:42
 * @ClassName: AtAccountUserSettingDO
 * @Description: 账户监控-用户账户设置
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("at_account_user_setting")
public class AtAccountUserSettingDO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 账号的唯一ID
     */
    private String atUniqueId;

    /**
     * 账户主页
     */
    private String homeUrl;

    /**
     * 备注名称
     */
    private String nickname;

    /**
     * 平台 dy、tiktok
     */
    private String platform;

    /**
     * 创建者
     */
    private Long creator;

    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;

    /**
     * 数据更新人
     */
    private Long updater;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除（0未删除，1已删除）
     */
    private Integer isDel;




}
