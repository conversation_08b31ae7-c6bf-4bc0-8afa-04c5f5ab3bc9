package com.bq.linkcore.bean.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("视频信息")
public class WorkVideoInfoVo {
    @ApiModelProperty(value = "视频ID")
    private String videoId;
    @ApiModelProperty(value = "视频质量标签")
    private String videoQuality;
    @ApiModelProperty(value = "视频质量评分(VQScore)")
    private String vqScore;
    @ApiModelProperty(value = "视频比特率(bps)")
    private Long bitrate;
    @ApiModelProperty(value = "编解码器类型(H.264/AV1等)")
    private String codecType;
    @ApiModelProperty(value = "分辨率(540p/1080p等)")
    private String definition;
    @ApiModelProperty(value = "视频时长(毫秒)")
    private Long duration;
    @ApiModelProperty(value = "视频大小(字节)")
    private Long dataSize;
    @ApiModelProperty(value = "视频高度(像素)")
    private Integer height;
    @ApiModelProperty(value = "视频宽度(像素)")
    private Integer width;
    @ApiModelProperty(value = "视频封面URL")
    private String cover;
    @ApiModelProperty(value = "视频页面URL")
    private String url;
}