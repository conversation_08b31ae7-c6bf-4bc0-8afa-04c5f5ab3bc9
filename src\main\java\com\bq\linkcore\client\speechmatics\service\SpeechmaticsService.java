package com.bq.linkcore.client.speechmatics.service;

import com.bq.linkcore.client.speechmatics.SpeechmaticsClient;
import com.bq.linkcore.client.speechmatics.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Speechmatics 服务类
 */
@Slf4j
@Service
public class SpeechmaticsService {
    
    @Autowired
    private SpeechmaticsClient speechmaticsClient;
    
    /**
     * 提交转录任务
     * 
     * @param fileUrl 音频/视频文件URL
     * @return 任务ID，如果失败返回null
     */
    public String submitTranscriptionJob(String fileUrl) {
        return submitTranscriptionJob(fileUrl, "en");
    }
    
    /**
     * 提交转录任务（指定语言）
     * 
     * @param fileUrl 音频/视频文件URL
     * @param language 语言代码
     * @return 任务ID，如果失败返回null
     */
    public String submitTranscriptionJob(String fileUrl, String language) {
        try {
            log.info("提交转录任务, fileUrl: {}, language: {}", fileUrl, language);
            
            SpeechmaticsResponse<CreateJobModel> response = speechmaticsClient.createTranscriptionJob(fileUrl, language);
            
            if (response.isSuccess() && response.getData() != null) {
                String jobId = response.getData().getId();
                log.info("转录任务提交成功, jobId: {}", jobId);
                return jobId;
            } else {
                log.error("转录任务提交失败: {}", response.getErrorMessage());
                return null;
            }
            
        } catch (Exception e) {
            log.error("提交转录任务异常, fileUrl: {}, language: {}", fileUrl, language, e);
            return null;
        }
    }
    
    /**
     * 检查任务状态
     * 
     * @param jobId 任务ID
     * @return 任务状态，如果失败返回null
     */
    public JobStatus checkJobStatus(String jobId) {
        try {
            log.info("检查任务状态, jobId: {}", jobId);
            
            SpeechmaticsResponse<CheckJsonModel> response = speechmaticsClient.checkJobStatus(jobId);
            
            if (response.isSuccess() && response.getData() != null && response.getData().getJob() != null) {
                String statusCode = response.getData().getJob().getStatus();
                JobStatus status = JobStatus.fromCode(statusCode);
                log.info("任务状态检查成功, jobId: {}, status: {}", jobId, status);
                return status;
            } else {
                log.error("任务状态检查失败, jobId: {}, error: {}", jobId, response.getErrorMessage());
                return null;
            }
            
        } catch (Exception e) {
            log.error("检查任务状态异常, jobId: {}", jobId, e);
            return null;
        }
    }
    
    /**
     * 获取转录结果
     * 
     * @param jobId 任务ID
     * @return 转录结果，如果失败返回null
     */
    public String getTranscriptionResult(String jobId) {
        try {
            log.info("获取转录结果, jobId: {}", jobId);
            
            // 先检查任务状态
            JobStatus status = checkJobStatus(jobId);
            if (status == null) {
                log.error("无法获取任务状态, jobId: {}", jobId);
                return null;
            }
            
            if (!status.isCompleted()) {
                log.warn("任务尚未完成, jobId: {}, status: {}", jobId, status);
                return null;
            }
            
            // 获取转录结果
            SpeechmaticsResponse<String> response = speechmaticsClient.getTranscriptionResult(jobId);
            
            if (response.isSuccess() && response.getData() != null) {
                log.info("转录结果获取成功, jobId: {}", jobId);
                return response.getData();
            } else {
                log.error("转录结果获取失败, jobId: {}, error: {}", jobId, response.getErrorMessage());
                return null;
            }
            
        } catch (Exception e) {
            log.error("获取转录结果异常, jobId: {}", jobId, e);
            return null;
        }
    }
    
    /**
     * 等待任务完成并获取结果
     * 
     * @param jobId 任务ID
     * @param maxWaitTimeMs 最大等待时间（毫秒）
     * @param checkIntervalMs 检查间隔（毫秒）
     * @return 转录结果，如果失败或超时返回null
     */
    public String waitForResultAndGet(String jobId, long maxWaitTimeMs, long checkIntervalMs) {
        try {
            log.info("等待任务完成, jobId: {}, maxWaitTime: {}ms, checkInterval: {}ms", 
                    jobId, maxWaitTimeMs, checkIntervalMs);
            
            long startTime = System.currentTimeMillis();
            
            while (System.currentTimeMillis() - startTime < maxWaitTimeMs) {
                JobStatus status = checkJobStatus(jobId);
                
                if (status == null) {
                    log.error("无法获取任务状态, jobId: {}", jobId);
                    return null;
                }
                
                if (status.isCompleted()) {
                    log.info("任务已完成, jobId: {}", jobId);
                    return getTranscriptionResult(jobId);
                }
                
                if (status.isFailed()) {
                    log.error("任务失败, jobId: {}, status: {}", jobId, status);
                    return null;
                }
                
                // 等待一段时间后再次检查
                Thread.sleep(checkIntervalMs);
            }
            
            log.warn("等待任务完成超时, jobId: {}", jobId);
            return null;
            
        } catch (InterruptedException e) {
            log.error("等待任务完成被中断, jobId: {}", jobId, e);
            Thread.currentThread().interrupt();
            return null;
        } catch (Exception e) {
            log.error("等待任务完成异常, jobId: {}", jobId, e);
            return null;
        }
    }
    
    /**
     * 删除任务
     * 
     * @param jobId 任务ID
     * @return 是否删除成功
     */
    public boolean deleteJob(String jobId) {
        try {
            log.info("删除任务, jobId: {}", jobId);
            
            SpeechmaticsResponse<String> response = speechmaticsClient.deleteJob(jobId);
            
            if (response.isSuccess()) {
                log.info("任务删除成功, jobId: {}", jobId);
                return true;
            } else {
                log.error("任务删除失败, jobId: {}, error: {}", jobId, response.getErrorMessage());
                return false;
            }
            
        } catch (Exception e) {
            log.error("删除任务异常, jobId: {}", jobId, e);
            return false;
        }
    }
}
