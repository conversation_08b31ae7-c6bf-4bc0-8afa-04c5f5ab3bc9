package com.bq.linkcore.client.tikhub.tiktok;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bq.linkcore.client.tikhub.TikHubHttpRequester;
import com.bq.linkcore.client.tikhub.models.TiktokHashTagModel;
import com.bq.linkcore.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/29 16:32
 * @className TikHubDouYinClient
 * @description
 */

@Slf4j
@Component
public class TikHubTiktokAdsRequester {
    @Autowired
    private TikHubHttpRequester tikHubHttpRequester;

    /**
     * 获取TikTok广告中的热门标签排行榜，了解当前流行的话题标签
     * 分析标签的使用量、观看量等数据，发现潜力标签
     * 帮助广告主选择合适的标签，提升广告曝光和互动率
     * <p>
     * period: 时间范围（天），如7、30、120天
     * country: 国家代码，如US、UK、JP等
     * sort: 排序方式，"popular"=热门，"new"=最新
     * industryId: 行业ID，留空返回所有行业
     * filter: 筛选条件，"new_on_board"=新上榜标签
     */
    public List<TiktokHashTagModel> queryHashTag(String country, String sort, String industryId,
                                                 String filter, Integer period, Integer count) {
        List<TiktokHashTagModel> hashTagModelList = new ArrayList<>();

        Integer pageNo = 1;
        Integer pageSize = 20;
        Boolean hasMore = true;
        while (hasMore) {
            JSONObject object = queryHashTagPage(pageNo, pageSize, filter, period, country, sort, industryId);
            JSONObject data = object.getJSONObject("data");
            data = object.getJSONObject("data");

            JSONArray hashTagList = data.getJSONArray("list");
            JSONObject pagination = data.getJSONObject("pagination");
            if (hashTagList.size() == 0) {
                break;
            }

            for (int index = 0; index < hashTagList.size(); index++) {
                TiktokHashTagModel hashTagModel = new TiktokHashTagModel();
                JSONObject hashTag = hashTagList.getJSONObject(index);

                try {
                    hashTagModel.setHashtagId(StringUtil.parseStr(hashTag, "hashtag_id"));
                    hashTagModel.setHashtagName(StringUtil.parseStr(hashTag, "hashtag_name"));
                    hashTagModel.setCountryInfo(hashTag.getJSONObject("country_info"));
                    hashTagModel.setIsPromoted(StringUtil.booleanToInt(StringUtil.parseBoolean(hashTag, "is_promoted")));
                    hashTagModel.setTrend(hashTag.getJSONArray("trend"));
                    hashTagModel.setCreators(hashTag.getJSONArray("creators"));
                    hashTagModel.setPublishCnt(StringUtil.parseLong(hashTag, "publish_cnt"));
                    hashTagModel.setVideoViews(StringUtil.parseLong(hashTag, "video_views"));
                    hashTagModel.setRank(StringUtil.parseInt(hashTag, "rank"));
                    hashTagModel.setRankDiff(StringUtil.parseInt(hashTag, "rank_diff"));
                    hashTagModel.setRankDiffType(StringUtil.parseInt(hashTag, "rank_diff_type"));

                    hashTagModelList.add(hashTagModel);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }

            hasMore = pagination.getBoolean("has_more");
            if (hashTagModelList.size() >= count) {
                break;
            }

            pageNo += 1;
        }

        return hashTagModelList;
    }

    /**
     * 获取TikTok广告中的热门标签排行榜，了解当前流行的话题标签
     * 分析标签的使用量、观看量等数据，发现潜力标签
     * 帮助广告主选择合适的标签，提升广告曝光和互动率
     *
     * page: 页码，默认1
     * limit: 每页数量，默认20
     * period: 时间范围（天），如7、30、120天
     * country_code: 国家代码，如US、UK、JP等
     * sort_by: 排序方式，"popular"=热门，"new"=最新
     * industry_id: 行业ID，留空返回所有行业
     * filter_by: 筛选条件，"new_on_board"=新上榜标签
     */
    public JSONObject queryHashTagPage(Integer page, Integer pageSize, String filter,
                                       Integer period, String country, String sort, String industryId) {
        String url = "/api/v1/tiktok/ads/get_hashtag_list?";
        url += "page" + page;
        url += "limit" + pageSize;
        url += "period" + period;
        url += "country_code" + country;
        url += "sort_by" + sort;
        url += "industry_id" + industryId;
        url += "filter_by" + filter;

        JSONObject object = tikHubHttpRequester.callGet(url);
        if (object == null) {
            object = tikHubHttpRequester.callGet(url);
        }

        return object;
    }
}
