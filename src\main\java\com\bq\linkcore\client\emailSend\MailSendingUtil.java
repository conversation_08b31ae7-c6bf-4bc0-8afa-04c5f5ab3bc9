package com.bq.linkcore.client.emailSend;

import com.bq.linkcore.config.MailSendingConfig;
import com.sun.mail.util.MailSSLSocketFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.GeneralSecurityException;
import java.util.Date;
import java.util.Properties;

/**
 * @ClassName: EmailSendingUtil
 * @Description:
 * @author: rendong.ck@naturobot
 * @date: 2021年10月10日 11:07 上午
 */
@Component
@Slf4j
public class MailSendingUtil {

    static class MyAuthenricator extends Authenticator{
        String u = null;
        String p = null;
        public MyAuthenricator(String u,String p){
            this.u=u;
            this.p=p;
        }
        @Override
        protected PasswordAuthentication getPasswordAuthentication() {
            return new PasswordAuthentication(u,p);
        }
    }


    public static void send(MailSendingConfig mailSendingConfig, String recipient, String title, String content, String attachment){
        log.info("start send email address is {} Thread : {}", recipient,Thread.currentThread().getName());
        Properties prop = new Properties();
        //协议
        prop.setProperty("mail.transport.protocol", mailSendingConfig.getProtocol());
        //服务器
        prop.setProperty("mail.smtp.host", mailSendingConfig.getHost());
        //端口
        prop.setProperty("mail.smtp.port", mailSendingConfig.getPort());
        //使用smtp身份验证
        prop.setProperty("mail.smtp.auth", "true");
        //使用SSL，企业邮箱必需！
        //开启安全协议
        MailSSLSocketFactory sf = null;
        try {
            sf = new MailSSLSocketFactory();
            sf.setTrustAllHosts(true);
        } catch (GeneralSecurityException e1) {
            e1.printStackTrace();
        }
        prop.put("mail.smtp.ssl.enable", "true");
        prop.put("mail.smtp.ssl.socketFactory", sf);

        Session session = Session.getDefaultInstance(prop, new MyAuthenricator(mailSendingConfig.getAccount(), mailSendingConfig.getPassword()));
//        session.setDebug(true);
        MimeMessage mimeMessage = new MimeMessage(session);
        try {
            //发件人
            //能够设置发件人的别名
            mimeMessage.setFrom(new InternetAddress(mailSendingConfig.getAccount(),"官网"));
            //mimeMessage.setFrom(new InternetAddress(account));    //若是不须要就省略
            //收件人
            mimeMessage.addRecipient(Message.RecipientType.TO, new InternetAddress(recipient));
            //主题
            mimeMessage.setSubject(title);
            //时间
            mimeMessage.setSentDate(new Date());
            //容器类，能够包含多个MimeBodyPart对象
            Multipart mp = new MimeMultipart();

            //MimeBodyPart能够包装文本，图片，附件
            MimeBodyPart body = new MimeBodyPart();
            //HTML正文
            body.setContent(content, "text/html; charset=UTF-8");
            mp.addBodyPart(body);

            //添加图片&附件
            if(attachment != null) {
                body = new MimeBodyPart();
                body.attachFile(attachment);
                mp.addBodyPart(body);
            }
            //设置邮件内容
            mimeMessage.setContent(mp);
            //仅仅发送文本
            //mimeMessage.setText(content);
            mimeMessage.saveChanges();
            Transport.send(mimeMessage);
        } catch (MessagingException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}