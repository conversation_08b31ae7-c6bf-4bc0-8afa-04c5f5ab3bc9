<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.UserRoleRelMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.UserRoleRelDO">
        <id column="id" property="id" />
        <result column="role_code" property="roleCode" />
        <result column="user_id" property="userId" />
        <result column="tenant_code" property="tenantCode" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, role_code, user_id, tenant_code, creator, create_time, updater, update_time
    </sql>

  

  
</mapper>
