<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.TiktokArticleVideosMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.TiktokArticleVideosDO">
        <id column="id" property="id" />
        <result column="video_id" property="videoId" />
        <result column="video_quality" property="videoQuality" />
        <result column="vq_score" property="vqScore" />
        <result column="bitrate" property="bitrate" />
        <result column="codec_type" property="codecType" />
        <result column="definition" property="definition" />
        <result column="duration" property="duration" />
        <result column="data_size" property="dataSize" />
        <result column="height" property="height" />
        <result column="width" property="width" />
        <result column="cover" property="cover" />
        <result column="url" property="url" />
    </resultMap>
    <sql id="Base_Column_List">
        id, video_id, video_quality, vq_score, bitrate, codec_type, definition, duration, data_size, height, width, cover, url
    </sql>

  

  
</mapper>
