package com.bq.linkcore.services.impl;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.entity.AtAccountUserSettingDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorPoolDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorWorkRecordDO;
import com.bq.linkcore.bean.entity.AuthorDisassemblyTaskRecordDO;
import com.bq.linkcore.bean.entity.TiktokArticleVideosDO;
import com.bq.linkcore.bean.entity.VideoTranscriptsTaskDO;
import com.bq.linkcore.bean.vo.DisassemblyResultVo;
import com.bq.linkcore.bean.vo.TranscriptVideoReqVo;
import com.bq.linkcore.bean.vo.TranscriptsRespVo;
import com.bq.linkcore.biz.AIActionBiz;
import com.bq.linkcore.biz.AtTtAuthorPoolBiz;
import com.bq.linkcore.biz.AtTtAuthorWorkBiz;
import com.bq.linkcore.biz.AtUserAccountBiz;
import com.bq.linkcore.client.speechmatics.model.JobStatus;
import com.bq.linkcore.client.speechmatics.service.SpeechmaticsService;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ResponseMsg;
import com.bq.linkcore.services.IAIActionService;
import com.bq.linkcore.services.impl.aiAction.ReportGenerator;
import com.bq.linkcore.services.impl.aiAction.models.CompleteReportVo;
import com.bq.linkcore.services.pool.ThreadRejectPolicy;
import com.bq.linkcore.services.pool.WorkThreadFactory;
import com.bq.linkcore.services.pool.WorkThreadPool;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.UUID;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class AIActionServiceImpl implements IAIActionService {

    @Resource
    private AIActionBiz aiActionBiz;

    @Resource
    private SpeechmaticsService speechmaticsService;

    @Resource
    private AtTtAuthorWorkBiz atTtAuthorWorkBiz;

    @Resource
    private AtUserAccountBiz atUserAccountBiz;

    @Resource
    private AtTtAuthorPoolBiz atTtAuthorPoolBiz;

    @Resource
    private ReportGenerator reportGenerator;

    @Resource
    private Gson gson;

    private final static String AI_THREAD_TAG = "ai-action";

    private final WorkThreadPool aiActionThreadPool = new WorkThreadPool(
            2,
            8,
            2L,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(5000),
            new WorkThreadFactory(AI_THREAD_TAG),
            new ThreadRejectPolicy(AI_THREAD_TAG)
    );


    @Override
    public ResponseData addTxVideoTranscriptsTask(Long userId, TranscriptVideoReqVo vo) {
        try {
            log.info("开始添加视频转写任务, userId: {}, vo: {}", userId, vo);

            // 2. 校验用户是否有权限查看该账户
            AtAccountUserSettingDO atAccountUserSettingDO
                    = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, vo.getAtUniqueId());
            if (atAccountUserSettingDO == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, vo.getAtUniqueId());
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            // 3. 校验作品是否存在
            AtTiktokAuthorWorkRecordDO workRecordDO
                    = atTtAuthorWorkBiz.queryAuthorWorkRecordByWorkIdAndUniqueId(vo.getAtUniqueId(), vo.getWorkId());
            if (workRecordDO == null) {
                log.warn("作品不存在, workId: {}, atUniqueId: {}", vo.getWorkId(), vo.getAtUniqueId());
                return RD.fail(ResponseMsg.ERROR_WK_NOT_EXIST_WORK);
            }

            // 4. 检查是否已存在转写任务
            VideoTranscriptsTaskDO existingTask = aiActionBiz.queryVideoTranscriptsTaskByWorkId(vo.getWorkId());
            if (existingTask != null) {
                log.info("作品已存在转写任务, workId: {}, taskId: {}", vo.getWorkId(), existingTask.getTaskId());
                TranscriptsRespVo respVo = new TranscriptsRespVo();
                respVo.setTaskId(existingTask.getTaskId());
                respVo.setStatus(existingTask.getStatus());
                respVo.setResult(existingTask.getResult());
                return RD.ok(respVo);
            }

            // 5. 获取视频详情信息
            String videoId = workRecordDO.getVideoId();
            if (!StringUtils.hasText(videoId)) {
                log.warn("作品缺少视频ID, workId: {}", vo.getWorkId());
                return RD.fail(ResponseMsg.FAIL.getCode(), "作品缺少视频信息");
            }

            TiktokArticleVideosDO videoDO = atTtAuthorWorkBiz.queryArticleVideo(videoId);
            if (videoDO == null) {
                log.warn("视频详情不存在, videoId: {}", videoId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "视频详情不存在");
            }

            // 6. 检查视频URL
            String videoUrl = videoDO.getUrl();
            if (!StringUtils.hasText(videoUrl)) {
                log.warn("视频URL为空, videoId: {}", videoId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "视频URL不可用");
            }

            String speechmaticsTaskId = speechmaticsService.submitTranscriptionJob(videoUrl, "en");
            if (!StringUtils.hasText(speechmaticsTaskId)) {
                log.error("创建Speechmatics转录任务失败, videoUrl: {}", videoUrl);
                return RD.fail(ResponseMsg.FAIL.getCode(), "创建转录任务失败");
            }

            // 8. 将任务信息插入数据库
            int insertResult = aiActionBiz.createVideoTranscriptsTask(
                    vo.getWorkId(),
                    videoUrl,
                    videoDO.getDuration(),
                    speechmaticsTaskId,
                    "tiktok"
            );

            if (insertResult <= 0) {
                log.error("插入转写任务记录失败, workId: {}, taskId: {}", vo.getWorkId(), speechmaticsTaskId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "保存任务信息失败");
            }

            // 9. 返回成功结果
            TranscriptsRespVo respVo = new TranscriptsRespVo();
            respVo.setTaskId(speechmaticsTaskId);
            respVo.setStatus(0); // 0-分析中
            respVo.setResult(null);

            log.info("成功创建视频转写任务, workId: {}, taskId: {}", vo.getWorkId(), speechmaticsTaskId);
            return RD.ok(respVo);

        } catch (Exception e) {
            log.error("添加视频转写任务异常, userId: {}, vo: {}", userId, vo, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "添加转写任务失败: " + e.getMessage());
        }
    }

    @Override
    public ResponseData<TranscriptsRespVo> queryTxTranscriptsTask(Long userId, TranscriptVideoReqVo vo) {
        try {
            log.info("开始查询视频转写任务, userId: {}, vo: {}", userId, vo);

            // 1. 参数校验
            if (userId == null || vo == null || !StringUtils.hasText(vo.getWorkId())) {
                log.warn("参数校验失败, userId: {}, vo: {}", userId, vo);
                return RD.fail(ResponseMsg.ERROR_INVALID_PARAMS);
            }

            // 2. 校验用户是否有权限查看该账户
            AtAccountUserSettingDO atAccountUserSettingDO
                    = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, vo.getAtUniqueId());
            if (atAccountUserSettingDO == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, vo.getAtUniqueId());
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            // 3. 查询转写任务记录
            VideoTranscriptsTaskDO taskDO = aiActionBiz.queryVideoTranscriptsTaskByWorkId(vo.getWorkId());
            if (taskDO == null) {
                log.warn("转写任务不存在, workId: {}", vo.getWorkId());
                return RD.fail(ResponseMsg.FAIL.getCode(), "转写任务不存在");
            }

            // 4. 如果任务正在进行中，检查Speechmatics任务状态
            if (taskDO.getStatus() == 0) { // 0-分析中
                try {
                    // 检查Speechmatics任务状态
                    JobStatus jobStatus =
                            speechmaticsService.checkJobStatus(taskDO.getTaskId());

                    if (jobStatus != null) {
                        if (jobStatus.isCompleted()) {
                            // 任务已完成，获取结果并更新数据库
                            String result = speechmaticsService.getTranscriptionResult(taskDO.getTaskId());
                            if (StringUtils.hasText(result)) {
                                aiActionBiz.updateTaskResult(taskDO.getTaskId(), result, result, 1); // 1-分析完成
                                taskDO.setStatus(1);
                                taskDO.setResult(result);
                                taskDO.setResultJson(result);
                            }
                        } else if (jobStatus.isFailed()) {
                            // 任务失败，更新状态
                            aiActionBiz.updateTaskError(taskDO.getTaskId(), "Speechmatics任务失败: " + jobStatus.getDescription());
                            taskDO.setStatus(2); // 2-分析失败
                            taskDO.setErrMsg("Speechmatics任务失败: " + jobStatus.getDescription());
                        }
                    }
                } catch (Exception e) {
                    log.warn("检查Speechmatics任务状态异常, taskId: {}", taskDO.getTaskId(), e);
                    // 不影响返回结果，继续返回当前状态
                }
            }

            // 5. 构建返回结果
            TranscriptsRespVo respVo = new TranscriptsRespVo();
            respVo.setTaskId(taskDO.getTaskId());
            respVo.setStatus(taskDO.getStatus());
            respVo.setResult(taskDO.getResult());

            log.info("成功查询视频转写任务, workId: {}, taskId: {}, status: {}",
                    vo.getWorkId(), taskDO.getTaskId(), taskDO.getStatus());
            return RD.ok(respVo);

        } catch (Exception e) {
            log.error("查询视频转写任务异常, userId: {}, vo: {}", userId, vo, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "查询转写任务失败: " + e.getMessage());
        }
    }

    @Override
    public ResponseData addTxAccountDisassemblyTask(Long userId, String atUniqueId) {
        try {
            log.info("开始添加账号拆解任务, userId: {}, atUniqueId: {}", userId, atUniqueId);

            if (userId == null || !StringUtils.hasText(atUniqueId)) {
                log.warn("参数校验失败, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.ERROR_INVALID_PARAMS);
            }

            AtAccountUserSettingDO atAccountUserSettingDO
                    = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
            if (atAccountUserSettingDO == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            AtTiktokAuthorPoolDO authorPoolDO = atTtAuthorPoolBiz.queryAuthorPoolByUniqueId(atUniqueId);
            if (authorPoolDO == null) {
                log.warn("账号不存在, atUniqueId: {}", atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "账号不存在");
            }

            // 4. 检查是否有正在进行中的拆解任务
            AuthorDisassemblyTaskRecordDO runningTask = aiActionBiz.queryRunningDisassemblyTaskByUniqueId(atUniqueId);
            if (runningTask != null) {
                log.info("账号已有正在进行中的拆解任务, atUniqueId: {}, taskId: {}, status: {}",
                        atUniqueId, runningTask.getTaskId(), runningTask.getTaskStatus());
                return RD.fail(ResponseMsg.FAIL.getCode(), "该账号已有正在进行中的拆解任务，请等待完成后再试");
            }

            // 5. 生成唯一的任务ID
            String taskId = generateTaskId(atUniqueId);

            // 6. 创建新的拆解任务
            int insertResult = aiActionBiz.createAuthorDisassemblyTask(
                    authorPoolDO.getAuthorId(),
                    atUniqueId,
                    taskId,
                    userId
            );

            if (insertResult <= 0) {
                log.error("插入拆解任务记录失败, atUniqueId: {}, userId: {}, taskId: {}", atUniqueId, userId, taskId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "创建任务失败");
            }

            // 7. 异步执行拆解任务
            aiActionThreadPool.addTask(new Runnable() {
                @Override
                public void run() {
                    executeDisassemblyTask(atUniqueId, userId, taskId);
                }
            });

            log.info("成功创建账号拆解任务, atUniqueId: {}, userId: {}, taskId: {}", atUniqueId, userId, taskId);
            return RD.ok("拆解任务已创建，正在后台执行，任务ID: " + taskId);

        } catch (Exception e) {
            log.error("添加账号拆解任务异常, userId: {}, atUniqueId: {}", userId, atUniqueId, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "添加拆解任务失败: " + e.getMessage());
        }
    }

    /**
     * 生成唯一的任务ID
     *
     * @param atUniqueId 账号唯一ID
     * @return 任务ID
     */
    private String generateTaskId(String atUniqueId) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return "TASK_" + atUniqueId + "_" + timestamp + "_" + uuid;
    }

    /**
     * 执行账号拆解任务
     */
    private void executeDisassemblyTask(String atUniqueId, Long userId, String taskId) {
        AuthorDisassemblyTaskRecordDO taskRecord = null;

        try {
            log.info("开始执行账号拆解任务, atUniqueId: {}, taskId: {}", atUniqueId, taskId);

            // 1. 根据taskId查询任务记录
            taskRecord = aiActionBiz.queryAuthorDisassemblyTaskByTaskId(taskId);
            if (taskRecord == null) {
                log.error("未找到拆解任务记录, atUniqueId: {}, taskId: {}", atUniqueId, taskId);
                return;
            }

            // 2. 更新任务状态为进行中（如果还是开始状态）
            if (taskRecord.getTaskStatus() == 0) {
                aiActionBiz.updateAuthorDisassemblyTaskStatus(taskRecord.getId(), 0, userId);
            }

            // 3. 调用报告生成器执行拆解
            CompleteReportVo completeReportVo = reportGenerator.generateCompleteReport(atUniqueId);

            // 4. 将结果转换为JSON字符串
            String resultJson = gson.toJson(completeReportVo);

            // 5. 根据报告状态确定任务状态
            Integer taskStatus = 1; // 默认完成
            if (completeReportVo != null && completeReportVo.getStatus() != null) {
                switch (completeReportVo.getStatus()) {
                    case SUCCESS:
                        taskStatus = 1; // 完成
                        break;
                    case PARTIAL_SUCCESS:
                        taskStatus = 1; // 部分成功也算完成
                        break;
                    case FAILED:
                        taskStatus = 2; // 失败
                        break;
                    default:
                        taskStatus = 1; // 默认完成
                        break;
                }
            }

            // 6. 更新任务结果
            aiActionBiz.updateAuthorDisassemblyTaskResult(taskRecord.getId(), resultJson, taskStatus, userId);

            log.info("账号拆解任务执行完成, atUniqueId: {}, taskId: {}, taskStatus: {}", atUniqueId, taskId, taskStatus);

        } catch (Exception e) {
            log.error("执行账号拆解任务异常, atUniqueId: {}, taskId: {}", atUniqueId, taskId, e);

            // 更新任务状态为失败
            if (taskRecord != null) {
                try {
                    aiActionBiz.updateAuthorDisassemblyTaskResult(
                            taskRecord.getId(),
                            "{\"error\":\"" + e.getMessage() + "\"}",
                            2, // 失败
                            userId
                    );
                } catch (Exception updateException) {
                    log.error("更新任务失败状态异常, atUniqueId: {}, taskId: {}", atUniqueId, taskId, updateException);
                }
            }
        }
    }

    /**
     * 获取任务状态描述
     */
    private String getTaskStatusDesc(Integer taskStatus) {
        if (taskStatus == null) {
            return "未知";
        }
        switch (taskStatus) {
            case 0:
                return "进行中";
            case 1:
                return "已完成";
            case 2:
                return "失败";
            default:
                return "未知";
        }
    }

    @Override
    public ResponseData queryTxAccountDisassemblyResult(Long userId, String atUniqueId) {
        try {
            log.info("开始查询账号拆解结果, userId: {}, atUniqueId: {}", userId, atUniqueId);

            // 1. 参数校验
            if (userId == null || !StringUtils.hasText(atUniqueId)) {
                log.warn("参数校验失败, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.ERROR_INVALID_PARAMS);
            }

            // 2. 校验用户是否有权限查看该账户
            AtAccountUserSettingDO atAccountUserSettingDO
                    = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
            if (atAccountUserSettingDO == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            // 3. 查询拆解任务记录
            AuthorDisassemblyTaskRecordDO taskRecord = aiActionBiz.queryAuthorDisassemblyTaskByUniqueId(atUniqueId);
            if (taskRecord == null) {
                log.warn("拆解任务不存在, atUniqueId: {}", atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "拆解任务不存在，请先创建任务");
            }

            // 4. 构建返回结果
            DisassemblyResultVo resultVo = DisassemblyResultVo.builder()
                    .taskId(taskRecord.getId())
                    .atUniqueId(atUniqueId)
                    .taskStatus(taskRecord.getTaskStatus())
                    .taskStatusDesc(getTaskStatusDesc(taskRecord.getTaskStatus()))
                    .createTime(taskRecord.getCreateTime())
                    .updateTime(taskRecord.getUpdateTime())
                    .build();

            // 5. 根据任务状态处理结果数据
            switch (taskRecord.getTaskStatus()) {
                case 1: // 任务已完成
                    if (StringUtils.hasText(taskRecord.getResJson())) {
                        try {
                            // 解析JSON结果
                            CompleteReportVo completeReportVo = gson.fromJson(taskRecord.getResJson(), CompleteReportVo.class);
                            resultVo.setReportData(completeReportVo);
                            resultVo.setHasResult(true);

                            // 设置详细的完成信息
                            if (completeReportVo != null && completeReportVo.getExecutionSummary() != null) {
                                resultVo.setProgressDesc(String.format("分析完成，成功率: %.1f%%",
                                        completeReportVo.getExecutionSummary().getSuccessRate()));
                            }
                        } catch (Exception e) {
                            log.warn("解析拆解结果JSON失败, atUniqueId: {}", atUniqueId, e);
                            resultVo.setHasResult(false);
                            resultVo.setErrorMessage("结果数据解析失败");
                        }
                    } else {
                        resultVo.setHasResult(false);
                        resultVo.setErrorMessage("任务完成但无结果数据");
                    }
                    break;

                case 2: // 任务失败
                    resultVo.setHasResult(false);
                    if (StringUtils.hasText(taskRecord.getResJson())) {
                        try {
                            // 尝试解析错误信息
                            Object errorObj = gson.fromJson(taskRecord.getResJson(), Object.class);
                            resultVo.setErrorMessage("任务执行失败: " + errorObj.toString());
                        } catch (Exception e) {
                            resultVo.setErrorMessage("任务执行失败");
                        }
                    } else {
                        resultVo.setErrorMessage("任务执行失败");
                    }
                    break;

                case 0: // 任务进行中
                default:
                    resultVo.setHasResult(false);
                    // 计算任务运行时长
                    if (taskRecord.getCreateTime() != null) {
                        long runningMinutes = java.time.Duration.between(
                                taskRecord.getCreateTime(),
                                LocalDateTime.now()
                        ).toMinutes();
                        resultVo.setProgressDesc(String.format("正在分析中，已运行 %d 分钟", runningMinutes));
                    }
                    break;
            }

            // 6. 添加额外的状态信息
            resultVo.setProgressDesc(resultVo.getProgressDesc()); // 使用VO中的方法生成描述

            log.info("成功查询账号拆解结果, atUniqueId: {}, taskStatus: {}", atUniqueId, taskRecord.getTaskStatus());
            return RD.ok(resultVo);

        } catch (Exception e) {
            log.error("查询账号拆解结果异常, userId: {}, atUniqueId: {}", userId, atUniqueId, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "查询拆解结果失败: " + e.getMessage());
        }
    }
}
