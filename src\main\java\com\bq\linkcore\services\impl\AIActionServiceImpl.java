package com.bq.linkcore.services.impl;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.entity.AtAccountUserSettingDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorPoolDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorWorkRecordDO;
import com.bq.linkcore.bean.entity.AuthorDisassemblyTaskRecordDO;
import com.bq.linkcore.bean.entity.TiktokArticleVideosDO;
import com.bq.linkcore.bean.entity.VideoTranscriptsTaskDO;
import com.bq.linkcore.bean.vo.SimpleDisassemblyResultVo;
import com.bq.linkcore.bean.vo.TranscriptVideoReqVo;
import com.bq.linkcore.bean.vo.TranscriptsRespVo;
import com.bq.linkcore.bean.vo.WorkInfoVo;
import com.bq.linkcore.biz.AIActionBiz;
import com.bq.linkcore.biz.AtTtAuthorPoolBiz;
import com.bq.linkcore.biz.AtTtAuthorWorkBiz;
import com.bq.linkcore.biz.AtUserAccountBiz;
import com.bq.linkcore.biz.VideoTransportTaskBiz;
import com.bq.linkcore.client.speechmatics.model.JobStatus;
import com.bq.linkcore.client.speechmatics.service.SpeechmaticsService;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ResponseMsg;
import com.bq.linkcore.services.IAIActionService;
import com.bq.linkcore.services.impl.aiAction.ReportGenerator;
import com.bq.linkcore.services.impl.aiAction.models.*;
import com.bq.linkcore.services.pool.ThreadRejectPolicy;
import com.bq.linkcore.services.pool.WorkThreadFactory;
import com.bq.linkcore.services.pool.WorkThreadPool;
import com.bq.linkcore.utils.DateTimeUtil;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.UUID;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class AIActionServiceImpl implements IAIActionService {

    @Resource
    private AIActionBiz aiActionBiz;

    @Resource
    private SpeechmaticsService speechmaticsService;

    @Resource
    private AtTtAuthorWorkBiz atTtAuthorWorkBiz;

    @Resource
    private AtUserAccountBiz atUserAccountBiz;

    @Resource
    private AtTtAuthorPoolBiz atTtAuthorPoolBiz;

    @Resource
    private ReportGenerator reportGenerator;

    @Resource
    private Gson gson;

    @Resource
    private VideoTransportTaskBiz videoTransportTaskBiz;

    private final static String AI_THREAD_TAG = "ai-action";

    private final WorkThreadPool aiActionThreadPool = new WorkThreadPool(
            2,
            8,
            2L,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(5000),
            new WorkThreadFactory(AI_THREAD_TAG),
            new ThreadRejectPolicy(AI_THREAD_TAG)
    );


    @Override
    public ResponseData addTxVideoTranscriptsTask(Long userId, TranscriptVideoReqVo vo) {
        try {
            log.info("开始添加视频转写任务, userId: {}, vo: {}", userId, vo);

            // 2. 校验用户是否有权限查看该账户
            AtAccountUserSettingDO atAccountUserSettingDO
                    = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, vo.getAtUniqueId());
            if (atAccountUserSettingDO == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, vo.getAtUniqueId());
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            // 3. 校验作品是否存在
            AtTiktokAuthorWorkRecordDO workRecordDO
                    = atTtAuthorWorkBiz.queryAuthorWorkRecordByWorkIdAndUniqueId(vo.getAtUniqueId(), vo.getWorkId());
            if (workRecordDO == null) {
                log.warn("作品不存在, workId: {}, atUniqueId: {}", vo.getWorkId(), vo.getAtUniqueId());
                return RD.fail(ResponseMsg.ERROR_WK_NOT_EXIST_WORK);
            }

            VideoTranscriptsTaskDO existingTask = aiActionBiz.queryVideoTranscriptsTaskByWorkId(vo.getWorkId());
            if (existingTask != null) {
                log.info("作品已存在转写任务, workId: {}, taskId: {}", vo.getWorkId(), existingTask.getTaskId());
                TranscriptsRespVo respVo = new TranscriptsRespVo();
                respVo.setTaskId(existingTask.getTaskId());
                respVo.setStatus(existingTask.getStatus());
                respVo.setResult(existingTask.getResult());
                return ResponseData.ok(respVo);
            }

            String videoId = workRecordDO.getVideoId();
            if (!StringUtils.hasText(videoId)) {
                log.warn("作品缺少视频ID, workId: {}", vo.getWorkId());
                return RD.fail(ResponseMsg.FAIL.getCode(), "作品缺少视频信息");
            }

            TiktokArticleVideosDO videoDO = atTtAuthorWorkBiz.queryArticleVideo(videoId);
            if (videoDO == null) {
                log.warn("视频详情不存在, videoId: {}", videoId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "视频详情不存在");
            }

            // 6. 检查视频URL
            String videoUrl = videoDO.getUrl();
            if (!StringUtils.hasText(videoUrl)) {
                log.warn("视频URL为空, videoId: {}", videoId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "视频URL不可用");
            }

            String speechmaticsTaskId = speechmaticsService.submitTranscriptionJob(videoUrl);
            if (!StringUtils.hasText(speechmaticsTaskId)) {
                log.error("创建Speechmatics转录任务失败, videoUrl: {}", videoUrl);
                return RD.fail(ResponseMsg.FAIL.getCode(), "创建转录任务失败");
            }

            // 8. 将任务信息插入数据库
            int insertResult = aiActionBiz.createVideoTranscriptsTask(
                    vo.getWorkId(),
                    videoUrl,
                    videoDO.getDuration(),
                    speechmaticsTaskId,
                    "tiktok"
            );

            if (insertResult <= 0) {
                log.error("插入转写任务记录失败, workId: {}, taskId: {}", vo.getWorkId(), speechmaticsTaskId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "保存任务信息失败");
            }

            // 9. 返回成功结果
            TranscriptsRespVo respVo = new TranscriptsRespVo();
            respVo.setTaskId(speechmaticsTaskId);
            respVo.setStatus(0); // 0-分析中
            respVo.setResult(null);

            log.info("成功创建视频转写任务, workId: {}, taskId: {}", vo.getWorkId(), speechmaticsTaskId);
            return ResponseData.ok(respVo);

        } catch (Exception e) {
            log.error("添加视频转写任务异常, userId: {}, vo: {}", userId, vo, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "添加转写任务失败: " + e.getMessage());
        }
    }

    @Override
    public ResponseData<TranscriptsRespVo> queryTxTranscriptsTask(Long userId, TranscriptVideoReqVo vo) {
        try {
            log.info("开始查询视频转写任务, userId: {}, vo: {}", userId, vo);

            // 1. 参数校验
            if (userId == null || vo == null || !StringUtils.hasText(vo.getWorkId())) {
                log.warn("参数校验失败, userId: {}, vo: {}", userId, vo);
                return RD.fail(ResponseMsg.ERROR_INVALID_PARAMS);
            }

            // 2. 校验用户是否有权限查看该账户
            AtAccountUserSettingDO atAccountUserSettingDO
                    = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, vo.getAtUniqueId());
            if (atAccountUserSettingDO == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, vo.getAtUniqueId());
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            // 3. 查询转写任务记录
            VideoTranscriptsTaskDO taskDO = aiActionBiz.queryVideoTranscriptsTaskByWorkId(vo.getWorkId());
            if (taskDO == null) {
                log.warn("转写任务不存在, workId: {}", vo.getWorkId());
                return RD.fail(ResponseMsg.FAIL.getCode(), "转写任务不存在");
            }

            // 4. 如果任务正在进行中，检查Speechmatics任务状态
            if (taskDO.getStatus() == 0) { // 0-分析中
                try {
                    // 检查Speechmatics任务状态
                    JobStatus jobStatus =
                            speechmaticsService.checkJobStatus(taskDO.getTaskId());

                    if (jobStatus != null) {
                        if (jobStatus.isCompleted()) {
                            // 任务已完成，获取结果并更新数据库
                            String result = speechmaticsService.getTranscriptionResult(taskDO.getTaskId());
                            if (StringUtils.hasText(result)) {
                                aiActionBiz.updateTaskResult(taskDO.getTaskId(), result, result, 1); // 1-分析完成
                                taskDO.setStatus(1);
                                taskDO.setResult(result);
                                taskDO.setResultJson(result);
                            }
                        } else if (jobStatus.isFailed()) {
                            // 任务失败，更新状态
                            aiActionBiz.updateTaskError(taskDO.getTaskId(), "Speechmatics任务失败: " + jobStatus.getDescription());
                            taskDO.setStatus(2); // 2-分析失败
                            taskDO.setErrMsg("Speechmatics任务失败: " + jobStatus.getDescription());
                        }
                    }
                } catch (Exception e) {
                    log.warn("检查Speechmatics任务状态异常, taskId: {}", taskDO.getTaskId(), e);
                    // 不影响返回结果，继续返回当前状态
                }
            }

            // 5. 构建返回结果
            TranscriptsRespVo respVo = new TranscriptsRespVo();
            respVo.setTaskId(taskDO.getTaskId());
            respVo.setStatus(taskDO.getStatus());
            respVo.setResult(taskDO.getResult());

            log.info("成功查询视频转写任务, workId: {}, taskId: {}, status: {}",
                    vo.getWorkId(), taskDO.getTaskId(), taskDO.getStatus());
            return ResponseData.ok(respVo);

        } catch (Exception e) {
            log.error("查询视频转写任务异常, userId: {}, vo: {}", userId, vo, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "查询转写任务失败: " + e.getMessage());
        }
    }

    @Override
    public ResponseData addTxAccountDisassemblyTask(Long userId, String atUniqueId) {
        try {
            log.info("开始添加账号拆解任务, userId: {}, atUniqueId: {}", userId, atUniqueId);

            if (userId == null || !StringUtils.hasText(atUniqueId)) {
                log.warn("参数校验失败, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.ERROR_INVALID_PARAMS);
            }

            AtAccountUserSettingDO atAccountUserSettingDO
                    = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
            if (atAccountUserSettingDO == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            AtTiktokAuthorPoolDO authorPoolDO = atTtAuthorPoolBiz.queryAuthorPoolByUniqueId(atUniqueId);
            if (authorPoolDO == null) {
                log.warn("账号不存在, atUniqueId: {}", atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "账号不存在");
            }

            // 4. 检查是否有正在进行中的拆解任务
            AuthorDisassemblyTaskRecordDO runningTask = aiActionBiz.queryRunningDisassemblyTaskByUniqueId(atUniqueId);
            if (runningTask != null) {
                log.info("账号已有正在进行中的拆解任务, atUniqueId: {}, taskId: {}, status: {}",
                        atUniqueId, runningTask.getTaskId(), runningTask.getTaskStatus());
                return RD.fail(ResponseMsg.FAIL.getCode(), "该账号已有正在进行中的拆解任务，请等待完成后再试");
            }

            // 5. 生成唯一的任务ID
            String taskId = generateTaskId(atUniqueId);

            // 6. 创建新的拆解任务
            int insertResult = aiActionBiz.createAuthorDisassemblyTask(
                    authorPoolDO.getAuthorId(),
                    atUniqueId,
                    taskId,
                    userId
            );

            if (insertResult <= 0) {
                log.error("插入拆解任务记录失败, atUniqueId: {}, userId: {}, taskId: {}", atUniqueId, userId, taskId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "创建任务失败");
            }

            // 7. 异步执行拆解任务
            aiActionThreadPool.addTask(new Runnable() {
                @Override
                public void run() {
                    executeDisassemblyTask(atUniqueId, userId, taskId);
                }
            });

            log.info("成功创建账号拆解任务, atUniqueId: {}, userId: {}, taskId: {}", atUniqueId, userId, taskId);
            return ResponseData.ok(taskId);

        } catch (Exception e) {
            log.error("添加账号拆解任务异常, userId: {}, atUniqueId: {}", userId, atUniqueId, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "添加拆解任务失败: " + e.getMessage());
        }
    }

    /**
     * 生成唯一的任务ID
     *
     * @param atUniqueId 账号唯一ID
     * @return 任务ID
     */
    private String generateTaskId(String atUniqueId) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return "TASK_" + atUniqueId + "_" + timestamp + "_" + uuid;
    }

    /**
     * 执行账号拆解任务
     */
    private void executeDisassemblyTask(String atUniqueId, Long userId, String taskId) {
        AuthorDisassemblyTaskRecordDO taskRecord = null;

        try {
            log.info("开始执行账号拆解任务, atUniqueId: {}, taskId: {}", atUniqueId, taskId);

            taskRecord = aiActionBiz.queryAuthorDisassemblyTaskByTaskId(taskId);
            if (taskRecord == null) {
                log.error("未找到拆解任务记录, atUniqueId: {}, taskId: {}", atUniqueId, taskId);
                return;
            }

            if (taskRecord.getTaskStatus() == 0) {
                aiActionBiz.updateAuthorDisassemblyTaskStatus(taskRecord.getId(), 0, userId);
            }

            CompleteReportVo completeReportVo = reportGenerator.generateCompleteReport(atUniqueId);

            String resultJson = gson.toJson(completeReportVo);

            Integer taskStatus = 1;
            if (completeReportVo != null && completeReportVo.getStatus() != null) {
                switch (completeReportVo.getStatus()) {
                    case SUCCESS:
                        taskStatus=1;
                        break;
                    case PARTIAL_SUCCESS:
                        taskStatus= 1;
                        break;
                    case FAILED:
                        taskStatus = 2;
                        break;
                    default:
                        taskStatus = 1;
                        break;
                }
            }

            // 6. 更新任务结果
            aiActionBiz.updateAuthorDisassemblyTaskResult(taskRecord.getId(), resultJson, taskStatus, userId);

            log.info("账号拆解任务执行完成, atUniqueId: {}, taskId: {}, taskStatus: {}", atUniqueId, taskId, taskStatus);

        } catch (Exception e) {
            log.error("执行账号拆解任务异常, atUniqueId: {}, taskId: {}", atUniqueId, taskId, e);

            // 更新任务状态为失败
            if (taskRecord != null) {
                try {
                    aiActionBiz.updateAuthorDisassemblyTaskResult(
                            taskRecord.getId(),
                            "{\"error\":\"" + e.getMessage() + "\"}",
                            2, // 失败
                            userId
                    );
                } catch (Exception updateException) {
                    log.error("更新任务失败状态异常, atUniqueId: {}, taskId: {}", atUniqueId, taskId, updateException);
                }
            }
        }
    }



    @Override
    public ResponseData<SimpleDisassemblyResultVo> queryTxAccountDisassemblyResult(Long userId, String atUniqueId) {
        try {
            log.info("开始查询账号拆解结果, userId: {}, atUniqueId: {}", userId, atUniqueId);

            if (userId == null || !StringUtils.hasText(atUniqueId)) {
                log.warn("参数校验失败, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.ERROR_INVALID_PARAMS);
            }

            AtAccountUserSettingDO atAccountUserSettingDO
                    = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
            if (atAccountUserSettingDO == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            AuthorDisassemblyTaskRecordDO taskRecord = aiActionBiz.queryAuthorDisassemblyTaskByUniqueId(atUniqueId);
            if (taskRecord == null) {
                log.warn("拆解任务不存在, atUniqueId: {}", atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "拆解任务不存在，请先创建任务");
            }

            SimpleDisassemblyResultVo resultVo = SimpleDisassemblyResultVo.builder()
                    .taskId(taskRecord.getTaskId())
                    .atUniqueId(atUniqueId)
                    .taskStatus(taskRecord.getTaskStatus())
                    .createTime(DateTimeUtil.convertUnixTs(taskRecord.getCreateTime()))
                    .updateTime(DateTimeUtil.convertUnixTs(taskRecord.getUpdateTime()))
                    .build();

            switch (taskRecord.getTaskStatus()) {
                case 1:
                    if (StringUtils.hasText(taskRecord.getResJson())) {
                        try {
                            CompleteReportVo completeReportVo = gson.fromJson(taskRecord.getResJson(), CompleteReportVo.class);

                            SimpleDisassemblyResultVo.TaskInfo taskInfo = convertToTaskInfo(completeReportVo);
                            resultVo.setTaskInfo(taskInfo);
                        } catch (Exception e) {
                            log.warn("解析拆解结果JSON失败, atUniqueId: {}", atUniqueId, e);
                        }
                    }
                    break;

                case 2: // 任务失败
                    // 失败状态下不设置taskInfo
                    break;

                case 0: // 任务进行中
                default:
                    // 进行中状态下不设置taskInfo
                    break;
            }

            log.info("成功查询账号拆解结果, atUniqueId: {}, taskStatus: {}", atUniqueId, taskRecord.getTaskStatus());
            return ResponseData.ok(resultVo);

        } catch (Exception e) {
            log.error("查询账号拆解结果异常, userId: {}, atUniqueId: {}", userId, atUniqueId, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "查询拆解结果失败: " + e.getMessage());
        }
    }

    /**
     * 将CompleteReportVo转换为SimpleDisassemblyResultVo.TaskInfo
     */
    private SimpleDisassemblyResultVo.TaskInfo convertToTaskInfo(CompleteReportVo completeReportVo) {
        if (completeReportVo == null) {
            return null;
        }

        SimpleDisassemblyResultVo.TaskInfo.TaskInfoBuilder taskInfoBuilder = SimpleDisassemblyResultVo.TaskInfo.builder();

        // 1. 处理账号诊断
        if (completeReportVo.getAccountDiagnosis() != null &&
            completeReportVo.getAccountDiagnosis().getSuccess() != null &&
            completeReportVo.getAccountDiagnosis().getSuccess()) {

            AccountDiagnosisVo accountDiagnosis = completeReportVo.getAccountDiagnosis().getData();
            if (accountDiagnosis != null && accountDiagnosis.getDiagnosisReport() != null) {
                taskInfoBuilder.accountDiagnosis(accountDiagnosis.getDiagnosisReport());
            }
        }

        List<SimpleDisassemblyResultVo.ContentAnalysisItem> contentAnalysisList = new ArrayList<>();
        if (completeReportVo.getContentAnalysis() != null &&
            completeReportVo.getContentAnalysis().getSuccess() != null &&
            completeReportVo.getContentAnalysis().getSuccess()) {

            ContentAnalysisVo contentAnalysis = completeReportVo.getContentAnalysis().getData();
            if (contentAnalysis != null && contentAnalysis.getContentTypes() != null) {
                for (ContentAnalysisVo.ContentType contentType : contentAnalysis.getContentTypes()) {
                    List<WorkInfoVo> list = queryWorkInfoList(contentType.getRelatedVideos());
                    SimpleDisassemblyResultVo.ContentAnalysisItem item = SimpleDisassemblyResultVo.ContentAnalysisItem.builder()
                            .content(contentType.getContent())
                            .relationWorks(list)
                            .build();
                    contentAnalysisList.add(item);
                }
            }
        }
        taskInfoBuilder.contentAnalysis(contentAnalysisList);

        List<SimpleDisassemblyResultVo.HitVideoAnalysisItem> hitVideoAnalysisList = new ArrayList<>();
        if (completeReportVo.getHitVideoAnalysis() != null &&
            completeReportVo.getHitVideoAnalysis().getSuccess() != null &&
            completeReportVo.getHitVideoAnalysis().getSuccess()) {

            HitVideoAnalysisVo hitVideoAnalysis = completeReportVo.getHitVideoAnalysis().getData();
            if (hitVideoAnalysis != null && hitVideoAnalysis.getHitModels() != null) {
                for (HitVideoAnalysisVo.HitModel hitModel : hitVideoAnalysis.getHitModels()) {
                    List<WorkInfoVo> list = queryWorkInfoList(hitModel.getRelatedVideos());
                    SimpleDisassemblyResultVo.HitVideoAnalysisItem item = SimpleDisassemblyResultVo.HitVideoAnalysisItem.builder()
                            .content(hitModel.getContent())
                            .relationWorks(list)
                            .build();
                    hitVideoAnalysisList.add(item);
                }
            }
        }
        taskInfoBuilder.hitVideoAnalysis(hitVideoAnalysisList);


        List<SimpleDisassemblyResultVo.HotScriptTemplate> hotScriptTemplates = new ArrayList<>();
        ReportResultVo<ScriptTemplateAnalysisVo> hotScriptTemplate = completeReportVo.getHotScriptTemplate();

        if (hotScriptTemplate!= null &&
                hotScriptTemplate.getSuccess() != null &&
                hotScriptTemplate.getSuccess()) {

            ScriptTemplateAnalysisVo hotScriptTemplateData = hotScriptTemplate.getData();
            if (hotScriptTemplateData != null && hotScriptTemplateData.getScriptTemplates() != null) {
                for (ScriptTemplateAnalysisVo.ScriptTemplate sp : hotScriptTemplateData.getScriptTemplates()) {
                    List<WorkInfoVo> list = queryWorkInfoList(sp.getRelationWorks());
                    SimpleDisassemblyResultVo.HotScriptTemplate item = SimpleDisassemblyResultVo.HotScriptTemplate.builder()
                            .type(sp.getType())
                            .typeFeature(sp.getTypeFeature())
                            .relationWorks(list)
                            .build();

                    hotScriptTemplates.add(item);
                }
            }
        }
        taskInfoBuilder.hotScriptTemplates(hotScriptTemplates);

        taskInfoBuilder.commercialAnalysis("商业化分析功能开发中");

        return taskInfoBuilder.build();
    }

    private List<WorkInfoVo> queryWorkInfoList(List<String> workList){

        List<WorkInfoVo> list = new ArrayList<>();
        for (String workUrl : workList) {
            AtTiktokAuthorWorkRecordDO atTiktokAuthorWorkRecordDO = atTtAuthorWorkBiz.queryAuthorWorkRecordByWorkUrl(workUrl);
            if (atTiktokAuthorWorkRecordDO == null) {
                log.error("获取作品数据失败 : workUrl: {}", workUrl);
            }
            WorkInfoVo workInfoVo = new WorkInfoVo();
            BeanUtils.copyProperties(atTiktokAuthorWorkRecordDO, workInfoVo);
            workInfoVo.setUpdateTime(DateTimeUtil.convertUnixTs(atTiktokAuthorWorkRecordDO.getUpdateTime()));
            workInfoVo.setCreateTime(DateTimeUtil.convertUnixTs(atTiktokAuthorWorkRecordDO.getCreateTime()));
            list.add(workInfoVo);
        }
        return list;
    }

    /**
     * 循环检查所有正在执行中的视频解析任务状态
     * 查询 status = 0 的任务，调用 SpeechmaticsService 检查任务状态
     * 如果任务完成，则更新任务状态并保存返回数据
     */
    public void checkRunningVideoTranscriptsTasks() {
        try {
            log.info("开始检查正在执行中的视频转写任务状态");

            // 1. 查询所有正在执行中的任务（status = 0）
            List<VideoTranscriptsTaskDO> runningTasks = videoTransportTaskBiz.queryRunningVideoTranscriptsTasks();

            if (runningTasks.isEmpty()) {
                log.debug("当前没有正在执行中的视频转写任务");
                return;
            }

            log.info("找到 {} 个正在执行中的视频转写任务，开始逐个检查状态", runningTasks.size());

            // 2. 逐个检查任务状态
            for (VideoTranscriptsTaskDO task : runningTasks) {
                checkSingleVideoTranscriptsTask(task);
            }

            log.info("完成所有正在执行中的视频转写任务状态检查");

        } catch (Exception e) {
            log.error("检查正在执行中的视频转写任务状态异常", e);
        }
    }

    /**
     * 检查单个视频转写任务的状态
     *
     * @param task 视频转写任务
     */
    private void checkSingleVideoTranscriptsTask(VideoTranscriptsTaskDO task) {
        if (task == null || !StringUtils.hasText(task.getTaskId())) {
            log.warn("任务信息无效，跳过检查");
            return;
        }

        try {
            log.debug("检查任务状态，taskId: {}, workId: {}", task.getTaskId(), task.getWorkId());

            // 1. 调用 SpeechmaticsService 检查任务状态
            JobStatus jobStatus = speechmaticsService.checkJobStatus(task.getTaskId());

            if (jobStatus == null) {
                log.warn("无法获取任务状态，taskId: {}", task.getTaskId());
                return;
            }

            log.info("任务状态检查结果，taskId: {}, status: {}", task.getTaskId(), jobStatus);

            // 2. 根据任务状态进行相应处理
            if (jobStatus.isCompleted()) {
                // 任务完成，获取转写结果并更新任务状态
                handleCompletedTask(task);
            } else if (jobStatus.isFailed()) {
                // 任务失败，更新任务状态为失败
                handleFailedTask(task, jobStatus);
            } else if (jobStatus.isRunning()) {
                // 任务仍在运行中，无需处理
                log.debug("任务仍在运行中，taskId: {}", task.getTaskId());
            } else {
                // 其他状态，记录日志
                log.warn("任务状态异常，taskId: {}, status: {}", task.getTaskId(), jobStatus);
            }

        } catch (Exception e) {
            log.error("检查单个视频转写任务状态异常，taskId: {}", task.getTaskId(), e);
        }
    }

    /**
     * 处理已完成的任务
     *
     * @param task 视频转写任务
     */
    private void handleCompletedTask(VideoTranscriptsTaskDO task) {
        try {
            log.info("处理已完成的任务，taskId: {}", task.getTaskId());

            // 1. 获取转写结果
            String transcriptionResult = speechmaticsService.getTranscriptionResult(task.getTaskId());

            if (transcriptionResult == null) {
                log.error("获取转写结果失败，taskId: {}", task.getTaskId());
                // 更新任务状态为失败
                videoTransportTaskBiz.updateTaskFailure(task.getTaskId(), "获取转写结果失败");
                return;
            }

            // 2. 更新任务状态为完成，并保存转写结果
            int updateResult = videoTransportTaskBiz.updateTaskSuccess(task.getTaskId(),
                    transcriptionResult, transcriptionResult);

            if (updateResult > 0) {
                log.info("任务完成状态更新成功，taskId: {}, workId: {}",
                        task.getTaskId(), task.getWorkId());
            } else {
                log.warn("任务完成状态更新失败，taskId: {}", task.getTaskId());
            }

        } catch (Exception e) {
            log.error("处理已完成任务异常，taskId: {}", task.getTaskId(), e);
            // 发生异常时，尝试更新任务状态为失败
            try {
                videoTransportTaskBiz.updateTaskFailure(task.getTaskId(),
                        "处理完成任务时发生异常: " + e.getMessage());
            } catch (Exception updateException) {
                log.error("更新任务失败状态异常，taskId: {}", task.getTaskId(), updateException);
            }
        }
    }

    /**
     * 处理失败的任务
     *
     * @param task 视频转写任务
     * @param jobStatus 任务状态
     */
    private void handleFailedTask(VideoTranscriptsTaskDO task, JobStatus jobStatus) {
        try {
            log.info("处理失败的任务，taskId: {}, status: {}", task.getTaskId(), jobStatus);

            String errorMessage = String.format("Speechmatics任务失败，状态: %s (%s)",
                    jobStatus.getCode(), jobStatus.getDescription());

            // 更新任务状态为失败
            int updateResult = videoTransportTaskBiz.updateTaskFailure(task.getTaskId(), errorMessage);

            if (updateResult > 0) {
                log.info("任务失败状态更新成功，taskId: {}, workId: {}",
                        task.getTaskId(), task.getWorkId());
            } else {
                log.warn("任务失败状态更新失败，taskId: {}", task.getTaskId());
            }

        } catch (Exception e) {
            log.error("处理失败任务异常，taskId: {}", task.getTaskId(), e);
        }
    }
}
