package com.bq.linkcore.services.impl;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.entity.AtAccountUserSettingDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorPoolDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorWorkRecordDO;
import com.bq.linkcore.bean.entity.AuthorDisassemblyTaskRecordDO;
import com.bq.linkcore.bean.entity.TiktokArticleVideosDO;
import com.bq.linkcore.bean.entity.VideoTranscriptsTaskDO;
import com.bq.linkcore.bean.vo.SimpleDisassemblyResultVo;
import com.bq.linkcore.bean.vo.TranscriptVideoReqVo;
import com.bq.linkcore.bean.vo.TranscriptsRespVo;
import com.bq.linkcore.biz.AIActionBiz;
import com.bq.linkcore.biz.AtTtAuthorPoolBiz;
import com.bq.linkcore.biz.AtTtAuthorWorkBiz;
import com.bq.linkcore.biz.AtUserAccountBiz;
import com.bq.linkcore.client.speechmatics.model.JobStatus;
import com.bq.linkcore.client.speechmatics.service.SpeechmaticsService;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ResponseMsg;
import com.bq.linkcore.services.IAIActionService;
import com.bq.linkcore.services.impl.aiAction.ReportGenerator;
import com.bq.linkcore.services.impl.aiAction.models.CompleteReportVo;
import com.bq.linkcore.services.impl.aiAction.models.AccountDiagnosisVo;
import com.bq.linkcore.services.impl.aiAction.models.ContentAnalysisVo;
import com.bq.linkcore.services.impl.aiAction.models.HitVideoAnalysisVo;
import com.bq.linkcore.services.pool.ThreadRejectPolicy;
import com.bq.linkcore.services.pool.WorkThreadFactory;
import com.bq.linkcore.services.pool.WorkThreadPool;
import com.bq.linkcore.utils.DateTimeUtil;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.UUID;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class AIActionServiceImpl implements IAIActionService {

    @Resource
    private AIActionBiz aiActionBiz;

    @Resource
    private SpeechmaticsService speechmaticsService;

    @Resource
    private AtTtAuthorWorkBiz atTtAuthorWorkBiz;

    @Resource
    private AtUserAccountBiz atUserAccountBiz;

    @Resource
    private AtTtAuthorPoolBiz atTtAuthorPoolBiz;

    @Resource
    private ReportGenerator reportGenerator;

    @Resource
    private Gson gson;

    private final static String AI_THREAD_TAG = "ai-action";

    private final WorkThreadPool aiActionThreadPool = new WorkThreadPool(
            2,
            8,
            2L,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(5000),
            new WorkThreadFactory(AI_THREAD_TAG),
            new ThreadRejectPolicy(AI_THREAD_TAG)
    );


    @Override
    public ResponseData addTxVideoTranscriptsTask(Long userId, TranscriptVideoReqVo vo) {
        try {
            log.info("开始添加视频转写任务, userId: {}, vo: {}", userId, vo);

            // 2. 校验用户是否有权限查看该账户
            AtAccountUserSettingDO atAccountUserSettingDO
                    = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, vo.getAtUniqueId());
            if (atAccountUserSettingDO == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, vo.getAtUniqueId());
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            // 3. 校验作品是否存在
            AtTiktokAuthorWorkRecordDO workRecordDO
                    = atTtAuthorWorkBiz.queryAuthorWorkRecordByWorkIdAndUniqueId(vo.getAtUniqueId(), vo.getWorkId());
            if (workRecordDO == null) {
                log.warn("作品不存在, workId: {}, atUniqueId: {}", vo.getWorkId(), vo.getAtUniqueId());
                return RD.fail(ResponseMsg.ERROR_WK_NOT_EXIST_WORK);
            }

            // 4. 检查是否已存在转写任务
            VideoTranscriptsTaskDO existingTask = aiActionBiz.queryVideoTranscriptsTaskByWorkId(vo.getWorkId());
            if (existingTask != null) {
                log.info("作品已存在转写任务, workId: {}, taskId: {}", vo.getWorkId(), existingTask.getTaskId());
                TranscriptsRespVo respVo = new TranscriptsRespVo();
                respVo.setTaskId(existingTask.getTaskId());
                respVo.setStatus(existingTask.getStatus());
                respVo.setResult(existingTask.getResult());
                return ResponseData.ok(respVo);
            }

            // 5. 获取视频详情信息
            String videoId = workRecordDO.getVideoId();
            if (!StringUtils.hasText(videoId)) {
                log.warn("作品缺少视频ID, workId: {}", vo.getWorkId());
                return RD.fail(ResponseMsg.FAIL.getCode(), "作品缺少视频信息");
            }

            TiktokArticleVideosDO videoDO = atTtAuthorWorkBiz.queryArticleVideo(videoId);
            if (videoDO == null) {
                log.warn("视频详情不存在, videoId: {}", videoId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "视频详情不存在");
            }

            // 6. 检查视频URL
            String videoUrl = videoDO.getUrl();
            if (!StringUtils.hasText(videoUrl)) {
                log.warn("视频URL为空, videoId: {}", videoId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "视频URL不可用");
            }

            String speechmaticsTaskId = speechmaticsService.submitTranscriptionJob(videoUrl, "en");
            if (!StringUtils.hasText(speechmaticsTaskId)) {
                log.error("创建Speechmatics转录任务失败, videoUrl: {}", videoUrl);
                return RD.fail(ResponseMsg.FAIL.getCode(), "创建转录任务失败");
            }

            // 8. 将任务信息插入数据库
            int insertResult = aiActionBiz.createVideoTranscriptsTask(
                    vo.getWorkId(),
                    videoUrl,
                    videoDO.getDuration(),
                    speechmaticsTaskId,
                    "tiktok"
            );

            if (insertResult <= 0) {
                log.error("插入转写任务记录失败, workId: {}, taskId: {}", vo.getWorkId(), speechmaticsTaskId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "保存任务信息失败");
            }

            // 9. 返回成功结果
            TranscriptsRespVo respVo = new TranscriptsRespVo();
            respVo.setTaskId(speechmaticsTaskId);
            respVo.setStatus(0); // 0-分析中
            respVo.setResult(null);

            log.info("成功创建视频转写任务, workId: {}, taskId: {}", vo.getWorkId(), speechmaticsTaskId);
            return ResponseData.ok(respVo);

        } catch (Exception e) {
            log.error("添加视频转写任务异常, userId: {}, vo: {}", userId, vo, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "添加转写任务失败: " + e.getMessage());
        }
    }

    @Override
    public ResponseData<TranscriptsRespVo> queryTxTranscriptsTask(Long userId, TranscriptVideoReqVo vo) {
        try {
            log.info("开始查询视频转写任务, userId: {}, vo: {}", userId, vo);

            // 1. 参数校验
            if (userId == null || vo == null || !StringUtils.hasText(vo.getWorkId())) {
                log.warn("参数校验失败, userId: {}, vo: {}", userId, vo);
                return RD.fail(ResponseMsg.ERROR_INVALID_PARAMS);
            }

            // 2. 校验用户是否有权限查看该账户
            AtAccountUserSettingDO atAccountUserSettingDO
                    = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, vo.getAtUniqueId());
            if (atAccountUserSettingDO == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, vo.getAtUniqueId());
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            // 3. 查询转写任务记录
            VideoTranscriptsTaskDO taskDO = aiActionBiz.queryVideoTranscriptsTaskByWorkId(vo.getWorkId());
            if (taskDO == null) {
                log.warn("转写任务不存在, workId: {}", vo.getWorkId());
                return RD.fail(ResponseMsg.FAIL.getCode(), "转写任务不存在");
            }

            // 4. 如果任务正在进行中，检查Speechmatics任务状态
            if (taskDO.getStatus() == 0) { // 0-分析中
                try {
                    // 检查Speechmatics任务状态
                    JobStatus jobStatus =
                            speechmaticsService.checkJobStatus(taskDO.getTaskId());

                    if (jobStatus != null) {
                        if (jobStatus.isCompleted()) {
                            // 任务已完成，获取结果并更新数据库
                            String result = speechmaticsService.getTranscriptionResult(taskDO.getTaskId());
                            if (StringUtils.hasText(result)) {
                                aiActionBiz.updateTaskResult(taskDO.getTaskId(), result, result, 1); // 1-分析完成
                                taskDO.setStatus(1);
                                taskDO.setResult(result);
                                taskDO.setResultJson(result);
                            }
                        } else if (jobStatus.isFailed()) {
                            // 任务失败，更新状态
                            aiActionBiz.updateTaskError(taskDO.getTaskId(), "Speechmatics任务失败: " + jobStatus.getDescription());
                            taskDO.setStatus(2); // 2-分析失败
                            taskDO.setErrMsg("Speechmatics任务失败: " + jobStatus.getDescription());
                        }
                    }
                } catch (Exception e) {
                    log.warn("检查Speechmatics任务状态异常, taskId: {}", taskDO.getTaskId(), e);
                    // 不影响返回结果，继续返回当前状态
                }
            }

            // 5. 构建返回结果
            TranscriptsRespVo respVo = new TranscriptsRespVo();
            respVo.setTaskId(taskDO.getTaskId());
            respVo.setStatus(taskDO.getStatus());
            respVo.setResult(taskDO.getResult());

            log.info("成功查询视频转写任务, workId: {}, taskId: {}, status: {}",
                    vo.getWorkId(), taskDO.getTaskId(), taskDO.getStatus());
            return ResponseData.ok(respVo);

        } catch (Exception e) {
            log.error("查询视频转写任务异常, userId: {}, vo: {}", userId, vo, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "查询转写任务失败: " + e.getMessage());
        }
    }

    @Override
    public ResponseData addTxAccountDisassemblyTask(Long userId, String atUniqueId) {
        try {
            log.info("开始添加账号拆解任务, userId: {}, atUniqueId: {}", userId, atUniqueId);

            if (userId == null || !StringUtils.hasText(atUniqueId)) {
                log.warn("参数校验失败, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.ERROR_INVALID_PARAMS);
            }

            AtAccountUserSettingDO atAccountUserSettingDO
                    = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
            if (atAccountUserSettingDO == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            AtTiktokAuthorPoolDO authorPoolDO = atTtAuthorPoolBiz.queryAuthorPoolByUniqueId(atUniqueId);
            if (authorPoolDO == null) {
                log.warn("账号不存在, atUniqueId: {}", atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "账号不存在");
            }

            // 4. 检查是否有正在进行中的拆解任务
            AuthorDisassemblyTaskRecordDO runningTask = aiActionBiz.queryRunningDisassemblyTaskByUniqueId(atUniqueId);
            if (runningTask != null) {
                log.info("账号已有正在进行中的拆解任务, atUniqueId: {}, taskId: {}, status: {}",
                        atUniqueId, runningTask.getTaskId(), runningTask.getTaskStatus());
                return RD.fail(ResponseMsg.FAIL.getCode(), "该账号已有正在进行中的拆解任务，请等待完成后再试");
            }

            // 5. 生成唯一的任务ID
            String taskId = generateTaskId(atUniqueId);

            // 6. 创建新的拆解任务
            int insertResult = aiActionBiz.createAuthorDisassemblyTask(
                    authorPoolDO.getAuthorId(),
                    atUniqueId,
                    taskId,
                    userId
            );

            if (insertResult <= 0) {
                log.error("插入拆解任务记录失败, atUniqueId: {}, userId: {}, taskId: {}", atUniqueId, userId, taskId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "创建任务失败");
            }

            // 7. 异步执行拆解任务
            aiActionThreadPool.addTask(new Runnable() {
                @Override
                public void run() {
                    executeDisassemblyTask(atUniqueId, userId, taskId);
                }
            });

            log.info("成功创建账号拆解任务, atUniqueId: {}, userId: {}, taskId: {}", atUniqueId, userId, taskId);
            return ResponseData.ok(taskId);

        } catch (Exception e) {
            log.error("添加账号拆解任务异常, userId: {}, atUniqueId: {}", userId, atUniqueId, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "添加拆解任务失败: " + e.getMessage());
        }
    }

    /**
     * 生成唯一的任务ID
     *
     * @param atUniqueId 账号唯一ID
     * @return 任务ID
     */
    private String generateTaskId(String atUniqueId) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return "TASK_" + atUniqueId + "_" + timestamp + "_" + uuid;
    }

    /**
     * 执行账号拆解任务
     */
    private void executeDisassemblyTask(String atUniqueId, Long userId, String taskId) {
        AuthorDisassemblyTaskRecordDO taskRecord = null;

        try {
            log.info("开始执行账号拆解任务, atUniqueId: {}, taskId: {}", atUniqueId, taskId);

            taskRecord = aiActionBiz.queryAuthorDisassemblyTaskByTaskId(taskId);
            if (taskRecord == null) {
                log.error("未找到拆解任务记录, atUniqueId: {}, taskId: {}", atUniqueId, taskId);
                return;
            }

            if (taskRecord.getTaskStatus() == 0) {
                aiActionBiz.updateAuthorDisassemblyTaskStatus(taskRecord.getId(), 0, userId);
            }

            CompleteReportVo completeReportVo = reportGenerator.generateCompleteReport(atUniqueId);

            String resultJson = gson.toJson(completeReportVo);

            Integer taskStatus = 1;
            if (completeReportVo != null && completeReportVo.getStatus() != null) {
                switch (completeReportVo.getStatus()) {
                    case SUCCESS:
                        taskStatus=1;
                        break;
                    case PARTIAL_SUCCESS:
                        taskStatus= 1;
                        break;
                    case FAILED:
                        taskStatus = 2;
                        break;
                    default:
                        taskStatus = 1;
                        break;
                }
            }

            // 6. 更新任务结果
            aiActionBiz.updateAuthorDisassemblyTaskResult(taskRecord.getId(), resultJson, taskStatus, userId);

            log.info("账号拆解任务执行完成, atUniqueId: {}, taskId: {}, taskStatus: {}", atUniqueId, taskId, taskStatus);

        } catch (Exception e) {
            log.error("执行账号拆解任务异常, atUniqueId: {}, taskId: {}", atUniqueId, taskId, e);

            // 更新任务状态为失败
            if (taskRecord != null) {
                try {
                    aiActionBiz.updateAuthorDisassemblyTaskResult(
                            taskRecord.getId(),
                            "{\"error\":\"" + e.getMessage() + "\"}",
                            2, // 失败
                            userId
                    );
                } catch (Exception updateException) {
                    log.error("更新任务失败状态异常, atUniqueId: {}, taskId: {}", atUniqueId, taskId, updateException);
                }
            }
        }
    }



    @Override
    public ResponseData<SimpleDisassemblyResultVo> queryTxAccountDisassemblyResult(Long userId, String atUniqueId) {
        try {
            log.info("开始查询账号拆解结果, userId: {}, atUniqueId: {}", userId, atUniqueId);

            if (userId == null || !StringUtils.hasText(atUniqueId)) {
                log.warn("参数校验失败, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.ERROR_INVALID_PARAMS);
            }

            AtAccountUserSettingDO atAccountUserSettingDO
                    = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
            if (atAccountUserSettingDO == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "用户未监控该账户");
            }

            AuthorDisassemblyTaskRecordDO taskRecord = aiActionBiz.queryAuthorDisassemblyTaskByUniqueId(atUniqueId);
            if (taskRecord == null) {
                log.warn("拆解任务不存在, atUniqueId: {}", atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "拆解任务不存在，请先创建任务");
            }

            SimpleDisassemblyResultVo resultVo = SimpleDisassemblyResultVo.builder()
                    .taskId(taskRecord.getTaskId())
                    .atUniqueId(atUniqueId)
                    .taskStatus(taskRecord.getTaskStatus())
                    .createTime(DateTimeUtil.convertUnixTs(taskRecord.getCreateTime()))
                    .updateTime(DateTimeUtil.convertUnixTs(taskRecord.getUpdateTime()))
                    .build();

            switch (taskRecord.getTaskStatus()) {
                case 1:
                    if (StringUtils.hasText(taskRecord.getResJson())) {
                        try {
                            CompleteReportVo completeReportVo = gson.fromJson(taskRecord.getResJson(), CompleteReportVo.class);

                            SimpleDisassemblyResultVo.TaskInfo taskInfo = convertToTaskInfo(completeReportVo);
                            resultVo.setTaskInfo(taskInfo);
                        } catch (Exception e) {
                            log.warn("解析拆解结果JSON失败, atUniqueId: {}", atUniqueId, e);
                        }
                    }
                    break;

                case 2: // 任务失败
                    // 失败状态下不设置taskInfo
                    break;

                case 0: // 任务进行中
                default:
                    // 进行中状态下不设置taskInfo
                    break;
            }

            log.info("成功查询账号拆解结果, atUniqueId: {}, taskStatus: {}", atUniqueId, taskRecord.getTaskStatus());
            return ResponseData.ok(resultVo);

        } catch (Exception e) {
            log.error("查询账号拆解结果异常, userId: {}, atUniqueId: {}", userId, atUniqueId, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "查询拆解结果失败: " + e.getMessage());
        }
    }

    /**
     * 将CompleteReportVo转换为SimpleDisassemblyResultVo.TaskInfo
     */
    private SimpleDisassemblyResultVo.TaskInfo convertToTaskInfo(CompleteReportVo completeReportVo) {
        if (completeReportVo == null) {
            return null;
        }

        SimpleDisassemblyResultVo.TaskInfo.TaskInfoBuilder taskInfoBuilder = SimpleDisassemblyResultVo.TaskInfo.builder();

        // 1. 处理账号诊断
        if (completeReportVo.getAccountDiagnosis() != null &&
            completeReportVo.getAccountDiagnosis().getSuccess() != null &&
            completeReportVo.getAccountDiagnosis().getSuccess()) {

            AccountDiagnosisVo accountDiagnosis = completeReportVo.getAccountDiagnosis().getData();
            if (accountDiagnosis != null && accountDiagnosis.getDiagnosisReport() != null) {
                taskInfoBuilder.accountDiagnosis(accountDiagnosis.getDiagnosisReport());
            }
        }

        List<SimpleDisassemblyResultVo.ContentAnalysisItem> contentAnalysisList = new ArrayList<>();
        if (completeReportVo.getContentAnalysis() != null &&
            completeReportVo.getContentAnalysis().getSuccess() != null &&
            completeReportVo.getContentAnalysis().getSuccess()) {

            ContentAnalysisVo contentAnalysis = completeReportVo.getContentAnalysis().getData();
            if (contentAnalysis != null && contentAnalysis.getContentTypes() != null) {
                for (ContentAnalysisVo.ContentType contentType : contentAnalysis.getContentTypes()) {
                    SimpleDisassemblyResultVo.ContentAnalysisItem item = SimpleDisassemblyResultVo.ContentAnalysisItem.builder()
                            .content(contentType.getContent())
                            .relationWorks(contentType.getRelatedVideos())
                            .build();
                    contentAnalysisList.add(item);
                }
            }
        }
        taskInfoBuilder.contentAnalysis(contentAnalysisList);

        List<SimpleDisassemblyResultVo.HitVideoAnalysisItem> hitVideoAnalysisList = new ArrayList<>();
        if (completeReportVo.getHitVideoAnalysis() != null &&
            completeReportVo.getHitVideoAnalysis().getSuccess() != null &&
            completeReportVo.getHitVideoAnalysis().getSuccess()) {

            HitVideoAnalysisVo hitVideoAnalysis = completeReportVo.getHitVideoAnalysis().getData();
            if (hitVideoAnalysis != null && hitVideoAnalysis.getHitModels() != null) {
                for (HitVideoAnalysisVo.HitModel hitModel : hitVideoAnalysis.getHitModels()) {

                    SimpleDisassemblyResultVo.HitVideoAnalysisItem item = SimpleDisassemblyResultVo.HitVideoAnalysisItem.builder()
                            .content(hitModel.getContent())
                            .relationWorks(hitModel.getRelatedVideos())
                            .build();
                    hitVideoAnalysisList.add(item);
                }
            }
        }
        taskInfoBuilder.hitVideoAnalysis(hitVideoAnalysisList);

        taskInfoBuilder.commercialAnalysis("商业化分析功能开发中");

        return taskInfoBuilder.build();
    }
}
