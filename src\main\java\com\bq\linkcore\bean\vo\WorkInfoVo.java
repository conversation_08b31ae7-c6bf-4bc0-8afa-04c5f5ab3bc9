package com.bq.linkcore.bean.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel("作品信息")
public class WorkInfoVo {
    @ApiModelProperty(value = "作品唯一标识")
    private String workId;
    @ApiModelProperty(value = "发布账号ID")
    private String authorId;
    @ApiModelProperty(value = "作者唯一用户名")
    private String uniqueId;
    @ApiModelProperty(value = "作者账号加密ID")
    private String secUid;
    @ApiModelProperty(value = "作品链接")
    private String url;
    @ApiModelProperty(value = "分类类型(对应TikTok分类体系)")
    private Integer categoryType;
    @ApiModelProperty(value = "封面图链接")
    private String thumbnailLink;
    @ApiModelProperty(value = "是否广告(1:是,0:否)")
    private Integer isAd;
    @ApiModelProperty(value = "作品标题")
    private String title;
    @ApiModelProperty(value = "作品内容描述")
    private String content;
    @ApiModelProperty(value = "话题标签数组，格式: [\"#tag1\",\"#tag2\"]")
    private String hashtags;
    @ApiModelProperty(value = "图片URL数组，格式: [\"url1\",\"url2\"]")
    private String images;
    @ApiModelProperty(value = "发布时间戳")
    private Integer publishTime;
    @ApiModelProperty(value = "文本语言(ISO 639-1)")
    private String textLanguage;
    @ApiModelProperty(value = "发布地理位置")
    private String locationIp;
    @ApiModelProperty(value = "播放量")
    private Integer playCount;
    @ApiModelProperty(value = "点赞数")
    private Integer likeCount;
    @ApiModelProperty(value = "评论数")
    private Integer commentCount;
    @ApiModelProperty(value = "转发数")
    private Integer shareCount;
    @ApiModelProperty(value = "收藏数")
    private Integer collectCount;
    @ApiModelProperty(value = "视频ID")
    private String videoId;
    @ApiModelProperty(value = "下载地址")
    private String videoUrl;
    @ApiModelProperty(value = "音乐ID")
    private String musicId;
    @ApiModelProperty(value = "创建者")
    private Long creator;
    @ApiModelProperty(value = "数据创建时间")
    private LocalDateTime createTime;
    @ApiModelProperty(value = "数据更新人")
    private Long updater;
    @ApiModelProperty(value = "数据更新时间")
    private LocalDateTime updateTime;

}