package com.bq.linkcore.client.tikhub.models;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/7/18 14:27
 * @className TiktokHashTagModel
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TiktokHashTagModel {

    /**
     * 标签ID
     */
    private String hashtagId;

    /**
     * 标签名称
     */
    private String hashtagName;

    /**
     * 国家标签
     */
    private JSONObject countryInfo;

    /**
     * 行业标签
     */
    private JSONObject industryInfo;

    /**
     * 是否推广,0=否；1=是
     */
    private Integer isPromoted;

    /**
     * 趋势数据列表
     */
    private JSONArray trend;

    /**
     * 创作者列表
     */
    private JSONArray creators;

    /**
     * 发布数量
     */
    private Long publishCnt;

    /**
     * 视频观看量
     */
    private Long videoViews;

    /**
     * 排名
     */
    private Integer rank;

    /**
     * 排名变化
     */
    private Integer rankDiff;

    /**
     * 排名变化类型
     */
    private Integer rankDiffType;

    private String recordDay;

    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;


}
