package com.bq.linkcore.client;

import com.alibaba.fastjson.JSON;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.bq.linkcore.config.SmsConfig;
import com.bq.linkcore.utils.AssertUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

import static com.bq.linkcore.common.ResponseMsg.ERROR_SEND_MESSAGE;

/**
 * <AUTHOR>
 * @date 2023/3/17 13:10
 * @className SmsClient
 * @description
 */
@Slf4j
@Component
public class SmsClient {

    private RestTemplate restTemplate;

    @Resource
    private SmsConfig smsConfig;

    private Client smsClientInner;

    private void initClient() {
        if (smsClientInner == null) {
            com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();

            config.setAccessKeyId(smsConfig.getAccessKeyId());
            config.setAccessKeySecret(smsConfig.getAccessKeySecret());
            config.setEndpoint(smsConfig.getEndpoint());

            try {
                smsClientInner = new Client(config);
            } catch (Exception e) {
                e.printStackTrace();
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 发送短信
     *
     * @param phone
     * @param templateCode
     * @param code
     * @return
     */
    public boolean sendSms(String phone, String templateCode, String code) {
        try {
            if (smsClientInner == null) {
                initClient();
            }

            AssertUtils.assertNull(smsClientInner, ERROR_SEND_MESSAGE);

            // 手机
            // 组装短信请求对象
            SendSmsRequest request = new SendSmsRequest();
            // 待发送的手机号，支持逗号分割的批量，上限为1000个
            request.setPhoneNumbers(phone);
            // 短信签名
            request.setSignName(smsConfig.getSignName());
            // 短信模板
            request.setTemplateCode(templateCode);
            // 短信参数
            String templateParam = "{\"code\":\"" + code + "\"}";
            request.setTemplateParam(templateParam);
            SendSmsResponse response = smsClientInner.sendSms(request);

            log.info("阿里短信服务返回的信息={}", JSON.toJSONString(response));
            if (null != response && null != response.getBody()) {
                if ("OK".equals(response.getBody().getCode())) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return false;
    }


}
