package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.UserMonitorRuleSettingDO;
import com.bq.linkcore.dao.mapper.UserMonitorRuleSettingMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Component
public class UserMonitorRuleBiz {

    @Resource
    private UserMonitorRuleSettingMapper userMonitorRuleSettingMapper;

    /**
     * 插入用户监控规则设置
     * @param userMonitorRuleSetting 用户监控规则设置实体对象
     * @return 插入影响的行数
     */
    public int insertUserMonitorRuleSetting(UserMonitorRuleSettingDO userMonitorRuleSetting) {
        return userMonitorRuleSettingMapper.insert(userMonitorRuleSetting);
    }

    /**
     * 根据ID更新用户监控规则设置
     * @param userMonitorRuleSetting 用户监控规则设置实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateUserMonitorRuleSetting(UserMonitorRuleSettingDO userMonitorRuleSetting) {
        return userMonitorRuleSettingMapper.updateById(userMonitorRuleSetting);
    }

    /**
     * 根据用户ID查询用户监控规则设置
     * @param userId 用户ID
     * @return 用户监控规则设置实体对象，不存在则返回null
     */
    public UserMonitorRuleSettingDO queryUserMonitorRuleSettingByUserId(Long userId) {
        LambdaQueryWrapper<UserMonitorRuleSettingDO> queryWrapper = new LambdaQueryWrapper<UserMonitorRuleSettingDO>()
                .eq(UserMonitorRuleSettingDO::getUserId, userId)
                .eq(UserMonitorRuleSettingDO::getIsDel, 0);
        return userMonitorRuleSettingMapper.selectOne(queryWrapper);
    }

    /**
     * 根据ID查询用户监控规则设置
     * @param id 主键ID
     * @return 用户监控规则设置实体对象，不存在则返回null
     */
    public UserMonitorRuleSettingDO queryUserMonitorRuleSettingById(Long id) {
        return userMonitorRuleSettingMapper.selectById(id);
    }

    /**
     * 查询所有用户监控规则设置列表
     * @return 用户监控规则设置列表
     */
    public List<UserMonitorRuleSettingDO> queryAllUserMonitorRuleSettings() {
        return userMonitorRuleSettingMapper.selectList(null);
    }

    /**
     * 根据用户ID删除用户监控规则设置
     * @param userId 用户ID
     * @return 删除影响的行数
     */
    public int deleteUserMonitorRuleSettingByUserId(Long userId) {
        LambdaQueryWrapper<UserMonitorRuleSettingDO> queryWrapper = new LambdaQueryWrapper<UserMonitorRuleSettingDO>()
                .eq(UserMonitorRuleSettingDO::getUserId, userId);
        return userMonitorRuleSettingMapper.delete(queryWrapper);
    }

    /**
     * 根据ID删除用户监控规则设置
     * @param id 主键ID
     * @return 删除影响的行数
     */
    public int deleteUserMonitorRuleSettingById(Long id) {
        return userMonitorRuleSettingMapper.deleteById(id);
    }

    /**
     * 保存或更新用户监控规则设置（如果用户已有配置则更新，否则插入）
     * @param userId 用户ID
     * @param hotVideoRule 视频爆款阈值规则
     * @param authorFansRule 达人粉丝阈值规则
     * @param operatorUserId 操作用户ID
     * @return 操作影响的行数
     */
    public int saveOrUpdateUserMonitorRuleSetting(Long userId, String hotVideoRule, String authorFansRule, Long operatorUserId) {
        UserMonitorRuleSettingDO existingSetting = queryUserMonitorRuleSettingByUserId(userId);

        if (existingSetting != null) {
            // 更新现有配置
            existingSetting.setHotVideoRule(hotVideoRule);
            existingSetting.setAuthorFansRule(authorFansRule);
            existingSetting.setUpdater(operatorUserId);
            existingSetting.setUpdateTime(LocalDateTime.now());
            return updateUserMonitorRuleSetting(existingSetting);
        } else {
            // 插入新配置
            UserMonitorRuleSettingDO newSetting = UserMonitorRuleSettingDO.builder()
                    .userId(userId)
                    .hotVideoRule(hotVideoRule)
                    .authorFansRule(authorFansRule)
                    .creator(operatorUserId)
                    .createTime(LocalDateTime.now())
                    .updater(operatorUserId)
                    .updateTime(LocalDateTime.now())
                    .build();
            return insertUserMonitorRuleSetting(newSetting);
        }
    }

    /**
     * 检查用户是否已有监控规则设置
     * @param userId 用户ID
     * @return 是否存在配置
     */
    public boolean existsUserMonitorRuleSetting(Long userId) {
        LambdaQueryWrapper<UserMonitorRuleSettingDO> queryWrapper = new LambdaQueryWrapper<UserMonitorRuleSettingDO>()
                .eq(UserMonitorRuleSettingDO::getUserId, userId);
        return userMonitorRuleSettingMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 批量插入用户监控规则设置
     * @param userMonitorRuleSettings 用户监控规则设置列表
     * @return 插入成功的记录数
     */
    public int batchInsertUserMonitorRuleSettings(List<UserMonitorRuleSettingDO> userMonitorRuleSettings) {
        if (userMonitorRuleSettings == null || userMonitorRuleSettings.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (UserMonitorRuleSettingDO setting : userMonitorRuleSettings) {
            int result = insertUserMonitorRuleSetting(setting);
            if (result > 0) {
                successCount++;
            }
        }
        return successCount;
    }

    /**
     * 根据用户ID列表查询用户监控规则设置列表
     * @param userIds 用户ID列表
     * @return 用户监控规则设置列表
     */
    public List<UserMonitorRuleSettingDO> queryUserMonitorRuleSettingsByUserIds(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<UserMonitorRuleSettingDO> queryWrapper = new LambdaQueryWrapper<UserMonitorRuleSettingDO>()
                .in(UserMonitorRuleSettingDO::getUserId, userIds)
                .orderByDesc(UserMonitorRuleSettingDO::getUpdateTime);
        return userMonitorRuleSettingMapper.selectList(queryWrapper);
    }

}
