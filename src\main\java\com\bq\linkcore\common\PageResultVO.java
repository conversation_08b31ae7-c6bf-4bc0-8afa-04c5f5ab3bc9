package com.bq.linkcore.common;

import com.bq.data.base.bean.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/24 14:48
 * @className UserInfoVO
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "用户信息对象")
public class PageResultVO<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "data")
    private List<T> data;

    @ApiModelProperty(value = "当前页，从1开始")
    private Integer pageNo;

    @ApiModelProperty(value = "每页显示条数")
    private Integer pageSize;

    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;

    private Boolean hasNextPage;

    public PageResultVO<T> initialize(Page page) {
        this.pageNo = page.getPageNo();
        this.pageSize = page.getPageSize();
        this.data = Collections.emptyList();
        this.totalCount = 0;
        return this;
    }

    public PageResultVO<T> initialize(int pageNo, int pageSize) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.data = Collections.emptyList();
        this.totalCount = 0;
        this.hasNextPage = false;
        return this;
    }

    private PageResultVO returnNullPage() {
        this.setTotalCount(0);
        this.setData(new ArrayList());
        return this;
    }
}