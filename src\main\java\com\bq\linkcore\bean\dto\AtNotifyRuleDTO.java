package com.bq.linkcore.bean.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AtNotifyRuleDTO {
    private Integer playCount = 10000;
    private Integer likeCount = 5000;
    private Integer commentCount = 2000;
    private Integer collectCount = 1000;
    private Integer shareCount = 1000;
    private Integer atFansIncr = 500;
}
