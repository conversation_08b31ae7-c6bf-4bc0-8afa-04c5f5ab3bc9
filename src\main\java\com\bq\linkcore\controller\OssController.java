package com.bq.linkcore.controller;

import com.bq.data.base.bean.BaseEnterpriseUser;
import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.vo.OSSUrlFileUploadReqVo;
import com.bq.linkcore.bean.vo.UserInfoVo;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ResponseMsg;
import com.bq.linkcore.common.ServiceException;
import com.bq.linkcore.services.IOssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.bq.linkcore.common.ResponseMsg.ERROR_NOT_USER_INFO;

/**
 * <AUTHOR>
 * @date 2025/7/21 1:28
 * @className OssController
 * @description
 */
@Api(value = "用户", tags = "添加用户接口")
@RestController
@Slf4j
@RequestMapping("/oss")
public class OssController {

    @Autowired
    private IOssService ossService;

    @ApiOperation(value = "上传oss", notes = "上传oss")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回信息", response = ResponseData.class)})
    @PostMapping(value = "/upload/url")
    public ResponseData uploadUrlFile(@Valid @RequestBody OSSUrlFileUploadReqVo vo) {
        try {
            return ossService.updateUrlFile(vo.getImageUrl());
        } catch (ServiceException e) {
            log.error(e.getMessage(), e);
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RD.fail();
        }
    }

}
