<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.AtTiktokAuthorWorkStatDailyMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.AtTiktokAuthorWorkStatDailyDO">
        <id column="id" property="id" />
        <result column="work_id" property="workId" />
        <result column="work_uuid" property="workUuid" />
        <result column="author_id" property="authorId" />
        <result column="unique_id" property="uniqueId" />
        <result column="sec_uid" property="secUid" />
        <result column="play_count" property="playCount" />
        <result column="like_count" property="likeCount" />
        <result column="comment_count" property="commentCount" />
        <result column="share_count" property="shareCount" />
        <result column="collect_count" property="collectCount" />
        <result column="stat_day" property="statDay" />
        <result column="stat_time" property="statTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, work_id, work_uuid, author_id, unique_id, sec_uid, play_count, like_count, comment_count, share_count, collect_count, stat_day, stat_time, create_time, update_time
    </sql>

  

  
</mapper>
