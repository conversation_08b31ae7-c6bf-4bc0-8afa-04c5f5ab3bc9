package com.bq.linkcore.bean.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/4/23 17:40
 * @className UserMemberInfo
 * @description
 */
@Data
public class UserMemberInfo {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 客户级别，0：普通用户；1：vip-1用户
     */
    private Integer level;

    /**
     * 最大监控达人数量
     */
    private Integer maxAuthorCount;

    /**
     * 统计的视频最大天数
     */
    private Integer maxVideoDays;

    /**
     * 监控视频任务最大数量
     */
    private Integer maxVideoMonitorCount;

    /**
     * 会员每月直播监控时长
     */
    private Integer liveMonitorDuration;

    /**
     * 扩展字段
     */
    private UserMemberExtendDTO userMemberExtendDTO;

    /**
     * 会员开始时间
     */
    private LocalDateTime startTime;

    /**
     * 会员结束时间
     */
    private LocalDateTime endTime;

}
