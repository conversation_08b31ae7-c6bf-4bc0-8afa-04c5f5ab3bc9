<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.AtTiktokUserProfileRecordMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.AtTiktokUserProfileRecordDO">
        <id column="id" property="id" />
        <result column="author_id" property="authorId" />
        <result column="avatar" property="avatar" />
        <result column="name" property="name" />
        <result column="desc" property="desc" />
        <result column="home_url" property="homeUrl" />
        <result column="company_name" property="companyName" />
        <result column="resource_cnt" property="resourceCnt" />
        <result column="like_count" property="likeCount" />
        <result column="fans_count" property="fansCount" />
        <result column="share_count" property="shareCount" />
        <result column="following_count" property="followingCount" />
        <result column="is_verified" property="isVerified" />
        <result column="registry_location" property="registryLocation" />
        <result column="register_time" property="registerTime" />
        <result column="sec_uid" property="secUid" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
        <result column="is_del" property="isDel" />
    </resultMap>
    <sql id="Base_Column_List">
        id, author_id, avatar, name, desc, home_url, company_name, resource_cnt, like_count, fans_count, share_count, following_count, is_verified, registry_location, register_time, sec_uid, creator, create_time, updater, update_time, is_del
    </sql>

  

  
</mapper>
