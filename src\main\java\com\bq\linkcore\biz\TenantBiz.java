 package com.bq.linkcore.biz;

 import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
 import com.bq.linkcore.bean.entity.TenantDO;
 import com.bq.linkcore.dao.mapper.TenantMapper;
 import org.springframework.stereotype.Component;

 import javax.annotation.Resource;
 import java.util.List;

 @Component
public class TenantBiz {

    @Resource
    private TenantMapper tenantMapper;


    public int insertTenant(TenantDO tenantDO){
        return tenantMapper.insert(tenantDO);
    }

    public int updateTenant(TenantDO tenantDO){
        return tenantMapper.updateById(tenantDO);
    }

    public TenantDO queryTenantByCode(String code){
        LambdaQueryWrapper<TenantDO> queryWrapper = new LambdaQueryWrapper<TenantDO>()
                .eq(TenantDO::getCode, code)
                .eq(TenantDO::getIsDel, 0);
        return tenantMapper.selectOne(queryWrapper);
    }

    public int queryTenantByName(String name){
        LambdaQueryWrapper<TenantDO> queryWrapper = new LambdaQueryWrapper<TenantDO>()
                .eq(TenantDO::getName, name)
                .eq(TenantDO::getIsDel, 0);
        return tenantMapper.selectCount(queryWrapper);
    }

    public List<TenantDO> queryTenantSearch(String tenantName){
        LambdaQueryWrapper<TenantDO> queryWrapper = new LambdaQueryWrapper<TenantDO>()
                .like(TenantDO::getName, tenantName)
                .eq(TenantDO::getIsDel, 0);
        return tenantMapper.selectList(queryWrapper);
    }
}
