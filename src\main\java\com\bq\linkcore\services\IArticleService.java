package com.bq.linkcore.services;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.vo.WorkDashboardRespVo;
import com.bq.linkcore.bean.vo.WorkInfoVo;

public interface IArticleService {

    /**
     * 查询作品数据信息
     * @param userId
     * @param workId
     * @param atUniqueId
     * @return
     */
    WorkDashboardRespVo queryTxArticleInfo(Long userId, String workId, String atUniqueId);

    /**
     * 同步作品数据
     * @param userId
     * @param workId
     * @param atUniqueId
     * @return
     */
    ResponseData syncArticleInfo(Long userId, String workId, String atUniqueId, Integer isDaily);

}
