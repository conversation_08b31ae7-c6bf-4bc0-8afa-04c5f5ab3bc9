package com.bq.linkcore.controller;

import com.bq.data.base.bean.BaseEnterpriseUser;
import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.vo.UserPageRequestVo;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ResponseMsg;
import com.bq.linkcore.common.ServiceException;
import com.bq.linkcore.services.IUserAuthorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.bq.linkcore.common.ResponseMsg.ERROR_UNKNOWN_TOKEN;
import static com.bq.linkcore.common.ResponseMsg.FAIL;

@Api(value = "用户", tags = "添加用户接口")
@RestController
@Slf4j
@RequestMapping("/monitor/author")
public class UserAuthorController extends BaseController {

    @Resource
    private IUserAuthorService userAuthorService;


    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "返回消息类", response = ResponseData.class)
    })
    @GetMapping(value = "/search")
    public ResponseData authorSearch(@RequestParam("homeUrl") String homeUrl) {
        try {
            log.info("/monitor/author, vo={}", homeUrl);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return userAuthorService.searchAuthor(baseUser.getId(), homeUrl);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return RD.fail(ResponseMsg.ERROR_PHONE_MESSAGE.getCode());
    }


    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "返回消息类", response = ResponseData.class)
    })
    @GetMapping(value = "/add")
    public ResponseData authorAdd(@RequestParam("atUniqueId") String atUniqueId) {
        try {
            log.info("/monitor/author/add, vo={}", atUniqueId);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return userAuthorService.addTxAuthorMonitor(baseUser.getId(), atUniqueId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return RD.fail();
    }


    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "返回消息类", response = ResponseData.class)
    })
    @PostMapping(value = "/list")
    public ResponseData authorList(@RequestBody UserPageRequestVo vo) {
        try {
            log.info("/monitor/author/list");
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return userAuthorService.queryAccountMonitorInCrList(baseUser.getId(), vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RD.fail();
        }

    }

    @ApiOperation("删除用户监控")
    @GetMapping("/remove")
    public ResponseData remove(@RequestParam("atUniqueId") String atUniqueId) {
        try {
            BaseEnterpriseUser user = getTokenBaseUser();
            if (user == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN);
            }

            return userAuthorService.removeTxMonitor(user.getId(), atUniqueId);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }

    @ApiOperation("更新达人账户数据")
    @GetMapping("/update")
    public ResponseData update(@RequestParam("atUniqueId") String atUniqueId) {
        try {
            BaseEnterpriseUser user = getTokenBaseUser();
            if (user == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN);
            }

            return userAuthorService.updateTxAuthor(user.getId(), atUniqueId);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }

    @ApiOperation("查询达人账号的更新记录及当前更新状态")
    @GetMapping("/query/update/info")
    public ResponseData queryUpdateInfo(@RequestParam("atUniqueId") String atUniqueId) {
        try {
            BaseEnterpriseUser user = getTokenBaseUser();
            if (user == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN);
            }

            return userAuthorService.queryUpdateInfo(user.getId(), atUniqueId);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }


}
