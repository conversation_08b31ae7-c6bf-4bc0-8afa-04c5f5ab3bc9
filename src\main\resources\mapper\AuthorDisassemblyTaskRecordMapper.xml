<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.AuthorDisassemblyTaskRecordMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.AuthorDisassemblyTaskRecordDO">
        <id column="id" property="id" />
        <result column="author_id" property="authorId" />
        <result column="unique_id" property="uniqueId" />
        <result column="task_id" property="taskId" />
        <result column="task_status" property="taskStatus" />
        <result column="res_json" property="resJson" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
        <result column="is_del" property="isDel" />
    </resultMap>
    <sql id="Base_Column_List">
        id, author_id, unique_id, task_id, task_status, res_json, creator, create_time, updater, update_time, is_del
    </sql>

  

  
</mapper>
