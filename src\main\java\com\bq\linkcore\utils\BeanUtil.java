package com.bq.linkcore.utils;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cglib.beans.BeanMap;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/19 23:27
 * @className BeanUtil
 * @description
 */
@Slf4j
public class BeanUtil {

    private static ObjectMapper mapper = new ObjectMapper(){{configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);}};

    /**
     * 将map装换为javabean对象
     *
     * @param map
     * @param bean
     * @return
     */
    public static <T> T mapToBean(Map<String, Object> map, T bean) {
        try {
            BeanMap beanMap = BeanMap.create(bean);
            beanMap.putAll(map);
            return bean;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 将map装换为javabean对象
     *
     * @param str
     * @param bean
     * @return
     */
    public static <T> T stringToBean(String str, T bean) {
        Map<String, Object> map = new HashMap<>();
        map = (Map<String, Object>) JSONUtils.parse(str);
        BeanMap beanMap = BeanMap.create(bean);
        beanMap.putAll(map);
        return bean;
    }

    /**
     * jsonobject/map转为指定类型对象
     * @param source
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T convert(Object source, Class<T> clazz) {
        T result = mapper.convertValue(source, clazz);
        return result;
    }

    public static void main(String[] args) {
        String data = "{\"cost\": \"177.689ms\",\"textProb\": 1.0,\"aspectItem\": [{\"clause\": \"环境 很干净 很舒服 整体装修很暖 环境也不是很嘈杂 服务 服务比较及时 听服务员说他是刚转正 但是一点看的也不像新手 服务一点也没有怠慢外面 万达巴奴自助小料台很干净 水果补充及时 中间没有断过 水果还很新鲜 点了他们家的虾滑 \",\"clauseIndex\": \"0,115\",\"aspectPolarity\": \"正\",\"terms\": [{\"aspectTerm\": \"环境\",\"opinionTerm\": \"不是很嘈杂\",\"normedAspectTerm\": \"环境\",\"normedOpinionTerm\": \"不是很嘈杂\"}, {\"aspectTerm\": \"小料台\",\"opinionTerm\": \"很干净\",\"normedAspectTerm\": \"小料台\",\"normedOpinionTerm\": \"很干净\"}, {\"aspectTerm\": \"环境\",\"opinionTerm\": \"很干净\",\"normedAspectTerm\": \"环境\",\"normedOpinionTerm\": \"很干净\"}, {\"aspectTerm\": \"水果\",\"opinionTerm\": \"很新鲜\",\"normedAspectTerm\": \"水果\",\"normedOpinionTerm\": \"新鲜\"}],\"positiveProb\": 1.0,\"aspectCategory\": \"其他\",\"negativeProb\": 0.0}, {\"clause\": \"环境 很干净 很舒服 整体装修很暖 环境也不是很嘈杂 服务 服务比较及时 听服务员说他是刚转正 但是一点看的也不像新手 服务一点也没有怠慢外面 万达巴奴自助小料台很干净 水果补充及时 中间没有断过 水果还很新鲜 点了他们家的虾滑 \",\"clauseIndex\": \"0,115\",\"aspectPolarity\": \"正\",\"terms\": [{\"aspectTerm\": \"环境\",\"opinionTerm\": \"很舒服\",\"normedAspectTerm\": \"环境\",\"normedOpinionTerm\": \"很舒服\"}],\"positiveProb\": 1.0,\"aspectCategory\": \"舒适度\",\"negativeProb\": 0.0}, {\"clause\": \"环境 很干净 很舒服 整体装修很暖 环境也不是很嘈杂 服务 服务比较及时 听服务员说他是刚转正 但是一点看的也不像新手 服务一点也没有怠慢外面 万达巴奴自助小料台很干净 水果补充及时 中间没有断过 水果还很新鲜 点了他们家的虾滑 \",\"clauseIndex\": \"0,115\",\"aspectPolarity\": \"正\",\"terms\": [{\"aspectTerm\": \"服务\",\"opinionTerm\": \"比较及时\",\"normedAspectTerm\": \"客服\",\"normedOpinionTerm\": \"很及时\"}, {\"aspectTerm\": \"服务\",\"opinionTerm\": \"没有怠慢\",\"normedAspectTerm\": \"客服\",\"normedOpinionTerm\": \"没有怠慢\"}, {\"aspectTerm\": \"补充\",\"opinionTerm\": \"及时\",\"normedAspectTerm\": \"补充\",\"normedOpinionTerm\": \"很及时\"}],\"positiveProb\": 1.0,\"aspectCategory\": \"卖家服务\",\"negativeProb\": 0.0}],\"textPolarity\": \"正\"}";
        JSONObject object = JSON.parseObject(data);


//        PredictResult predictResult = BeanUtil.convert(object, PredictResult.class);

//        System.out.println(predictResult);
    }
}
