<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.AtAccountUserSettingMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.AtAccountUserSettingDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="at_unique_id" property="atUniqueId" />
        <result column="home_url" property="homeUrl" />
        <result column="nickname" property="nickname" />
        <result column="platform" property="platform" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
        <result column="is_del" property="isDel" />
    </resultMap>
    <sql id="Base_Column_List">
        id, user_id, at_unique_id, home_url, nickname, platform, creator, create_time, updater, update_time, is_del
    </sql>

  

  
</mapper>
