package com.bq.linkcore.config;

import com.bq.data.base.bean.ResponseData;
import com.bq.data.base.exception.SystemException;
import com.bq.linkcore.common.ResponseMsg;
import com.bq.linkcore.common.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/18 16:24
 * @className ControllerExceptionHandler
 * @description
 */
@RestControllerAdvice
@Slf4j
public class ControllerExceptionHandler {

    @ExceptionHandler(Exception.class)
    public ResponseData exceptionHandler(Exception exception) {
        exception.printStackTrace();
        if (exception instanceof BindException) {
            BindException bindException = (BindException) exception;
            List<ObjectError> errorList = bindException.getBindingResult().getAllErrors();
            StringBuffer errorInfo = new StringBuffer();
            for (ObjectError oe : errorList) {
                errorInfo.append(oe.getDefaultMessage());
            }
            return new ResponseData(ResponseMsg.FAIL.getCode(), exchangeMsg(errorInfo.toString()));
        } else if (exception instanceof SystemException) {
            SystemException systemException = (SystemException) exception;
            return new ResponseData(systemException.getCode(), exchangeMsg(systemException.getMessage()));
        } else if (exception instanceof SQLException) {
            return new ResponseData(ResponseMsg.FAIL.getCode(), "SQL异常！");
        } else if (exception instanceof ServiceException) {
            ServiceException serviceException = (ServiceException) exception;
            return new ResponseData(serviceException.getErrorCode(), exchangeMsg(serviceException.getMessage()));
        }
        log.error("RuntimeException error", exception);
        return new ResponseData(ResponseMsg.FAIL.getCode(), exchangeMsg(exception.getMessage()));
    }

    @ExceptionHandler(RuntimeException.class)
    public ResponseData runtimeExceptionHandler(RuntimeException runtimeException) {
        log.error(runtimeException.getMessage(), runtimeException);
        if (runtimeException instanceof SystemException) {
            SystemException systemException = (SystemException) runtimeException;
            return new ResponseData(systemException.getCode(), exchangeMsg(systemException.getMessage()));
        } else if (runtimeException instanceof DataAccessException) {
            return new ResponseData(ResponseMsg.FAIL.getCode(), exchangeMsg(runtimeException.getMessage()));
        } else if (runtimeException instanceof NullPointerException) {
            return new ResponseData(ResponseMsg.FAIL.getCode(), "业务空异常！");
        } else if (runtimeException instanceof ServiceException) {
            ServiceException serviceException = (ServiceException) runtimeException;
            return new ResponseData(serviceException.getErrorCode(), exchangeMsg(serviceException.getMessage()));
        }
        return new ResponseData(ResponseMsg.FAIL.getCode(), exchangeMsg(runtimeException.getMessage()));
    }

    private String exchangeMsg(String msg) {
        return msg.replaceAll("trade", "boxquant");
    }
}
