package com.bq.linkcore.client.tikhub.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/22 14:09
 * @className OneBoundXHSArticle
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TikHubArticleProduct {

    /**
     * 商品 ID
     */
    private String productId;

    /**
     * 商品关键字
     */
    private String keyword;

    /**
     * 商品类型编号（如 33）
     */
    private Integer productType;

    /**
     * title: 商品标题
     */
    private String title;

    /**
     * cover: 商品封面图标识符
     */
    private String cover;

    /**
     * img: 商品图片列表（多个图片标识符）
     */
    private List<String> imageList;

    /**
     * 市场价
     */
    private Double marketPrice;

    /**
     * 实际价格（如 0）
     */
    private Double price;

    /**
     * currency: 货币类型（如 "USD"）
     */
    private String currency;

    /**
     * url: 商品详情页 URL
     */
    private String url;

    /**
     * 商品状态（如 90 可能表示上架状态）
     */
    private Integer productStatus;

    /**
     * 是否在店铺中（如 false 表示不在）
     */
    private Integer inShop;

    /**
     * 商品来源（如 "TikTok Shop"）
     */
    private String source;

    /**
     * 卖家 ID
     */
    private String sellerId;

    /**
     * 广告标签（如 "#sponsored"）
     */
    private String adLabelName;

    /**
     * 平台标识（如 5 可能代表 TikTok Shop）
     */
    private Integer platform;

    private String categories;

    /**
     * 业务类型（如 0 表示普通商品）
     */
    private Integer bizType;

    /**
     * sku列表
     */
    private List<String> skuList;
}

