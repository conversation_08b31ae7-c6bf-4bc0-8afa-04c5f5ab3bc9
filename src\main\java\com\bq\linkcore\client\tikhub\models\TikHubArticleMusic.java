package com.bq.linkcore.client.tikhub.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/22 14:09
 * @className OneBoundXHSArticle
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TikHubArticleMusic {
    private String musicId;

    private String title;

    /**
     * 作者昵称
     */
    private String authorName;

    /**
     * 作者头像
     */
    private String authorAvatar;

    private Integer duration;

    /**
     * 版权状态：标识该音乐是否受版权保护
     *
     * 1：受版权保护（使用可能需授权/产生分成）
     * 0：无版权限制（可自由使用）
     */
    private Integer isCopyrighted;

    /**
     * music地址
     */
    private String url;

    /**
     * 创作来源：标识音乐是否为原创内容
     *
     * 1：由TikTok用户或合作音乐人原创
     * 0：翻唱/二次创作/平台曲库音乐
     */
    private Integer original;
}
