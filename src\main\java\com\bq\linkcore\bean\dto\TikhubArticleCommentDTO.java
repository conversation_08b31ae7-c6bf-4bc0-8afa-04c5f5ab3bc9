package com.bq.linkcore.bean.dto;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-20 04:39:36
 * @ClassName: TikhubArticleCommentDO
 * @Description: 文章评论表
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TikhubArticleCommentDTO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 作品id
     */
    private String workId;

    /**
     * 评论ID
     */
    private String commentId;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 评论语言（en,zh等）
     */
    private String commentLanguage;

    /**
     * 发布时间（秒级时间戳）
     */
    private Integer publishTime;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 评论者ID
     */
    private String commenterId;

    /**
     * 评论者名称
     */
    private String commenterName;

    /**
     * 评论者唯一ID
     */
    private String commenterUniqueId;

    /**
     * 评论者安全ID
     */
    private String commenterSecUid;

    /**
     * 评论人url
     */
    private String commenterUrl;

    /**
     * 评论者简介
     */
    private String commenterDesc;

    /**
     * 用户地区（ISO代码）
     */
    private String commenterRegion;

    /**
     * 头像URL
     */
    private String commenterAvatar;

    /**
     * 回复总数
     */
    private Integer replayCommentTotal;




}
