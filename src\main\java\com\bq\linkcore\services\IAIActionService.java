package com.bq.linkcore.services;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.vo.TranscriptVideoReqVo;
import com.bq.linkcore.bean.vo.TranscriptsRespVo;

public interface IAIActionService {

    /**
     * 文案提取
     * @param userId
     * @param vo
     * @return
     */
    ResponseData addTxVideoTranscriptsTask(Long userId, TranscriptVideoReqVo vo);

    /**
     * 查询文案提取任务
     * @param userId
     * @param vo
     * @return
     */
    ResponseData<TranscriptsRespVo> queryTxTranscriptsTask(Long userId, TranscriptVideoReqVo vo);


    /**
     * 添加账号拆解任务
     * @param userId
     * @param atUniqueId
     * @return
     */
    ResponseData addTxAccountDisassemblyTask(Long userId, String atUniqueId);

    /**
     * 查询账号拆解结果
     * @param userId
     * @param atUniqueId
     * @return
     */
    ResponseData queryTxAccountDisassemblyResult(Long userId, String atUniqueId);

}
