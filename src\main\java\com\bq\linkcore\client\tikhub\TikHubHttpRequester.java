package com.bq.linkcore.client.tikhub;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bq.linkcore.config.RemoteServiceConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2023/9/29 16:32
 * @className TikHubDouYinClient
 * @description
 */
@Slf4j
@Component
public class TikHubHttpRequester {
    @Resource(name = "commonRestTemplate")
    private RestTemplate restTemplate;

    /**
     * 通过网关调用服务
     *
     * @param url
     * @return
     */
    public JSONObject callGet(String url) {
        url = RemoteServiceConfig.getTikhubBaseUrl() + url;

        HttpHeaders headers = new HttpHeaders();

        MediaType type = MediaType.parseMediaType("application/json;charset=UTF-8");
        headers.setAccept(Collections.singletonList(type));
        headers.setBearerAuth("6p0MEmst+HkugHAX7XSqoUVvxgIWhozWj6tp4ib4mWFhqGtUdd4v9z+bXw==");
        headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        try {
            // 创建 HttpEntity 对象，包含请求头和请求体（如果需要）
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 发送 GET 请求
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    String.class
            );

            String body = response.getBody();
            log.info("TikHubXhsClient response={}", response);

            JSONObject object = JSON.parseObject(body);
            Integer code = object.getInteger("code");
            if (!code.equals(200)) {
                log.error("TikHubXhsClient 查询{}服务接口出错:", url);
                return null;
            }

            return object;
        } catch (Exception e) {
            log.error("TikHubXhsClient 调用AutoPaas服务接口出错:", e);
        }

        return null;
    }


    /**
     * 通过网关调用服务
     *
     * @param url
     * @param reqJson
     * @return
     */
    public void callPostNoResponse(String url, String reqJson) {
        url = RemoteServiceConfig.getTikhubBaseUrl() + url;

        HttpHeaders headers = new HttpHeaders();

        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());

        HttpEntity formEntity = new HttpEntity(reqJson, headers);

        try {
            String response = restTemplate.postForEntity(url, formEntity, String.class).getBody();

            log.info("http response={}", response);
        } catch (Exception e) {
            log.error("http 调用AutoPaas服务接口出错:", e);
        }

    }

}
