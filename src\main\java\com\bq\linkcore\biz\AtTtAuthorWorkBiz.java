package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.*;
import com.bq.linkcore.client.tikhub.models.TikHubArticleProduct;
import com.bq.linkcore.dao.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class AtTtAuthorWorkBiz {

    @Resource
    private AtTiktokAuthorWorkRecordMapper tiktokAuthorWorkRecordMapper;

    @Resource
    private AtTiktokAuthorWorkHistoryMapper tiktokAuthorWorkHistoryMapper;

    @Resource
    private TiktokArticleVideosMapper tiktokArticleVideosMapper;

    @Resource
    private TiktokArticleMusicsMapper tiktokArticleMusicsMapper;

    @Resource
    private TiktokArticleTagRelMapper tiktokArticleTagRelMapper;

    @Resource
    private TiktokProductRecordMapper tiktokProductRecordMapper;

    // ==================== AtTiktokAuthorWorkRecord 相关方法 ====================

    public int insertArticleVideo(TiktokArticleVideosDO videosDO) {
        return tiktokArticleVideosMapper.insert(videosDO);
    }

    public TiktokArticleVideosDO queryArticleVideo(String videoId) {
        LambdaQueryWrapper<TiktokArticleVideosDO> queryWrapper = new LambdaQueryWrapper<TiktokArticleVideosDO>()
                .eq(TiktokArticleVideosDO::getVideoId, videoId);

        return tiktokArticleVideosMapper.selectOne(queryWrapper);
    }

    public int insertArticleMusic(TiktokArticleMusicsDO musicsDO) {
        return tiktokArticleMusicsMapper.insert(musicsDO);
    }

    public TiktokArticleMusicsDO queryArticleMusic(String musicId) {
        LambdaQueryWrapper<TiktokArticleMusicsDO> queryWrapper = new LambdaQueryWrapper<TiktokArticleMusicsDO>()
                .eq(TiktokArticleMusicsDO::getMusicId, musicId);

        return tiktokArticleMusicsMapper.selectOne(queryWrapper);
    }

    /**
     * 插入TikTok作者作品记录
     *
     * @param authorWorkRecordDO TikTok作者作品记录实体对象
     * @return 插入影响的行数
     */
    public int insertAuthorWorkRecord(AtTiktokAuthorWorkRecordDO authorWorkRecordDO) {
        return tiktokAuthorWorkRecordMapper.insert(authorWorkRecordDO);
    }

    /**
     * 根据uniqueId查询TikTok作者作品记录列表
     *
     * @param uniqueId 用户名
     * @return TikTok作者作品记录列表
     */
    public List<AtTiktokAuthorWorkRecordDO> queryAuthorWorkRecordListByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO>()
                .eq(AtTiktokAuthorWorkRecordDO::getUniqueId, uniqueId)
                .orderByDesc(AtTiktokAuthorWorkRecordDO::getPublishTime);

        return tiktokAuthorWorkRecordMapper.selectList(queryWrapper);
    }

    /**
     * 根据workId查询TikTok作者作品记录
     *
     * @param workId 作品唯一标识
     * @return TikTok作者作品记录实体对象，不存在则返回null
     */
    public AtTiktokAuthorWorkRecordDO queryAuthorWorkRecordByWorkId(String workId) {
        LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO>()
                .eq(AtTiktokAuthorWorkRecordDO::getWorkId, workId);

        return tiktokAuthorWorkRecordMapper.selectOne(queryWrapper);
    }

    public AtTiktokAuthorWorkRecordDO queryAuthorWorkRecordByWorkIdAndUniqueId(String uniqueId, String workId) {
        LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO>()
                .eq(AtTiktokAuthorWorkRecordDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorWorkRecordDO::getWorkId, workId);

        return tiktokAuthorWorkRecordMapper.selectOne(queryWrapper);
    }


    /**
     * 根据uniqueId和workId查询记录是否存在
     *
     * @param uniqueId 用户名
     * @param workId   作品唯一标识
     * @return 记录数量，大于0表示存在
     */
    public int queryAuthorWorkRecordExistsByUniqueIdAndWorkId(String uniqueId, String workId) {
        LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO>()
                .eq(AtTiktokAuthorWorkRecordDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorWorkRecordDO::getWorkId, workId);

        return tiktokAuthorWorkRecordMapper.selectCount(queryWrapper);
    }

    /**
     * 根据ID更新TikTok作者作品记录
     *
     * @param authorWorkRecordDO TikTok作者作品记录实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateAuthorWorkRecord(AtTiktokAuthorWorkRecordDO authorWorkRecordDO) {
        return tiktokAuthorWorkRecordMapper.updateById(authorWorkRecordDO);
    }

    // ==================== AtTiktokAuthorWorkHistory 相关方法 ====================

    /**
     * 插入TikTok作者作品历史记录
     *
     * @param authorWorkHistoryDO TikTok作者作品历史记录实体对象
     * @return 插入影响的行数
     */
    public int insertAuthorWorkHistory(AtTiktokAuthorWorkHistoryDO authorWorkHistoryDO) {
        return tiktokAuthorWorkHistoryMapper.insert(authorWorkHistoryDO);
    }

    /**
     * 根据uniqueId和recordDay查询TikTok作者作品历史记录列表
     *
     * @param uniqueId  用户名
     * @param recordDay 数据获取日期
     * @return TikTok作者作品历史记录列表
     */
    public List<AtTiktokAuthorWorkHistoryDO> queryAuthorWorkHistoryListByUniqueIdAndRecordDay(String uniqueId, String recordDay) {
        LambdaQueryWrapper<AtTiktokAuthorWorkHistoryDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkHistoryDO>()
                .eq(AtTiktokAuthorWorkHistoryDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorWorkHistoryDO::getRecordDay, recordDay)
                .orderByDesc(AtTiktokAuthorWorkHistoryDO::getPublishTime);

        return tiktokAuthorWorkHistoryMapper.selectList(queryWrapper);
    }

    /**
     * 根据uniqueId查询TikTok作者作品历史记录列表
     *
     * @param uniqueId 用户名
     * @return TikTok作者作品历史记录列表
     */
    public List<AtTiktokAuthorWorkHistoryDO> queryAuthorWorkHistoryListByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorWorkHistoryDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkHistoryDO>()
                .eq(AtTiktokAuthorWorkHistoryDO::getUniqueId, uniqueId)
                .orderByDesc(AtTiktokAuthorWorkHistoryDO::getRecordDay)
                .orderByDesc(AtTiktokAuthorWorkHistoryDO::getPublishTime);

        return tiktokAuthorWorkHistoryMapper.selectList(queryWrapper);
    }

    /**
     * 根据workId和recordDay查询TikTok作者作品历史记录
     *
     * @param workId    作品唯一标识
     * @param recordDay 数据获取日期
     * @return TikTok作者作品历史记录实体对象，不存在则返回null
     */
    public AtTiktokAuthorWorkHistoryDO queryAuthorWorkHistoryByWorkIdAndRecordDay(String workId, String recordDay) {
        LambdaQueryWrapper<AtTiktokAuthorWorkHistoryDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkHistoryDO>()
                .eq(AtTiktokAuthorWorkHistoryDO::getWorkId, workId)
                .eq(AtTiktokAuthorWorkHistoryDO::getRecordDay, recordDay);

        return tiktokAuthorWorkHistoryMapper.selectOne(queryWrapper);
    }

    /**
     * 根据uniqueId、workId和recordDay查询记录是否存在
     *
     * @param uniqueId  用户名
     * @param workId    作品唯一标识
     * @param recordDay 数据获取日期
     * @return 记录数量，大于0表示存在
     */
    public int queryAuthorWorkHistoryExistsByUniqueIdAndWorkIdAndRecordDay(String uniqueId, String workId, String recordDay) {
        LambdaQueryWrapper<AtTiktokAuthorWorkHistoryDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkHistoryDO>()
                .eq(AtTiktokAuthorWorkHistoryDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorWorkHistoryDO::getWorkId, workId)
                .eq(AtTiktokAuthorWorkHistoryDO::getRecordDay, recordDay);

        return tiktokAuthorWorkHistoryMapper.selectCount(queryWrapper);
    }

    /**
     * 根据ID更新TikTok作者作品历史记录
     *
     * @param authorWorkHistoryDO TikTok作者作品历史记录实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateAuthorWorkHistory(AtTiktokAuthorWorkHistoryDO authorWorkHistoryDO) {
        return tiktokAuthorWorkHistoryMapper.updateById(authorWorkHistoryDO);
    }

    // ==================== TiktokArticleTagRel 相关方法 ====================

    /**
     * 插入作品标签关系记录
     *
     * @param tagRelDO 作品标签关系实体对象
     * @return 插入影响的行数
     */
    public int insertArticleTagRel(TiktokArticleTagRelDO tagRelDO) {
        return tiktokArticleTagRelMapper.insert(tagRelDO);
    }

    /**
     * 批量插入作品标签关系记录
     *
     * @param tagRelList 作品标签关系实体对象列表
     * @return 插入成功的记录数
     */
    public int batchInsertArticleTagRel(List<TiktokArticleTagRelDO> tagRelList) {
        if (tagRelList == null || tagRelList.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (TiktokArticleTagRelDO tagRel : tagRelList) {
            try {
                int result = tiktokArticleTagRelMapper.insert(tagRel);
                if (result > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                // 记录日志但继续处理其他记录
                log.warn("插入作品标签关系失败, workId: {}, tag: {}", tagRel.getWorkId(), tagRel.getTag(), e);
            }
        }
        return successCount;
    }

    /**
     * 根据workId查询作品标签关系记录列表
     *
     * @param workId 作品唯一标识
     * @return 作品标签关系记录列表
     */
    public List<TiktokArticleTagRelDO> queryArticleTagRelListByWorkId(String workId) {
        LambdaQueryWrapper<TiktokArticleTagRelDO> queryWrapper = new LambdaQueryWrapper<TiktokArticleTagRelDO>()
                .eq(TiktokArticleTagRelDO::getWorkId, workId)
                .eq(TiktokArticleTagRelDO::getIsDel, 0)
                .orderByDesc(TiktokArticleTagRelDO::getCreateTime);
        return tiktokArticleTagRelMapper.selectList(queryWrapper);
    }

    /**
     * 根据workId和tag查询作品标签关系记录
     *
     * @param workId 作品唯一标识
     * @param tag    标签名称
     * @return 作品标签关系记录，不存在则返回null
     */
    public TiktokArticleTagRelDO queryArticleTagRelByWorkIdAndTag(String workId, String tag) {
        LambdaQueryWrapper<TiktokArticleTagRelDO> queryWrapper = new LambdaQueryWrapper<TiktokArticleTagRelDO>()
                .eq(TiktokArticleTagRelDO::getWorkId, workId)
                .eq(TiktokArticleTagRelDO::getTag, tag)
                .eq(TiktokArticleTagRelDO::getIsDel, 0);
        return tiktokArticleTagRelMapper.selectOne(queryWrapper);
    }

    /**
     * 根据workId和tag列表查询作品标签关系记录列表
     *
     * @param workId  作品唯一标识
     * @param tagList 标签名称列表
     * @return 作品标签关系记录列表
     */
    public List<TiktokArticleTagRelDO> queryArticleTagRelListByWorkIdAndTags(String workId, List<String> tagList) {
        if (tagList == null || tagList.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<TiktokArticleTagRelDO> queryWrapper = new LambdaQueryWrapper<TiktokArticleTagRelDO>()
                .eq(TiktokArticleTagRelDO::getWorkId, workId)
                .in(TiktokArticleTagRelDO::getTag, tagList)
                .eq(TiktokArticleTagRelDO::getIsDel, 0)
                .orderByDesc(TiktokArticleTagRelDO::getCreateTime);
        return tiktokArticleTagRelMapper.selectList(queryWrapper);
    }

    /**
     * 根据ID更新作品标签关系记录
     *
     * @param tagRelDO 作品标签关系实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateArticleTagRel(TiktokArticleTagRelDO tagRelDO) {
        return tiktokArticleTagRelMapper.updateById(tagRelDO);
    }

    /**
     * 根据workId删除作品标签关系记录（逻辑删除）
     *
     * @param workId 作品唯一标识
     * @return 删除影响的行数
     */
    public int deleteArticleTagRelByWorkId(String workId) {
        TiktokArticleTagRelDO updateDO = new TiktokArticleTagRelDO();
        updateDO.setIsDel(1);

        LambdaQueryWrapper<TiktokArticleTagRelDO> queryWrapper = new LambdaQueryWrapper<TiktokArticleTagRelDO>()
                .eq(TiktokArticleTagRelDO::getWorkId, workId)
                .eq(TiktokArticleTagRelDO::getIsDel, 0);

        return tiktokArticleTagRelMapper.update(updateDO, queryWrapper);
    }

    /**
     * 根据workId和tag删除作品标签关系记录（逻辑删除）
     *
     * @param workId 作品唯一标识
     * @param tag    标签名称
     * @return 删除影响的行数
     */
    public int deleteArticleTagRelByWorkIdAndTag(String workId, String tag) {
        TiktokArticleTagRelDO updateDO = new TiktokArticleTagRelDO();
        updateDO.setIsDel(1);

        LambdaQueryWrapper<TiktokArticleTagRelDO> queryWrapper = new LambdaQueryWrapper<TiktokArticleTagRelDO>()
                .eq(TiktokArticleTagRelDO::getWorkId, workId)
                .eq(TiktokArticleTagRelDO::getTag, tag)
                .eq(TiktokArticleTagRelDO::getIsDel, 0);

        return tiktokArticleTagRelMapper.update(updateDO, queryWrapper);
    }

    /**
     * 批量插入作品标签关系记录
     *
     * @param workId  作品唯一标识
     * @param tagList 标签名称列表
     * @return 插入成功的记录数
     */
    public int batchInsertArticleTagRelByWorkIdAndTags(String workId, List<String> tagList) {
        if (workId == null || workId.trim().isEmpty() || tagList == null || tagList.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (String tag : tagList) {
            if (tag == null || tag.trim().isEmpty()) {
                continue;
            }

            try {
                // 检查是否已存在相同的记录
                TiktokArticleTagRelDO existingRecord = queryArticleTagRelByWorkIdAndTag(workId, tag.trim());
                if (existingRecord != null) {
                    log.debug("作品标签关系已存在，跳过插入, workId: {}, tag: {}", workId, tag.trim());
                    continue;
                }

                TiktokArticleTagRelDO tagRelDO = TiktokArticleTagRelDO.builder()
                        .workId(workId)
                        .tag(tag.trim())
                        .createTime(java.time.LocalDateTime.now())
                        .isDel(0)
                        .build();

                int result = tiktokArticleTagRelMapper.insert(tagRelDO);
                if (result > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                log.warn("插入作品标签关系失败, workId: {}, tag: {}", workId, tag, e);
            }
        }
        return successCount;
    }

    /**
     * 批量删除作品标签关系记录（逻辑删除）
     *
     * @param workId 作品唯一标识
     * @return 删除影响的行数
     */
    public int batchDeleteArticleTagRelByWorkId(String workId) {
        if (workId == null || workId.trim().isEmpty()) {
            return 0;
        }

        TiktokArticleTagRelDO updateDO = new TiktokArticleTagRelDO();
        updateDO.setIsDel(1);

        LambdaQueryWrapper<TiktokArticleTagRelDO> queryWrapper = new LambdaQueryWrapper<TiktokArticleTagRelDO>()
                .eq(TiktokArticleTagRelDO::getWorkId, workId)
                .eq(TiktokArticleTagRelDO::getIsDel, 0);

        return tiktokArticleTagRelMapper.update(updateDO, queryWrapper);
    }

    /**
     * 查询作品是否存在标签关系记录
     *
     * @param workId 作品唯一标识
     * @return true-存在记录，false-不存在记录
     */
    public boolean existsArticleTagRelByWorkId(String workId) {
        if (workId == null || workId.trim().isEmpty()) {
            return false;
        }

        LambdaQueryWrapper<TiktokArticleTagRelDO> queryWrapper = new LambdaQueryWrapper<TiktokArticleTagRelDO>()
                .eq(TiktokArticleTagRelDO::getWorkId, workId)
                .eq(TiktokArticleTagRelDO::getIsDel, 0)
                .last("LIMIT 1");

        return tiktokArticleTagRelMapper.selectCount(queryWrapper) > 0;
    }


    /**
     * 根据workId列表查询标签关联记录
     *
     * @param workIds 作品ID列表
     * @return 标签关联记录列表
     */
    public List<TiktokArticleTagRelDO> queryArticleTagRelListByWorkIds(List<String> workIds) {
        if (workIds == null || workIds.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<TiktokArticleTagRelDO> queryWrapper = new LambdaQueryWrapper<TiktokArticleTagRelDO>()
                .in(TiktokArticleTagRelDO::getWorkId, workIds)
                .eq(TiktokArticleTagRelDO::getIsDel, 0);

        return tiktokArticleTagRelMapper.selectList(queryWrapper);
    }

    /**
     * 根据musicId列表查询音乐详情列表
     *
     * @param musicIds 音乐ID列表
     * @return 音乐详情列表
     */
    public List<TiktokArticleMusicsDO> queryArticleMusicListByMusicIds(List<String> musicIds) {
        if (musicIds == null || musicIds.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<TiktokArticleMusicsDO> queryWrapper = new LambdaQueryWrapper<TiktokArticleMusicsDO>()
                .in(TiktokArticleMusicsDO::getMusicId, musicIds);

        return tiktokArticleMusicsMapper.selectList(queryWrapper);
    }

    /**
     * 根据uniqueId查询该达人的所有不重复标签
     *
     * @param uniqueId 用户唯一ID
     * @return 不重复的标签列表
     */
    public List<String> queryDistinctTagsByUniqueId(String uniqueId) {
        if (uniqueId == null || uniqueId.trim().isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // 1. 先获取该达人的所有作品ID
            List<AtTiktokAuthorWorkRecordDO> workRecordList = queryAuthorWorkRecordListByUniqueId(uniqueId);
            if (workRecordList == null || workRecordList.isEmpty()) {
                return new ArrayList<>();
            }

            // 2. 提取所有作品ID
            List<String> workIds = workRecordList.stream()
                    .map(AtTiktokAuthorWorkRecordDO::getWorkId)
                    .filter(workId -> workId != null && !workId.trim().isEmpty())
                    .collect(java.util.stream.Collectors.toList());

            if (workIds.isEmpty()) {
                return new ArrayList<>();
            }

            // 3. 查询这些作品的所有标签关系
            LambdaQueryWrapper<TiktokArticleTagRelDO> queryWrapper = new LambdaQueryWrapper<TiktokArticleTagRelDO>()
                    .in(TiktokArticleTagRelDO::getWorkId, workIds)
                    .eq(TiktokArticleTagRelDO::getIsDel, 0)
                    .select(TiktokArticleTagRelDO::getTag);

            List<TiktokArticleTagRelDO> tagRelList = tiktokArticleTagRelMapper.selectList(queryWrapper);

            // 4. 提取并去重标签
            return tagRelList.stream()
                    .map(TiktokArticleTagRelDO::getTag)
                    .filter(tag -> tag != null && !tag.trim().isEmpty())
                    .distinct()
                    .sorted()
                    .collect(java.util.stream.Collectors.toList());

        } catch (Exception e) {
            log.error("查询达人标签失败, uniqueId: {}", uniqueId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据条件分页查询作品记录列表（使用PageHelper）
     *
     * @param uniqueId   用户名
     * @param startTime  开始时间戳
     * @param endTime    结束时间戳
     * @param topicTag   话题标签
     * @param musicId    音乐ID
     * @param search     搜索关键词
     * @param sortFields 排序字段 (0:发布时间, 1:点赞数, 2:评论数, 3:转发数, 4:收藏数)
     * @param sortOrder  排序方式 (0:倒序, 1:正序)
     * @return 作品记录列表
     */
    public List<AtTiktokAuthorWorkRecordDO> queryAuthorWorkRecordListWithFilters(
            String uniqueId, Long startTime, Long endTime, String topicTag, String musicId,
            String search, Integer sortFields, Integer sortOrder) {

        // 构建查询条件
        LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO>()
                .eq(AtTiktokAuthorWorkRecordDO::getUniqueId, uniqueId);

        // 时间范围筛选
        if (startTime != null) {
            queryWrapper.ge(AtTiktokAuthorWorkRecordDO::getPublishTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(AtTiktokAuthorWorkRecordDO::getPublishTime, endTime);
        }

        // 音乐ID筛选
        if (musicId != null && !musicId.trim().isEmpty()) {
            queryWrapper.eq(AtTiktokAuthorWorkRecordDO::getMusicId, musicId);
        }

        // 搜索关键词筛选（标题或内容）
        if (search != null && !search.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                    .like(AtTiktokAuthorWorkRecordDO::getTitle, search)
                    .or()
                    .like(AtTiktokAuthorWorkRecordDO::getContent, search)
            );
        }

        // 话题标签筛选（需要特殊处理JSON字段）
        if (topicTag != null && !topicTag.trim().isEmpty()) {
            queryWrapper.like(AtTiktokAuthorWorkRecordDO::getHashtags, topicTag);
        }

        // 排序处理
        applySorting(queryWrapper, sortFields, sortOrder);

        return tiktokAuthorWorkRecordMapper.selectList(queryWrapper);
    }

    /**
     * 应用排序条件
     *
     * @param queryWrapper 查询包装器
     * @param sortFields   排序字段 (0:发布时间, 1:点赞数, 2:评论数, 3:转发数, 4:收藏数)
     * @param sortOrder    排序方式 (0:倒序, 1:正序)
     */
    private void applySorting(LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO> queryWrapper,
                              Integer sortFields, Integer sortOrder) {
        boolean isAsc = sortOrder != null && sortOrder == 1;

        if (sortFields == null) {
            sortFields = 0; // 默认按发布时间排序
        }

        switch (sortFields) {
            case 0: // 发布时间
                if (isAsc) {
                    queryWrapper.orderByAsc(AtTiktokAuthorWorkRecordDO::getPublishTime);
                } else {
                    queryWrapper.orderByDesc(AtTiktokAuthorWorkRecordDO::getPublishTime);
                }
                break;
            case 1: // 点赞数
                if (isAsc) {
                    queryWrapper.orderByAsc(AtTiktokAuthorWorkRecordDO::getLikeCount);
                } else {
                    queryWrapper.orderByDesc(AtTiktokAuthorWorkRecordDO::getLikeCount);
                }
                break;
            case 2: // 评论数
                if (isAsc) {
                    queryWrapper.orderByAsc(AtTiktokAuthorWorkRecordDO::getCommentCount);
                } else {
                    queryWrapper.orderByDesc(AtTiktokAuthorWorkRecordDO::getCommentCount);
                }
                break;
            case 3: // 转发数
                if (isAsc) {
                    queryWrapper.orderByAsc(AtTiktokAuthorWorkRecordDO::getShareCount);
                } else {
                    queryWrapper.orderByDesc(AtTiktokAuthorWorkRecordDO::getShareCount);
                }
                break;
            case 4: // 收藏数
                if (isAsc) {
                    queryWrapper.orderByAsc(AtTiktokAuthorWorkRecordDO::getCollectCount);
                } else {
                    queryWrapper.orderByDesc(AtTiktokAuthorWorkRecordDO::getCollectCount);
                }
                break;
            default:
                // 默认按发布时间倒序
                queryWrapper.orderByDesc(AtTiktokAuthorWorkRecordDO::getPublishTime);
                break;
        }
    }

    public void batchInsertOrUpdateProduct(List<TikHubArticleProduct> tikHubArticleProductList) {
        List<String> productIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tikHubArticleProductList)) {
            tikHubArticleProductList.forEach(tikHubArticleProduct -> {
                try {
                    String productId = tikHubArticleProduct.getProductId();
                    productIds.add(productId);

                    if (existsTiktokProduct(tikHubArticleProduct.getProductId())) {
                        TiktokProductRecordDO productRecordDO = buildTiktokProductRecordDO(tikHubArticleProduct);
                        insertTiktokProduct(productRecordDO);
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            });
        }
    }


    /**
     * 查询作品是否存在标签关系记录
     *
     * @param productId 作品唯一标识
     * @return true-存在记录，false-不存在记录
     */
    public boolean existsTiktokProduct(String productId) {
        if (productId == null || productId.trim().isEmpty()) {
            return false;
        }

        LambdaQueryWrapper<TiktokProductRecordDO> queryWrapper = new LambdaQueryWrapper<TiktokProductRecordDO>()
                .eq(TiktokProductRecordDO::getProductId, productId)
                .last("LIMIT 1");

        return tiktokProductRecordMapper.selectCount(queryWrapper) > 0;
    }

    public int insertTiktokProduct(TiktokProductRecordDO productRecordDO) {
        return tiktokProductRecordMapper.insert(productRecordDO);
    }

    private TiktokProductRecordDO buildTiktokProductRecordDO(TikHubArticleProduct tikHubArticleProduct) {
        String images = "";
        if (CollectionUtils.isNotEmpty(tikHubArticleProduct.getImageList())) {
            images = String.join(";", tikHubArticleProduct.getImageList());
        }

        String skus = "";
        if (CollectionUtils.isNotEmpty(tikHubArticleProduct.getSkuList())) {
            images = String.join(";", tikHubArticleProduct.getSkuList());
        }

        TiktokProductRecordDO tiktokProductRecordDO = TiktokProductRecordDO.builder()
                .productId(tikHubArticleProduct.getProductId())
                .keyword(tikHubArticleProduct.getKeyword())
                .productType(tikHubArticleProduct.getProductType())
                .title(tikHubArticleProduct.getTitle())
                .cover(tikHubArticleProduct.getCover())
                .images(images)
                .marketPrice(String.valueOf(tikHubArticleProduct.getMarketPrice()))
                .price(String.valueOf(tikHubArticleProduct.getPrice()))
                .currency(tikHubArticleProduct.getCurrency())
                .url(tikHubArticleProduct.getUrl())
                .productStatus(tikHubArticleProduct.getProductStatus())
                .inShop(tikHubArticleProduct.getInShop())
                .source(tikHubArticleProduct.getSource())
                .sellerUd(tikHubArticleProduct.getSellerId())
                .adlabelName(tikHubArticleProduct.getAdLabelName())
                .platform(tikHubArticleProduct.getPlatform())
                .categories(tikHubArticleProduct.getCategories())
                .bizType(tikHubArticleProduct.getBizType())
                .skus(skus)
                .creator(0L)
                .createTime(LocalDateTime.now())
                .updater(0L)
                .updateTime(LocalDateTime.now())
                .build();

        return tiktokProductRecordDO;
    }
}
