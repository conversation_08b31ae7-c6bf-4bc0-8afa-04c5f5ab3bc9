package com.bq.linkcore.services.impl.aiAction;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bq.linkcore.bean.entity.AtTiktokAuthorPoolDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorWorkRecordDO;
import com.bq.linkcore.biz.AtTtAuthorPoolBiz;
import com.bq.linkcore.biz.AtTtAuthorWorkBiz;
import com.bq.linkcore.services.impl.aiAction.models.*;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报告生成器的核心逻辑。
 */
@Slf4j
@Service
public class ReportGenerator {
    @Resource
    private QianwenClient qianwenClient;

    @Resource
    private AtTtAuthorPoolBiz authorPoolBiz;

    @Resource
    private AtTtAuthorWorkBiz authorWorkBiz;

    @Resource
    private Gson gson;


    /**
     * 模块1: 账号诊断
     */
    public ReportResultVo<AccountDiagnosisVo> runAccountDiagnosis(String uniqueId) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始执行账号诊断, uniqueId: {}", uniqueId);
            log.info("\n[模块1: 账号诊断] 正在获取数据...");

            AtTiktokAuthorPoolDO authorInfo = authorPoolBiz.queryAuthorPoolByUniqueId(uniqueId);
            if (authorInfo == null) {
                log.warn("未找到账号信息, uniqueId: {}", uniqueId);
                log.info("未找到账号信息，跳过账号诊断。");
                return ReportResultVo.failure("ACCOUNT_NOT_FOUND", "未找到账号信息",
                        System.currentTimeMillis() - startTime);
            }

            List<AtTiktokAuthorWorkRecordDO> allWorks = authorWorkBiz.queryAuthorWorkRecordListByUniqueId(uniqueId);
            if (allWorks == null || allWorks.isEmpty()) {
                log.warn("未找到作品数据, uniqueId: {}", uniqueId);
                log.info("该作者尚无作品数据，跳过账号诊断。");
                return ReportResultVo.failure("NO_WORKS_FOUND", "该作者尚无作品数据",
                        System.currentTimeMillis() - startTime);
            }

            LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
            List<AtTiktokAuthorWorkRecordDO> recentWorks = allWorks.stream()
                    .filter(work -> work.getPublishTime() != null &&
                            LocalDateTime.ofEpochSecond(work.getPublishTime(), 0, java.time.ZoneOffset.UTC)
                                    .isAfter(thirtyDaysAgo))
                    .collect(Collectors.toList());

            int recentVideoCount = recentWorks.size();
            int maxPlayCount = allWorks.stream()
                    .mapToInt(work -> work.getPlayCount() != null ? work.getPlayCount() : 0)
                    .max()
                    .orElse(0);

            int avgPlayCount = (int) allWorks.stream()
                    .mapToInt(work -> work.getPlayCount() != null ? work.getPlayCount() : 0)
                    .average()
                    .orElse(0);

            int avgLikeCount = (int) allWorks.stream()
                    .mapToInt(work -> work.getLikeCount() != null ? work.getLikeCount() : 0)
                    .average()
                    .orElse(0);

            // 4. 构建近期作品示例
            List<AccountDiagnosisVo.VideoSample> videoSamples = allWorks.stream()
                    .limit(10)
                    .map(work -> AccountDiagnosisVo.VideoSample.builder()
                            .title(StringUtils.hasText(work.getTitle()) ? work.getTitle() : "无标题")
                            .playCount(work.getPlayCount() != null ? work.getPlayCount() : 0)
                            .url(StringUtils.hasText(work.getUrl()) ? work.getUrl() : "")
                            .build())
                    .collect(Collectors.toList());

            // 5. 构建近期作品示例文本（用于AI分析）
            StringBuilder recentVideosText = new StringBuilder();
            videoSamples.forEach(video ->
                    recentVideosText.append(String.format("- %s (播放量: %d)\n", video.getTitle(), video.getPlayCount())));

            // 6. 调用大模型进行分析
            log.info("[模块1: 账号诊断] 正在调用大模型进行分析...");

            String systemPrompt = "作为一名顶级的短视频内容策略分析师";
            String userPrompt = String.join("\n",
                    String.format("请根据以下为一个TikTok账号（unique_id: %s）抓取的核心数据和近期作品示例，生成一份账号诊断报告。", uniqueId),
                    "",
                    "**账号核心数据:**",
                    String.format("- 作者昵称: %s", authorInfo.getAuthorName() != null ? authorInfo.getAuthorName() : "未知"),
                    String.format("- 个人简介: %s", authorInfo.getDesc() != null ? authorInfo.getDesc() : "无简介"),
                    String.format("- 粉丝数: %d", authorInfo.getFollowerCount() != null ? authorInfo.getFollowerCount() : 0),
                    String.format("- 总作品数: %d", authorInfo.getVideoCount() != null ? authorInfo.getVideoCount() : 0),
                    String.format("- 总获赞数: %d", authorInfo.getHeartCount() != null ? authorInfo.getHeartCount() : 0),
                    String.format("- 近30天发布作品数: %d", recentVideoCount),
                    String.format("- 历史最高单条播放量: %d", maxPlayCount),
                    "",
                    "**近期作品示例:**",
                    recentVideosText.toString(),
                    "",
                    "**任务要求:**",
                    "严格按照以下结构和要点进行分析，直接输出结果，不要包含额外说明：",
                    "- **提供价值**: (分析作者简介和近期作品内容，总结其为粉丝提供的核心价值)",
                    "- **发展阶段**: (根据粉丝数和近期更新频率，判断账号发展阶段)",
                    "- **核心竞争力**: (分析其最突出的优势，并用数据支撑)",
                    "- **发现问题**: (指出当前内容的明显短板，并提出具体改进建议)",
                    "- **可借鉴点**: (总结1-3个该账号最值得其他创作者学习的亮点)"
            );

            String diagnosisReport = qianwenClient.submitPrompt(systemPrompt, userPrompt);
            log.info("\n====================== 模块1: 账号诊断报告 ======================");
            log.info(diagnosisReport);
            log.info("=================================================================\n");

            // 7. 构建返回结果
            AccountDiagnosisVo.AccountBasicInfo basicInfo = AccountDiagnosisVo.AccountBasicInfo.builder()
                    .authorName(authorInfo.getAuthorName())
                    .description(authorInfo.getDesc())
                    .followerCount(authorInfo.getFollowerCount())
                    .videoCount(authorInfo.getVideoCount())
                    .heartCount(authorInfo.getHeartCount())
                    .build();

            AccountDiagnosisVo.AccountStatistics statistics = AccountDiagnosisVo.AccountStatistics.builder()
                    .recentVideoCount(recentVideoCount)
                    .maxPlayCount(maxPlayCount)
                    .avgPlayCount(avgPlayCount)
                    .avgLikeCount(avgLikeCount)
                    .build();

            AccountDiagnosisVo result = AccountDiagnosisVo.builder()
                    .uniqueId(uniqueId)
                    .accountInfo(basicInfo)
                    .statistics(statistics)
                    .diagnosisReport(diagnosisReport)
                    .recentVideos(videoSamples)
                    .build();

            log.info("账号诊断完成, uniqueId: {}", uniqueId);
            return ReportResultVo.success(result, System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            log.error("账号诊断异常, uniqueId: {}", uniqueId, e);
            System.err.println("模块1分析失败: " + e.getMessage());
            return ReportResultVo.failure("ANALYSIS_ERROR", "账号诊断分析失败: " + e.getMessage(),
                    System.currentTimeMillis() - startTime);
        }
    }

    /**
     * 模块2: 内容选题分析
     */
    public ReportResultVo<ContentAnalysisVo> runContentAnalysis(String uniqueId) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始执行内容选题分析, uniqueId: {}", uniqueId);
            log.info("[模块2: 内容选题分析] 正在获取数据...");

            // 1. 获取所有作品数据
            List<AtTiktokAuthorWorkRecordDO> allWorks = authorWorkBiz.queryAuthorWorkRecordListByUniqueId(uniqueId);
            if (allWorks == null || allWorks.isEmpty()) {
                log.warn("未找到作品数据, uniqueId: {}", uniqueId);
                log.info("该作者尚无作品数据，跳过内容选题分析。");
                return ReportResultVo.failure("NO_WORKS_FOUND", "该作者尚无作品数据",
                        System.currentTimeMillis() - startTime);
            }

            log.info("[模块2: 内容选题分析] 正在调用大模型进行分析...");

            // 2. 准备Markdown格式的作品数据（按播放量排序，取前50条）
            List<AtTiktokAuthorWorkRecordDO> topWorks = allWorks.stream()
                    .sorted(Comparator.comparing(AtTiktokAuthorWorkRecordDO::getPlayCount,
                            Comparator.nullsLast(Comparator.reverseOrder())))
                    .limit(50)
                    .collect(Collectors.toList());

            StringBuilder worksSampleMd = new StringBuilder();
            worksSampleMd.append("| title | hashtags | play_count | url |\n");
            worksSampleMd.append("|---|---|---|---|--|\n");

            for (AtTiktokAuthorWorkRecordDO work : topWorks) {
                String title = StringUtils.hasText(work.getTitle()) ? work.getTitle().replace("|", "\\|") : "无标题";
                String hashtags = StringUtils.hasText(work.getHashtags()) ? work.getHashtags() : "[]";
                int playCount = work.getPlayCount() != null ? work.getPlayCount() : 0;
                String url = StringUtils.hasText(work.getUrl()) ? work.getUrl() : "";
                worksSampleMd.append(String.format("| %s | %s | %d | %s |\n", title, hashtags, playCount, url));
            }

            // 3. 计算总播放量
            long totalPlays = allWorks.stream()
                    .mapToLong(work -> work.getPlayCount() != null ? work.getPlayCount().longValue() : 0L)
                    .sum();

            // 4. 构建大模型提示词
            String systemPrompt = "作为一名专业的短视频内容分析师";
            String userPrompt = String.join("\n",
                    String.format("请分析以下来自TikTok作者（unique_id: %s）的作品数据。", uniqueId),
                    "",
                    "**任务要求:**",
                    "1.  将所有作品**最多归纳为4个**主要的内容类型。",
                    String.format("2.  计算每个类型的**作品数量占比**和**贡献的播放量占比**（总播放量为 %d）。", totalPlays),
                    "3.  为每个类型撰写详细的分析，包括：内容定位、内容特点、典型选题和数据表现。",
                    "4.  为每个类型，从数据中找出**3个最相关**的视频URL作为示例。",
                    "5.  **必须严格按照指定的JSON格式**返回结果，不要添加任何额外的解释或Markdown标记。",
                    "",
                    "**作品数据 (播放量最高的50条):**",
                    worksSampleMd.toString(),
                    "",
                    "**输出JSON格式示例:**",
                    "[",
                    "  {",
                    "    \"成长认知类\": \"占比45%，贡献68%的播放量\",",
                    "    \"content\": \"内容定位：...\\n内容特点：...\\n典型选题：...\\n数据表现：...\",",
                    "    \"relatedVideos\": [\"https://...\",\"https://...\",\"https://...\"]",
                    "  },",
                    "  {",
                    "    \"教育育儿类\": \"占比25%，贡献22%的播放量\",",
                    "    \"content\": \"内容定位：...\\n内容特点：...\\n典型选题：...\\n数据表现：...\",",
                    "    \"relatedVideos\": [\"https://...\",\"https://...\",\"https://...\"]",
                    "  }",
                    "]"
            );

            // 5. 调用大模型分析
            String responseStr = qianwenClient.submitPrompt(systemPrompt, userPrompt);
            log.info("\n====================== 模块2: 内容选题分析 ======================");

            String jsonStr = null;
            List<ContentAnalysisVo.ContentType> contentTypes = new ArrayList<>();

            try {
                jsonStr = extractJson(responseStr, '[', ']');
                contentTypes = parseContentAnalysisJson(jsonStr);
                log.info("成功解析内容分析JSON，共" + contentTypes.size() + "个内容类型");

            } catch (Exception parseException) {
                log.warn("JSON解析失败, 使用原始文本, uniqueId: {}", uniqueId, parseException);
                System.err.println("警告：大模型未能返回有效的JSON格式。以下是原始输出：");
                System.err.println(parseException.getMessage());
            }

            log.info("=================================================================\n");

            // 6. 构建返回结果
            ContentAnalysisVo result = ContentAnalysisVo.builder()
                    .uniqueId(uniqueId)
                    .totalVideoCount(allWorks.size())
                    .totalPlayCount(totalPlays)
                    .contentTypes(contentTypes)
                    .analysisReportJson(jsonStr)
                    .analysisReportText(responseStr)
                    .build();

            log.info("内容选题分析完成, uniqueId: {}", uniqueId);
            return ReportResultVo.success(result, System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            log.error("内容选题分析异常, uniqueId: {}", uniqueId, e);
            System.err.println("内容选题分析失败: " + e.getMessage());
            return ReportResultVo.failure("ANALYSIS_ERROR", "内容选题分析失败: " + e.getMessage(),
                    System.currentTimeMillis() - startTime);
        }
    }

    /**
     * 模块3: 爆款解析
     */
    public ReportResultVo<HitVideoAnalysisVo> runHitVideoAnalysis(String uniqueId) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始执行爆款解析, uniqueId: {}", uniqueId);
            log.info("[模块3: 爆款解析] 正在获取数据...");

            // 1. 设置爆款阈值
            long hitThreshold = 10000;

            // 2. 获取所有作品并筛选爆款视频
            List<AtTiktokAuthorWorkRecordDO> allWorks = authorWorkBiz.queryAuthorWorkRecordListByUniqueId(uniqueId);
            if (allWorks == null || allWorks.isEmpty()) {
                log.warn("未找到作品数据, uniqueId: {}", uniqueId);
                log.info("该作者尚无作品数据，跳过爆款解析。");
                return ReportResultVo.failure("NO_WORKS_FOUND", "该作者尚无作品数据",
                        System.currentTimeMillis() - startTime);
            }

            List<AtTiktokAuthorWorkRecordDO> hitVideosData = allWorks.stream()
                    .filter(work -> work.getPlayCount() != null && work.getPlayCount() >= hitThreshold)
                    .sorted(Comparator.comparing(AtTiktokAuthorWorkRecordDO::getPlayCount,
                            Comparator.nullsLast(Comparator.reverseOrder())))
                    .collect(Collectors.toList());

            if (hitVideosData.isEmpty()) {
                log.info("未找到爆款视频, uniqueId: {}, threshold: {}", uniqueId, hitThreshold);
                log.info("该作者尚无播放量超过 " + hitThreshold + " 的爆款视频，跳过爆款解析。");
                return ReportResultVo.failure("NO_HIT_VIDEOS", "该作者尚无爆款视频",
                        System.currentTimeMillis() - startTime);
            }

            // 3. 构建爆款视频列表
            List<HitVideoAnalysisVo.HitVideo> hitVideos = hitVideosData.stream()
                    .map(work -> HitVideoAnalysisVo.HitVideo.builder()
                            .title(StringUtils.hasText(work.getTitle()) ? work.getTitle() : "无标题")
                            .playCount(work.getPlayCount() != null ? work.getPlayCount() : 0)
                            .url(StringUtils.hasText(work.getUrl()) ? work.getUrl() : "")
                            .likeCount(work.getLikeCount() != null ? work.getLikeCount() : 0)
                            .commentCount(work.getCommentCount() != null ? work.getCommentCount() : 0)
                            .shareCount(work.getShareCount() != null ? work.getShareCount() : 0)
                            .build())
                    .collect(Collectors.toList());

            // 4. 构建爆款视频文本（用于AI分析）
            log.info("[模块3: 爆款解析] 正在调用大模型进行分析...");
            String hitsText = hitVideos.stream()
                    .map(video -> String.format("- 标题: %s, 播放量: %d, URL: %s",
                            video.getTitle(), video.getPlayCount(), video.getUrl()))
                    .collect(Collectors.joining("\n"));

            // 5. 构建大模型提示词
            String systemPrompt = "作为一名顶级的爆款内容拆解师";
            String userPrompt = String.join("\n",
                    "请深入分析以下爆款视频数据。",
                    "任务要求:",
                    "1. 识别并归纳出这些爆款视频背后的核心爆款模型。",
                    "2. 为每种模型，描述名称、爆款特征、黄金3S和爆款模型。",
                    "3. 为每种模型，找出3个最符合的视频URL作为案例。",
                    "4. 必须严格按照指定的JSON格式返回结果，不含额外文本。",
                    "",
                    "**输出JSON格式示例:**",
                    "[",
                    "  {",
                    "    \"名称\": \"产品推荐\",",
                    "    \"爆款特征\": \"展示产品特点，分享使用体验，吸引观众购买兴趣。\",",
                    "    \"黄金3S\": \"在视频的前3秒内展示产品的最吸引人的特点或效果。\",",
                    "    \"爆款模型\": \"快速展示产品 -> 分享个人使用体验 -> 强调产品优势 -> 呼吁行动（如购买链接）。\",",
                    "    \"案例\": [\"https://www.tiktok.com/@example/video/1\", \"https://www.tiktok.com/@example/video/2\", \"https://www.tiktok.com/@example/video/3\"]",
                    "  }",
                    "]",
                    "",
                    "爆款视频数据:",
                    hitsText
            );

            // 6. 调用大模型分析
            String responseStr = qianwenClient.submitPrompt(systemPrompt, userPrompt);
            log.info("\n====================== 模块3: 爆款解析 ======================");

            String jsonStr = null;
            List<HitVideoAnalysisVo.HitModel> hitModels = new ArrayList<>();

            try {
                jsonStr = extractJson(responseStr, '[', ']');
                hitModels = parseHitVideoAnalysisJson(jsonStr);
                log.info("成功解析爆款视频分析JSON，共" + hitModels.size() + "个爆款模型");

            } catch (Exception parseException) {
                log.warn("JSON解析失败, 使用原始文本, uniqueId: {}", uniqueId, parseException);
                System.err.println("警告：大模型未能返回有效的JSON格式。以下是原始输出：");
                System.err.println(parseException.getMessage());
            }

            log.info("=================================================================\n");

            // 7. 构建返回结果
            HitVideoAnalysisVo result = HitVideoAnalysisVo.builder()
                    .uniqueId(uniqueId)
                    .hitThreshold(hitThreshold)
                    .hitVideoCount(hitVideos.size())
                    .hitModels(hitModels)
                    .analysisReportJson(jsonStr)
                    .analysisReportText(responseStr)
                    .hitVideos(hitVideos)
                    .build();

            log.info("爆款解析完成, uniqueId: {}, hitCount: {}", uniqueId, hitVideos.size());
            return ReportResultVo.success(result, System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            log.error("爆款解析异常, uniqueId: {}", uniqueId, e);
            System.err.println("模块3分析失败: " + e.getMessage());
            return ReportResultVo.failure("ANALYSIS_ERROR", "爆款解析失败: " + e.getMessage(),
                    System.currentTimeMillis() - startTime);
        }
    }


    /**
     * 新增模块4: 爆款脚本模版分析
     */
    public boolean runScriptTemplateAnalysis(String uniqueId) {
        log.info("\n[模块4: 爆款脚本模版分析] 正在获取数据...");
        long startTime = System.currentTimeMillis();
        
        // 1. 设置爆款阈值
        long hitThreshold = 10000;

        // 2. 获取所有作品并筛选爆款视频
        List<AtTiktokAuthorWorkRecordDO> hitVideos = authorWorkBiz.queryAuthorWorkRecordListByUniqueId(uniqueId);
        if (hitVideos == null || hitVideos.isEmpty()) {
            log.warn("未找到作品数据, uniqueId: {}", uniqueId);
            log.info("该作者尚无作品数据，跳过爆款解析。");
            return ReportResultVo.failure("NO_WORKS_FOUND", "该作者尚无作品数据",
                    System.currentTimeMillis() - startTime).getSuccess();
        }
        
        log.info("[模块4: 爆款脚本模版分析] 正在调用大模型进行分析...");
        String hitsText = hitVideos.stream()
                .map(v -> String.format("- 标题: %s, 播放量: %d, URL: %s", v.getTitle(), v.getPlayCount(), v.getUrl()))
                .collect(Collectors.joining("\n"));

        String systemPrompt = "作为一名顶级的短视频脚本策划专家";
        String userPrompt = String.join("\n",
                "请根据以下爆款视频数据，提炼出三种固定类型的可复用脚本模版：爆款容易、涨粉容易、互动容易。",
                "",
                "**任务要求:**",
                "1. 分析所有爆款视频，为“爆款容易”、“涨粉容易”、“互动容易”这三个固定类别，分别归纳出一种最典型的内容模版（例如“认知颠覆型”、“情感疗愈型”等）。",
                "2. 为每种模版，详细拆解其“适用场景”、“钩子设计”、“内容结构”、“情绪曲线”和“进阶技巧”。",
                "3. **必须严格按照指定的JSON格式**返回一个包含3个对象的数组，每个对象代表一种模版，不要添加任何额外的解释或Markdown标记。",
                "",
                "**爆款视频数据:**",
                hitsText,
                "",
                "**输出JSON格式示例:**",
                "[",
                "  {",
                "    \"type\": \"认知颠覆型（爆款容易）\",",
                "    \"type_feature\": \"适用场景：...\\n钩子设计：...\\n内容结构：...\\n情绪曲线：...\\n进阶技巧：...\",",
                "    \"relationWorks\": []",
                "  },",
                "  {",
                "    \"type\": \"情感疗愈型（涨粉容易）\",",
                "    \"type_feature\": \"适用场景：...\\n钩子设计：...\\n内容结构：...\\n情绪曲线：...\\n进阶技巧：...\",",
                "    \"relationWorks\": []",
                "  },",
                "  {",
                "    \"type\": \"热点解读型（互动容易）\",",
                "    \"type_feature\": \"适用场景：...\\n钩子设计：...\\n内容结构：...\\n情绪曲线：...\\n进阶技巧：...\",",
                "    \"relationWorks\": []",
                "  }",
                "]"
        );

        try {
            String responseStr = qianwenClient.submitPrompt(systemPrompt, userPrompt);
            log.info("\n====================== 模块4: 爆款脚本模版分析 ======================");
            String jsonStr = extractJson(responseStr, '[', ']');
            Object jsonObj = gson.fromJson(jsonStr, Object.class);
            
            log.info(gson.toJson(jsonObj));
            return true;
        } catch (Exception e) {
            System.err.println("模块4分析失败: " + e.getMessage());
            return false;
        }
    }


    /**
     * 从模型返回的字符串中稳健地提取JSON部分。
     */
    private String extractJson(String str, char startChar, char endChar) throws JsonSyntaxException {
        int startIndex = str.indexOf(startChar);
        int endIndex = str.lastIndexOf(endChar);
        if (startIndex == -1 || endIndex == -1 || endIndex < startIndex) {
            throw new JsonSyntaxException("无法在模型响应中找到有效的JSON。响应: " + str);
        }
        return str.substring(startIndex, endIndex + 1);
    }

    /**
     * 解析内容分析JSON，提取内容类型和相关视频
     *
     * @param jsonStr JSON字符串，格式如：
     * [{
     *   "美妆护肤类": "占比50%，贡献63%的播放量",
     *   "content": "内容定位：专注于美妆和护肤产品的评测、推荐和使用体验分享。\n内容特点：注重产品效果展示，强调实际使用感受和个人体验。\n典型选题：护肤品试用、美妆产品测评、护肤步骤分享等。\n数据表现：此类视频平均播放量较高，用户互动活跃。",
     *   "relatedVideos": ["https://www.tiktok.com/@mia_manhattan_ny/video/7525091112302693646"]
     * }]
     * @return 解析后的内容类型列表
     */
    private List<ContentAnalysisVo.ContentType> parseContentAnalysisJson(String jsonStr) {
        List<ContentAnalysisVo.ContentType> contentTypes = new ArrayList<>();

        // 使用FastJSON解析JSON数组
        JSONArray jsonArray = JSON.parseArray(jsonStr);

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject item = jsonArray.getJSONObject(i);

            // 找到第一个不是"content"和"relatedVideos"的键作为内容类型标题
            String contentTitle = null;
            String contentDescription = null;
            List<String> relatedVideos = new ArrayList<>();

            for (String key : item.keySet()) {
                Object value = item.get(key);

                if ("content".equals(key)) {
                    contentDescription = value != null ? value.toString() : "";
                } else if ("relatedVideos".equals(key)) {
                    if (value instanceof JSONArray) {
                        JSONArray videoArray = (JSONArray) value;
                        for (int j = 0; j < videoArray.size(); j++) {
                            Object video = videoArray.get(j);
                            if (video != null) {
                                relatedVideos.add(video.toString());
                            }
                        }
                    }
                } else {
                    // 这是内容类型的标题和占比信息
                    contentTitle = key + "：" + (value != null ? value.toString() : "");
                }
            }

            // 组合完整的内容描述
            String fullContent = "";
            if (contentTitle != null) {
                fullContent = contentTitle;
                if (contentDescription != null && !contentDescription.isEmpty()) {
                    fullContent += "\n" + contentDescription;
                }
            } else if (contentDescription != null) {
                fullContent = contentDescription;
            }

            ContentAnalysisVo.ContentType contentType = ContentAnalysisVo.ContentType.builder()
                    .content(fullContent)
                    .relatedVideos(relatedVideos)
                    .build();
            contentTypes.add(contentType);
        }

        return contentTypes;
    }

    /**
     * 解析爆款视频分析JSON，提取爆款模型信息
     *
     * @param jsonStr JSON字符串，格式如：
     * [{
     *   "名称": "产品推荐",
     *   "爆款特征": "展示产品特点，分享使用体验，吸引观众购买兴趣。",
     *   "黄金3S": "在视频的前3秒内展示产品的最吸引人的特点或效果。",
     *   "爆款模型": "快速展示产品 -> 分享个人使用体验 -> 强调产品优势 -> 呼吁行动（如购买链接）。",
     *   "案例": ["https://www.tiktok.com/@mia_manhattan_ny/video/7525091112302693646"]
     * }]
     * @return 解析后的爆款模型列表
     */
    private List<HitVideoAnalysisVo.HitModel> parseHitVideoAnalysisJson(String jsonStr) {
        List<HitVideoAnalysisVo.HitModel> hitModels = new ArrayList<>();

        // 使用FastJSON解析JSON数组
        JSONArray jsonArray = JSON.parseArray(jsonStr);

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject item = jsonArray.getJSONObject(i);

            // 提取各个字段
            String modelName = item.getString("名称");
            String features = item.getString("爆款特征");
            String golden3S = item.getString("黄金3S");
            String modelDescription = item.getString("爆款模型");

            // 组合完整的内容描述
            StringBuilder contentBuilder = new StringBuilder();
            if (modelName != null && !modelName.isEmpty()) {
                contentBuilder.append(modelName);
            }
            if (features != null && !features.isEmpty()) {
                contentBuilder.append("\n爆款特征：").append(features);
            }
            if (golden3S != null && !golden3S.isEmpty()) {
                contentBuilder.append("\n黄金3S：").append(golden3S);
            }
            if (modelDescription != null && !modelDescription.isEmpty()) {
                contentBuilder.append("\n爆款模型：").append(modelDescription);
            }

            // 提取案例视频
            List<String> relatedVideos = new ArrayList<>();
            JSONArray caseArray = item.getJSONArray("案例");
            if (caseArray != null) {
                for (int j = 0; j < caseArray.size(); j++) {
                    Object video = caseArray.get(j);
                    if (video != null) {
                        relatedVideos.add(video.toString());
                    }
                }
            }

            HitVideoAnalysisVo.HitModel hitModel = HitVideoAnalysisVo.HitModel.builder()
                    .content(contentBuilder.toString())
                    .relatedVideos(relatedVideos)
                    .build();
            hitModels.add(hitModel);
        }

        return hitModels;
    }

    /**
     * 执行完整的报告生成流程。
     */
    public CompleteReportVo generateCompleteReport(String uniqueId) {
        long totalStartTime = System.currentTimeMillis();

        try {
            log.info("开始生成完整报告, uniqueId: {}", uniqueId);
            log.info("开始为账号 " + uniqueId + " 生成分析报告...");

            // 执行各个模块
            ReportResultVo<AccountDiagnosisVo> accountResult = runAccountDiagnosis(uniqueId);
            ReportResultVo<ContentAnalysisVo> contentResult = runContentAnalysis(uniqueId);
            ReportResultVo<HitVideoAnalysisVo> hitVideoResult = runHitVideoAnalysis(uniqueId);

            // 计算执行摘要
            int successModules = 0;
            int totalModules = 3;
            StringBuilder executionDetails = new StringBuilder();

            if (accountResult.getSuccess()) {
                successModules++;
                executionDetails.append("账号诊断: 成功; ");
            } else {
                executionDetails.append("账号诊断: 失败(").append(accountResult.getErrorMessage()).append("); ");
            }

            if (contentResult.getSuccess()) {
                successModules++;
                executionDetails.append("内容分析: 成功; ");
            } else {
                executionDetails.append("内容分析: 失败(").append(contentResult.getErrorMessage()).append("); ");
            }

            if (hitVideoResult.getSuccess()) {
                successModules++;
                executionDetails.append("爆款分析: 成功; ");
            } else {
                executionDetails.append("爆款分析: 失败(").append(hitVideoResult.getErrorMessage()).append("); ");
            }

            double successRate = (double) successModules / totalModules * 100;

            CompleteReportVo.ExecutionSummary summary = CompleteReportVo.ExecutionSummary.builder()
                    .successModules(successModules)
                    .failedModules(totalModules - successModules)
                    .totalModules(totalModules)
                    .successRate(successRate)
                    .executionDetails(executionDetails.toString())
                    .build();

            // 确定报告状态
            CompleteReportVo.ReportStatus status;
            if (successModules == totalModules) {
                status = CompleteReportVo.ReportStatus.SUCCESS;
            } else if (successModules > 0) {
                status = CompleteReportVo.ReportStatus.PARTIAL_SUCCESS;
            } else {
                status = CompleteReportVo.ReportStatus.FAILED;
            }

            CompleteReportVo completeReport = CompleteReportVo.builder()
                    .uniqueId(uniqueId)
                    .generateTime(LocalDateTime.now())
                    .totalProcessingTime(System.currentTimeMillis() - totalStartTime)
                    .accountDiagnosis(accountResult)
                    .contentAnalysis(contentResult)
                    .hitVideoAnalysis(hitVideoResult)
                    .status(status)
                    .executionSummary(summary)
                    .build();

            log.info("报告生成完毕。");
            log.info("完整报告生成完成, uniqueId: {}, 成功率: {}%", uniqueId, successRate);

            return completeReport;

        } catch (Exception e) {
            log.error("完整报告生成异常, uniqueId: {}", uniqueId, e);
            System.err.println("报告生成失败: " + e.getMessage());

            // 返回失败的完整报告
            CompleteReportVo.ExecutionSummary summary = CompleteReportVo.ExecutionSummary.builder()
                    .successModules(0)
                    .failedModules(3)
                    .totalModules(3)
                    .successRate(0.0)
                    .executionDetails("报告生成异常: " + e.getMessage())
                    .build();

            return CompleteReportVo.builder()
                    .uniqueId(uniqueId)
                    .generateTime(LocalDateTime.now())
                    .totalProcessingTime(System.currentTimeMillis() - totalStartTime)
                    .status(CompleteReportVo.ReportStatus.FAILED)
                    .executionSummary(summary)
                    .build();
        }
    }

    /**
     * 兼容性方法 - 执行完整的报告生成流程（无返回值）
     */
    public void run(String uniqueId) {
        generateCompleteReport(uniqueId);
    }
}
