package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-16 18:00:20
 * @ClassName: TiktokArticleTagRelDO
 * @Description: 作品标签
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("tiktok_article_tag_rel")
public class TiktokArticleTagRelDO implements Serializable{

    private static final long serialVersionUID = 1L;

    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 视频ID
     */
    private String workId;

    /**
     * 视频标签
     */
    private String tag;

    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;

    /**
     * 是否被删除
     */
    private Integer isDel;




}
