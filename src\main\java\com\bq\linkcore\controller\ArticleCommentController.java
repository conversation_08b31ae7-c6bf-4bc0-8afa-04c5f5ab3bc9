package com.bq.linkcore.controller;

import com.bq.data.base.bean.BaseEnterpriseUser;
import com.bq.data.base.bean.Page;
import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.dto.TikhubArticleCommentDTO;
import com.bq.linkcore.bean.vo.UserPageRequestVo;
import com.bq.linkcore.bean.vo.WorkInfoVo;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ResponseMsg;
import com.bq.linkcore.common.ServiceException;
import com.bq.linkcore.services.IArticleCommentService;
import com.bq.linkcore.services.IUserAuthorService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.bq.linkcore.common.ResponseMsg.ERROR_UNKNOWN_TOKEN;
import static com.bq.linkcore.common.ResponseMsg.FAIL;

@Api(value = "作品评论接口", tags = "作品评论接口")
@RestController
@Slf4j
@RequestMapping("/article/comment")
public class ArticleCommentController extends BaseController {

    @Resource
    private IArticleCommentService articleCommentService;

    @ApiOperation(value = "评论列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回信息", response = TikhubArticleCommentDTO.class)})
    @GetMapping(value = "/list")
    public  ResponseData queryTxArticleInfo(
            @ApiParam(value = "达人唯一ID", required = true) @RequestParam("atUniqueId") String atUniqueId,
            @ApiParam(value = "作品唯一ID", required = true) @RequestParam("workId") String workId,
            @ApiParam(value = "分页下标", required = true) @RequestParam("pageNo") Integer pageNo ,
            @ApiParam(value = "分页大小", required = true) @RequestParam("pageSize") Integer pageSize
            )  {
        try {
            log.info("/article/comment/list, vo={}", atUniqueId);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return articleCommentService.queryTxCommentList(baseUser.getId(), atUniqueId,workId, pageNo, pageSize);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return RD.fail();
    }

    @ApiOperation(value = "开始评论更新任务")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回信息", response = TikhubArticleCommentDTO.class)})
    @GetMapping(value = "/sync")
    public  ResponseData updateTxComment(
            @ApiParam(value = "达人唯一ID", required = true) @RequestParam("atUniqueId") String atUniqueId,
            @ApiParam(value = "作品唯一ID", required = true) @RequestParam("workId") String workId)  {
        try {
            log.info("/article/comment/sync, atUniqueId={}, workId={}", atUniqueId, workId);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return articleCommentService.updateTxComment(baseUser.getId(), atUniqueId,workId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return RD.fail();
    }

    @ApiOperation(value = "查询更新状态")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回信息", response = Boolean.class)})
    @GetMapping(value = "/updateStatus")
    public  ResponseData queryTxCommentUpdateStatus(
            @ApiParam(value = "达人唯一ID", required = true) @RequestParam("atUniqueId") String atUniqueId,
            @ApiParam(value = "作品唯一ID", required = true) @RequestParam("workId") String workId)  {
        try {
            log.info("/article/comment/updateStatus, atUniqueId={}, workId={}", atUniqueId, workId);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return articleCommentService.queryTxCommentUpdateStatus(baseUser.getId(), atUniqueId,workId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return RD.fail();
    }

}
