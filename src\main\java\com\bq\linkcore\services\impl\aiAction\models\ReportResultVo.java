package com.bq.linkcore.services.impl.aiAction.models;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 报告生成结果基础类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "报告生成结果")
public class ReportResultVo<T> {
    
    @ApiModelProperty(value = "是否成功")
    private Boolean success;
    
    @ApiModelProperty(value = "错误码")
    private String errorCode;
    
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;
    
    @ApiModelProperty(value = "结果数据")
    private T data;
    
    @ApiModelProperty(value = "生成时间")
    private LocalDateTime generateTime;
    
    @ApiModelProperty(value = "处理耗时(毫秒)")
    private Long processingTime;
    
    /**
     * 成功结果
     */
    public static <T> ReportResultVo<T> success(T data) {
        return ReportResultVo.<T>builder()
                .success(true)
                .data(data)
                .generateTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 成功结果（带处理时间）
     */
    public static <T> ReportResultVo<T> success(T data, Long processingTime) {
        return ReportResultVo.<T>builder()
                .success(true)
                .data(data)
                .generateTime(LocalDateTime.now())
                .processingTime(processingTime)
                .build();
    }
    
    /**
     * 失败结果
     */
    public static <T> ReportResultVo<T> failure(String errorCode, String errorMessage) {
        return ReportResultVo.<T>builder()
                .success(false)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .generateTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 失败结果（带处理时间）
     */
    public static <T> ReportResultVo<T> failure(String errorCode, String errorMessage, Long processingTime) {
        return ReportResultVo.<T>builder()
                .success(false)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .generateTime(LocalDateTime.now())
                .processingTime(processingTime)
                .build();
    }
}
