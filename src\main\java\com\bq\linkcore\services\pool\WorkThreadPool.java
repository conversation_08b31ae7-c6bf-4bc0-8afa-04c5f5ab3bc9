package com.bq.linkcore.services.pool;

import java.util.concurrent.*;

/**
 * 针对每个zone的空间
 *
 * <AUTHOR>
 * @date 2022/5/21 15:30
 * @className WorkThreadPool
 * @description
 */
public class WorkThreadPool {

    /**
     * 线程池
     */
    private ThreadPoolExecutor threadPool = null;

    /**
     * 初始化线程池
     *
     * @param waitingQueue
     * @param threadFactory
     * @param rejectedExecutionHandler
     */
    public WorkThreadPool(int corePoolSize,
                          int maximumPoolSize,
                          long keepAliveTime,
                          TimeUnit unit,
                          BlockingQueue<Runnable> waitingQueue,
                          ThreadFactory threadFactory,
                          RejectedExecutionHandler rejectedExecutionHandler) {
        threadPool = new ThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                keepAliveTime,
                unit,
                waitingQueue,
                threadFactory,
                rejectedExecutionHandler
        );
    }

    /**
     * 添加任务
     *
     * @param workTask
     */
    public void addTask(Runnable workTask) {
        threadPool.execute(workTask);
    }

    /**
     * 获取等待任务数量
     *
     * @return
     */
    public long getTaskCount() {
        return threadPool.getTaskCount();
    }

    /**
     * the current number of threads in the pool
     *
     * @return
     */
    public long getThreadCount() {
        return threadPool.getPoolSize();
    }

    /**
     * 暂停线程池
     */
    public void stop() {
        if (!threadPool.isShutdown()) {
            threadPool.shutdown();
        }
    }

}
