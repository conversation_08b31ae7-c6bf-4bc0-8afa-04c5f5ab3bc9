package com.bq.linkcore.utils;

import com.bq.data.base.exception.SystemException;
import com.bq.linkcore.common.ResponseMsg;
import com.bq.linkcore.common.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collection;

/**
 * 断言工具类
 *
 * <AUTHOR>
 */
public class AssertUtils {

    /**
     * assert object not null
     *
     * @param obj
     * @param msg
     */
    public static void assertNotNull(Object obj, String msg) {
        if (obj == null) {
            throw new ServiceException(ResponseMsg.FAIL.getCode(), msg);
        }
    }

    /**
     * assert object not null
     *
     * @param obj
     * @param error
     * @param msg
     */
    public static void assertNotNull(Object obj, ResponseMsg error, String msg) {
        if (obj == null) {
            throw new ServiceException(error.getCode(), msg);
        }
    }

    /**
     * assert object is null
     *
     * @param obj
     * @param msg
     */
    public static void assertNull(Object obj, String msg) {
        if (obj != null) {
            throw new ServiceException(ResponseMsg.FAIL.getCode(), msg);
        }
    }

    /**
     * assert object is null
     *
     * @param obj
     * @param error
     * @param msg
     */
    public static void assertNull(Object obj, ResponseMsg error, String msg) {
        if (obj != null) {
            throw new ServiceException(error.getCode(), msg);
        }
    }

    /**
     * assert object is null
     *
     * @param value
     * @param msg
     */
    public static void assertTrue(boolean value, String msg) {
        if (!value) {
            throw new ServiceException(ResponseMsg.FAIL.getCode(), msg);
        }
    }

    public static void assertTrue(boolean value, ResponseMsg responseMsg) {
        if (!value) {
            throw new ServiceException(responseMsg);
        }
    }

    public static void assertNull(Object object, ResponseMsg responseMsg) {
        if (object == null) {
            throw new ServiceException(responseMsg);
        }
    }

    /**
     * assert object is null
     *
     * @param value
     * @param error
     * @param msg
     */
    public static void assertNull(boolean value, ResponseMsg error, String msg) {
        if (!value) {
            throw new SystemException(error.getCode(), msg);
        }
    }

    /**
     * assert string not empty
     *
     * @param str
     * @param msg
     */
    public static void assertStringNotEmpty(String str, String msg) {
        if (StringUtils.isBlank(str)) {
            throw new ServiceException(ResponseMsg.FAIL.getCode(), msg);
        }
    }

    /**
     * assert string not empty
     *
     * @param str
     * @param error
     * @param msg
     */
    public static void assertStringNotEmpty(String str, ResponseMsg error, String msg) {
        if (StringUtils.isBlank(str)) {
            throw new ServiceException(error.getCode(), msg);
        }
    }

    /**
     * assert collection is empty
     *
     * @param coll
     * @param msg
     */
    public static void assertCollectionEmpty(Collection coll, String msg) {
        if (!CollectionUtils.isEmpty(coll)) {
            throw new ServiceException(ResponseMsg.FAIL.getCode(), msg);
        }
    }

    /**
     * assert collection is empty
     *
     * @param coll
     * @param error
     * @param msg
     */
    public static void assertCollectionEmpty(Collection coll, ResponseMsg error, String msg) {
        if (!CollectionUtils.isEmpty(coll)) {
            throw new ServiceException(error.getCode(), msg);
        }
    }
}
