package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.AtAccountUserSettingDO;
import com.bq.linkcore.bean.entity.TikhubArticleCommentDO;
import com.bq.linkcore.common.PlatformEnum;
import com.bq.linkcore.dao.mapper.AtAccountUserSettingMapper;
import com.bq.linkcore.dao.mapper.TikhubArticleCommentMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class ArticleCommentBiz {

    @Resource
    private TikhubArticleCommentMapper tikhubArticleCommentMapper;

    /**
     * 根据用户ID查询用户关联的TikTok账户列表
     * @param workId 用户ID
     * @return 用户账户设置列表
     */
    public List<TikhubArticleCommentDO> queryTiktokArticleCommentList(String workId) {
        LambdaQueryWrapper<TikhubArticleCommentDO> queryWrapper = new LambdaQueryWrapper<TikhubArticleCommentDO>()
                .eq(TikhubArticleCommentDO::getWorkId, workId)
                .orderByDesc(TikhubArticleCommentDO::getPublishTime);

        return tikhubArticleCommentMapper.selectList(queryWrapper);
    }

    /**
     * 根据用户ID查询用户关联的TikTok账户列表
     * @param commentId 用户ID
     * @return 用户账户设置列表
     */
    public TikhubArticleCommentDO queryTiktokArticleComment(String commentId) {
        LambdaQueryWrapper<TikhubArticleCommentDO> queryWrapper = new LambdaQueryWrapper<TikhubArticleCommentDO>()
                .eq(TikhubArticleCommentDO::getCommentId, commentId)
                .orderByDesc(TikhubArticleCommentDO::getPublishTime);

        return tikhubArticleCommentMapper.selectOne(queryWrapper);
    }

    public int insertArticleComment(TikhubArticleCommentDO articleCommentDO) {
        return tikhubArticleCommentMapper.insert(articleCommentDO);
    }

    public int updateArticleComment(TikhubArticleCommentDO articleCommentDO) {
        return tikhubArticleCommentMapper.updateById(articleCommentDO);
    }
}
