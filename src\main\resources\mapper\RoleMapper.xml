<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.RoleMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.RoleDO">
        <id column="id" property="id" />
        <result column="role_code" property="roleCode" />
        <result column="role_name" property="roleName" />
        <result column="role_type" property="roleType" />
        <result column="role_remark" property="roleRemark" />
        <result column="tenant_code" property="tenantCode" />
        <result column="is_del" property="isDel" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, role_code, role_name, role_type, role_remark, tenant_code, is_del, updater, update_time, creator, create_time
    </sql>

  

  
</mapper>
