package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.UserTenantRelDO;
import com.bq.linkcore.dao.mapper.UserTenantRelMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class UserTenantRelBiz {

    @Resource
    private UserTenantRelMapper userTenantRelMapper;

    public int insertUserTenantRel(Long userId, String tenantCode) {
        UserTenantRelDO userTenantRelDO = new UserTenantRelDO();
        userTenantRelDO.setUserId(userId);
        userTenantRelDO.setTenantCode(tenantCode);
        return userTenantRelMapper.insert(userTenantRelDO);
    }

    public UserTenantRelDO queryUserTenantRel(Long userId) {
        LambdaQueryWrapper<UserTenantRelDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserTenantRelDO::getUserId, userId);
        return userTenantRelMapper.selectOne(queryWrapper);
    }


    public String getTenantByUserId(Long userId){
        LambdaQueryWrapper<UserTenantRelDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserTenantRelDO::getUserId, userId);
        UserTenantRelDO userTenantRelDO = userTenantRelMapper.selectOne(queryWrapper);
        if(userTenantRelDO != null){
            return userTenantRelDO.getTenantCode();
        }
        return null;
    }

//    public List<TenantUserInfoVo> queryTenantUserList(String tenantCode) {
//        return userTenantRelMapper.queryTenantUserList(tenantCode);
//    }
//
//    public List<UserTenantRelDO> queryTenantUserRelList(String tenantCode) {
//        LambdaQueryWrapper<UserTenantRelDO> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(UserTenantRelDO::getTenantCode, tenantCode);
//        return userTenantRelMapper.selectList(queryWrapper);
//    }


}
