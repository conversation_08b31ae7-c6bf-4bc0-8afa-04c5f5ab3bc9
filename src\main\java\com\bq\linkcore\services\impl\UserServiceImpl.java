package com.bq.linkcore.services.impl;

import com.alibaba.fastjson.JSON;
import com.bq.data.base.bean.BaseEnterpriseUser;
import com.bq.data.base.bean.ResponseData;
import com.bq.data.base.code.SecurityUtils;
import com.bq.data.base.exception.SystemException;
import com.bq.linkcore.bean.dto.TenantUserInfoDTO;
import com.bq.linkcore.bean.dto.UserMemberExtendDTO;
import com.bq.linkcore.bean.dto.UserMemberInfo;
import com.bq.linkcore.bean.entity.*;
import com.bq.linkcore.bean.vo.UserInfoVo;
import com.bq.linkcore.bean.vo.UserLoginReqVo;
import com.bq.linkcore.bean.vo.UserLoginRspVo;
import com.bq.linkcore.bean.vo.UserMessageReqVo;
import com.bq.linkcore.biz.*;
import com.bq.linkcore.client.EmailClient;
import com.bq.linkcore.client.RateLimiterFactory;
import com.bq.linkcore.common.*;
import com.bq.linkcore.config.CommercialConfig;
import com.bq.linkcore.config.EncryptConfig;
import com.bq.linkcore.config.LoginRepeatLimitConfig;
import com.bq.linkcore.config.TenantConfig;
import com.bq.linkcore.services.IUserAuthorService;
import com.bq.linkcore.services.IUserService;
import com.bq.linkcore.utils.MyRandom;
import com.bq.linkcore.utils.NetworkEncoder;
import com.bq.linkcore.utils.SecretKeyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.bq.linkcore.common.Constants.LOGIN_EMAIL_KEY_;
import static com.bq.linkcore.common.ResponseMsg.*;

@Service
@Slf4j
public class UserServiceImpl implements IUserService {
    @Autowired
    private RedissonClient redissonClient;

    @Resource
    private EncryptConfig encryptConfig;

    @Autowired
    private UserBiz userBiz;

    @Autowired
    private RateLimiterFactory rateLimiterFactory;

    @Resource
    private EmailClient emailClient;

    @Resource
    private LoginRepeatLimitConfig limitConfig;

    @Resource
    private TenantConfig tenantConfig;

    @Resource
    private CommercialConfig commercialConfig;

    @Resource
    private UserMemberInfoBiz userMemberInfoBiz;

    @Resource
    private UserTenantRelBiz userTenantRelBiz;

    @Resource
    private TenantBiz tenantBiz;

    @Resource
    private RoleLineBiz roleLineBiz;

    @Resource
    private IUserAuthorService userAuthorService;

    @Override
    public ResponseData sendMessageByEmail(UserMessageReqVo vo) {
        // 通过手机号查询用户对象 修改密码操作不传手机号，查询手机号
        if (vo.getType() == 3) {
            RBucket<BaseEnterpriseUser> bucketLoginUser = redissonClient.getBucket(SecurityUtils.getAuthorization());
            UserDO userDO = userBiz.selectUser(bucketLoginUser.get().getId());
            if (userDO == null) {
                throw new SystemException(ERROR_PHONE_MESSAGE.getCode(), ERROR_PHONE_MESSAGE.getMsg());
            }
            vo.setEmail(userDO.getEmail());
        }

        String redisKey = buildRedisMessageCode(vo.getEmail(), vo.getType());

        // 一分钟内发送次数超过 1 就提示错误
        if (!rateLimiterFactory.getToken(redisKey, 1, RateIntervalUnit.MINUTES)) {
            return RD.fail(ResponseMsg.ERROR_FREQUENTLY.getCode());
        }

        // 生成短信6位随机码
        String code = MyRandom.randomNumber6();
        try {
            RBucket<String> bucket = redissonClient.getBucket(redisKey);
            if (sendEmailSms(vo.getEmail(), code)) {
                bucket.set(code, Constants.MESSAGE_CODE_SIX_OVER_TIME_MINUTES, TimeUnit.MINUTES);
            } else {
                return RD.fail(ResponseMsg.ERROR_SEND_MESSAGE.getCode());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RD.fail(e.getMessage());
        }
        return RD.ok();
    }

    @Override
    public ResponseData accountLogin(UserLoginReqVo vo) {

        String pwd = NetworkEncoder.decrypt(vo.getPassword(), encryptConfig.getNetworkSecretKey(), encryptConfig.getNetworkSecretIv());
        if (StringUtils.isBlank(pwd)) {
            return RD.fail(ResponseMsg.ERROR_PASSWORD.getCode());
        }
        vo.setPassword(pwd);
        return login(vo);
    }

    private String buildRedisMessageCode(String email, int type) {
        return LOGIN_EMAIL_KEY_ + email + "_" + type + "_";
    }

    private boolean sendEmailSms(String email, String code) {
        try {
            emailClient.sendVerifyCode(email, code);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return false;
    }

    @Override
    public ResponseData loginTxByPhone(UserLoginReqVo vo, UserDO userDO) {
        BaseEnterpriseUser baseUser = cacheRedis(userDO);
        UserLoginRspVo userLoginRspVo = buildUserLoginRsp(userDO, baseUser.getToken(), baseUser.getSsotoken());
        clearRepeatLimit(vo.getMessageCode(), vo.getType(), true);

        UserMemberInfo userMemberInfo = queryTxUserMember(baseUser.getId());
        if (userMemberInfo == null) {
            userMemberInfo = insertUserMemberInfo(baseUser.getId());
        }

        userLoginRspVo.setLevel(userMemberInfo.getLevel());
        userLoginRspVo.setStartTime(userMemberInfo.getStartTime());
        userLoginRspVo.setEndTime(userMemberInfo.getEndTime());

        return ResponseData.ok(userLoginRspVo);
    }

    @Override
    public boolean checkMessage(String email, Integer type, String messageCode) {
        boolean ret = false;
        //
        String redisKey = buildRedisMessageCode(email, type);
        try {

            RBucket<String> bucket = redissonClient.getBucket(redisKey);
            if (bucket.isExists()) {
                String code = bucket.get();
                ret = StringUtils.equals(code, messageCode);

                bucket.delete();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return ret;
    }

    @Override
    public boolean checkPwdRepeatLimit(String email) {
        //验证此账号是否已限制
        RAtomicLong atomicLong = redissonClient.getAtomicLong(Constants.USER_CHECK_PWD_NUM_ + email);
        long num = atomicLong.incrementAndGet();

        if (num == 1) {
            atomicLong.expire(limitConfig.getPwdTimeout(), TimeUnit.MINUTES);
        }
        //比较输入密码是否超过指定次数
        if (num > limitConfig.getPwdRepeatLimit()) {
            log.error("用户{}输入密码错误{}次!", email, atomicLong.get());
            return true;
        }

        return false;
    }

    @Override
    public UserInfoVo queryTxUser(Long userId) {
        // 判断是否存在
        UserDO userDO = userBiz.selectUser(userId);
        if (userDO == null) {
            return null;
        }

        UserInfoVo userInfoVo = new UserInfoVo();
        BeanUtils.copyProperties(userDO, userInfoVo);

        UserMemberInfo userMemberInfo = queryTxUserMember(userId);
        if (userMemberInfo != null) {
            userInfoVo.setLevel(userMemberInfo.getLevel());
            userInfoVo.setStartTime(userMemberInfo.getStartTime());
            userInfoVo.setEndTime(userMemberInfo.getEndTime());
        }

        UserTenantRelDO userTenantRelDO = userTenantRelBiz.queryUserTenantRel(userId);
        if (userTenantRelDO != null) {
            userInfoVo.setTenantCode(userTenantRelDO.getTenantCode());
        } else {
            log.error("用户无企业信息：{}", userTenantRelDO);
        }

        UserRoleRelDO userRoleRelDO = roleLineBiz.queryTenantUserRoleRel(userId, userTenantRelDO.getTenantCode());
        if (userRoleRelDO != null) {
            userInfoVo.setRoleCode(userRoleRelDO.getRoleCode());
        } else {
            log.error("该用户无角色信息 :{}", userDO);
        }
        RoleDO roleDO =
                roleLineBiz.queryEveryRoleByCode(userRoleRelDO.getRoleCode(), userRoleRelDO.getTenantCode());

        userInfoVo.setRoleName(roleDO.getRoleName());
        return userInfoVo;
    }

    @Override
    public UserDO queryTxUserByEmail(String email) {
        // 判断是否存在
        return userBiz.selectUserByEmail(email);
    }


    public ResponseData login(UserLoginReqVo vo) {
        // 校验失败次数
        boolean limit = checkPwdRepeatLimit(vo.getEmail());
        if (limit) {
            String msg = String.format(ResponseMsg.ERROR_LOGIN_ERR_PWD_TIMES.getMsg(), limitConfig.getPwdRepeatLimit(), limitConfig.getPwdTimeout());
            return ResponseData.fail(ResponseMsg.ERROR_LOGIN_ERR_PWD_TIMES.getCode(), msg);
        }

        UserDO userDO = null;
        if (vo.getLoginType().equals(LoginTypeEnum.ACCOUNT.getCode())) {
            userDO = userBiz.selectUserByAccount(vo.getEmail(), SecretKeyUtil.encodeBase64String(vo.getPassword()));
        } else {
            userDO = userBiz.selectUserByEmail(vo.getEmail());
        }

        if (userDO == null) {
            return RD.fail(ERROR_ACCOUNT_OR_PWD.getCode());
        }

        BaseEnterpriseUser baseUser = cacheRedis(userDO);

        UserLoginRspVo userLoginRspVo = buildUserLoginRsp(userDO, baseUser.getToken(), baseUser.getSsotoken());
        clearRepeatLimit(vo.getEmail(), vo.getType(), true);

        UserMemberInfo userMemberInfo = queryTxUserMember(userDO.getId());
        if (userMemberInfo == null) {
            userMemberInfo = insertUserMemberInfo(baseUser.getId());
        } else {
            userMemberInfoBiz.updateUserMemberInfoCache(userMemberInfo);
        }

        userLoginRspVo.setLevel(userMemberInfo.getLevel());
        userLoginRspVo.setStartTime(userMemberInfo.getStartTime());
        userLoginRspVo.setEndTime(userMemberInfo.getEndTime());

        return ResponseData.ok(userLoginRspVo);
    }

    @Override
    public ResponseData insertTxUser(UserLoginReqVo vo) {

        if (StringUtils.isBlank(vo.getPassword())) {
            return RD.fail(ResponseMsg.ERROR_PASSWORD_NOT_EXIST);
        }
        String pwd = NetworkEncoder.decrypt(vo.getPassword(), encryptConfig.getNetworkSecretKey(), encryptConfig.getNetworkSecretIv());

        String tenant = tenantConfig.getTenant();
        String roleCode = tenantConfig.getRoleCode();

        TenantUserInfoDTO infoDTO = new TenantUserInfoDTO();
        infoDTO.setUsername(vo.getUsername());
        infoDTO.setEmail(vo.getEmail());
        infoDTO.setTenantCode(tenant);
        infoDTO.setRoleCode(roleCode);
        infoDTO.setPassword(pwd);

        UserLoginRspVo userLoginRspVo = insertTxTenantUser(infoDTO);

        // 初始化爆款设置
        userAuthorService.initTxUserSetting(userLoginRspVo.getId());

        // 初始化额度
//        userQBApiService.addTxUserDefault(userLoginRspVo.getId(), tenant);
        return ResponseData.ok(userLoginRspVo);
    }

    public UserLoginRspVo insertTxTenantUser(TenantUserInfoDTO dto) {

        UserDO userDO = userBiz.insertUser(
                dto.getPhone(), dto.getEmail(), dto.getUsername(), dto.getUsername(), dto.getPassword()
        );

        if (userDO == null) {
            throw new SystemException(ERROR_CREATE_ACCOUNT.getCode(), ERROR_CREATE_ACCOUNT.getMsg());
        }
        // 初始化爆款设置
        userAuthorService.initTxUserSetting(userDO.getId());

        LocalDateTime n = LocalDateTime.now();
        LocalDateTime e = n.plusDays(commercialConfig.getMembershipDays());

        UserMemberInfoDO userMemberInfoDO = buildUserMemberInfoDO(userDO.getId(), 0, commercialConfig.getMaxAuthorCount(), commercialConfig.getTimeDenominator(), n, e);
        userMemberInfoBiz.insertUserMemberInfo(userMemberInfoDO);
        userMemberInfoBiz.updateUserMemberInfoCache(userMemberInfoDO);

        userTenantRelBiz.insertUserTenantRel(userDO.getId(), dto.getTenantCode());

        UserRoleRelDO userRoleRelDO = roleLineBiz.queryTenantUserRoleRel(userDO.getId(), dto.getTenantCode());
        if (userRoleRelDO != null) {
            throw new ServiceException(ERROR_USER_ROLE_EXIST.getCode(), ERROR_USER_ROLE_EXIST.getMsg());
        }
        roleLineBiz.insertUserRoleRel(dto.getRoleCode(), userDO.getId(), dto.getTenantCode());

        // build and login
        BaseEnterpriseUser baseUser = cacheRedis(userDO);
        UserLoginRspVo userLoginRspVo = buildUserLoginRsp(userDO, baseUser.getToken(), baseUser.getSsotoken());

        userLoginRspVo.setLevel(userMemberInfoDO.getLevel());
        userLoginRspVo.setStartTime(userMemberInfoDO.getStartTime());
        userLoginRspVo.setEndTime(userMemberInfoDO.getEndTime());

        return userLoginRspVo;
    }

    @Override
    public UserMemberInfo queryTxUserMember(Long userId) {
        return userMemberInfoBiz.queryTxUserMember(userId);
    }

    private UserMemberInfo insertUserMemberInfo(Long userId) {
        LocalDateTime n = LocalDateTime.now();
        LocalDateTime e = n.plusDays(commercialConfig.getMembershipDays());

        UserMemberInfoDO userMemberInfoDO =
                buildUserMemberInfoDO(userId, 0, commercialConfig.getMaxAuthorCount(), commercialConfig.getMembershipDays(), n, e);

        userMemberInfoBiz.insertUserMemberInfo(userMemberInfoDO);
        return userMemberInfoBiz.updateUserMemberInfoCache(userMemberInfoDO);
    }

    private UserMemberInfoDO buildUserMemberInfoDO(Long userId,
                                                   Integer level,
                                                   Integer maxAuthorCount,
                                                   Integer liveMonitorDuration,
                                                   LocalDateTime startTime,
                                                   LocalDateTime endTime) {
        UserMemberExtendDTO extendDTO = new UserMemberExtendDTO();
        extendDTO.setIsInitComment(0);
        extendDTO.setMaxInitCommentCount(100);
        extendDTO.setDefaultCommentCount(1000);
        extendDTO.setMaxDouyinOAuthCount(10);
        extendDTO.setMaxDouyinVideoDays(90);
        extendDTO.setMaxDouyinVideoMonitorCount(5);

        return UserMemberInfoDO.builder()
                .userId(userId)
                .level(level)
                .extend(JSON.toJSONString(extendDTO))
                .maxAuthorCount(maxAuthorCount)
                .liveMonitorDuration(liveMonitorDuration)
                .startTime(startTime)
                .endTime(endTime)
                .creator(0L)
                .updater(0L)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
    }

    private UserLoginRspVo buildUserLoginRsp(UserDO userDO,
                                             String token,
                                             String ssoToken) {
        UserLoginRspVo vo = new UserLoginRspVo();

        vo.setId(userDO.getId());
        vo.setPhone(userDO.getPhone());
        vo.setEmail(userDO.getEmail());
        vo.setNickname(userDO.getNickname());
        vo.setAvatar(userDO.getAvatar());
        vo.setToken(token);
        vo.setSsoToken(ssoToken);

        return vo;
    }

    private BaseEnterpriseUser cacheRedis(UserDO userDO) {
        boolean isTokenExist = true;

        UserTokenRelDO userTokenRelDO = userBiz.selectToken(userDO.getId());
        if (userTokenRelDO == null) {
            userTokenRelDO = new UserTokenRelDO();
            userTokenRelDO.setUserId(userDO.getId());
            isTokenExist = false;
        }

        String oldSSOToken = userTokenRelDO.getSsotoken();
        String oldToken = userTokenRelDO.getToken();

        String token = UUID.randomUUID().toString().replace("-", "");

        userTokenRelDO.setToken(token);

        // 清理已cache数据
        if (!StringUtils.isEmpty(oldToken)) {
            RBucket<String> oldTokenBucket = redissonClient.getBucket(oldToken);
            oldTokenBucket.delete();
        }

        // 存储数据库
        if (isTokenExist) {
            userBiz.updateUserToken(userTokenRelDO);
        } else {
            userBiz.insertUserToken(userTokenRelDO);
        }

        BaseEnterpriseUser baseUser = userBiz.buildBaseUser(userDO, token, userTokenRelDO.getSsotoken(), "");
        initTenantInfo(baseUser);

        UserRoleRelDO userRoleRelDO = roleLineBiz.queryTenantUserRoleRel(baseUser.getId(), baseUser.getTenantCode());
        if (userRoleRelDO != null) {
            baseUser.setRoleCode(userRoleRelDO.getRoleCode());
        }

        RBucket<BaseEnterpriseUser> bucketLoginUser = redissonClient.getBucket(token);
        bucketLoginUser.set(baseUser, Constants.AUTO_LOGIN_FIFTEEN_OVER_TIME_DAYS, TimeUnit.DAYS);

        return baseUser;
    }

    private void initTenantInfo(BaseEnterpriseUser baseUser) {
        UserTenantRelDO userTenantRelDO = userTenantRelBiz.queryUserTenantRel(baseUser.getId());
        TenantDO tenantDO = tenantBiz.queryTenantByCode(userTenantRelDO.getTenantCode());
        if (tenantDO == null) {
            throw new SystemException(FAIL.getCode(), "企业信息初始化失败");
        }
        baseUser.setTenantCode(tenantDO.getCode());
        baseUser.setTenantName(tenantDO.getName());
    }


    private boolean clearRepeatLimit(String email,
                                     Integer type,
                                     boolean clearVerifyCode) {
        String pre = null;
        switch (type) {
            case 1:
                pre = Constants.USER_CHECK_PWD_NUM_;
                break;
            case 2:
                pre = Constants.USER_CHECK_MESSAGE_CODE_NUM_;
                break;
            default:
                pre = null;
        }

        if (pre == null) {
            return false;
        }

        RAtomicLong atomicLong = null;
        try {
            atomicLong = redissonClient.getAtomicLong(pre + email);
            atomicLong.delete();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }

        return true;
    }

}
