package com.bq.linkcore.bean.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/02/22 14:50
 * @className UserInfoVo
 * @description
 */
@ApiModel(description = "登录接口请求参数对象")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class UserInfoVo implements Serializable {

    @ApiModelProperty(value = "自增长ID")
    private Long id;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "用户头像url")
    private String avatar;

    @ApiModelProperty(value = "用户昵称")
    private String nickname;

    /**
     * 客户级别，0：普通用户；1：vip-1用户
     */
    private Integer level;

    /**
     * 会员开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 会员结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "角色code")
    private String roleCode;

    @ApiModelProperty(value = "企业code")
    private String tenantCode;

    @ApiModelProperty(value = "角色名称")
    private String roleName;
}
