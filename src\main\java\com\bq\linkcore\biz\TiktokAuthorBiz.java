package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.AtTiktokAuthorPoolDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorProfileHistoryDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorSearchResultDO;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorPoolMapper;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorProfileHistoryMapper;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorSearchResultMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class TiktokAuthorBiz {

    @Resource
    private AtTiktokAuthorPoolMapper atTiktokAuthorPoolMapper;
    @Resource
    private AtTiktokAuthorProfileHistoryMapper atTiktokAuthorProfileHistoryMapper;
    @Resource
    private AtTiktokAuthorSearchResultMapper atTiktokAuthorSearchResultMapper;

    public int insertTiktokAuthor(AtTiktokAuthorPoolDO atTiktokAuthorPoolDO) {
        return atTiktokAuthorPoolMapper.insert(atTiktokAuthorPoolDO);
    }

    public int updateTiktokAuthor(AtTiktokAuthorPoolDO atTiktokAuthorPoolDO) {
        return atTiktokAuthorPoolMapper.updateById(atTiktokAuthorPoolDO);
    }

    public AtTiktokAuthorPoolDO queryTiktokAuthorByAuthorId(String authorId) {
        LambdaQueryWrapper<AtTiktokAuthorPoolDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorPoolDO::getAuthorId, authorId);

        return atTiktokAuthorPoolMapper.selectOne(queryWrapper);
    }

    public AtTiktokAuthorPoolDO queryTiktokAuthorByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorPoolDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorPoolDO::getUniqueId, uniqueId);

        return atTiktokAuthorPoolMapper.selectOne(queryWrapper);
    }

    public AtTiktokAuthorPoolDO queryTiktokAuthorBySecUid(String secUid) {
        LambdaQueryWrapper<AtTiktokAuthorPoolDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorPoolDO::getSecUid, secUid);

        return atTiktokAuthorPoolMapper.selectOne(queryWrapper);
    }

    public int insertTiktokAuthorHis(AtTiktokAuthorProfileHistoryDO atTiktokAuthorProfileHistoryDO) {
        return atTiktokAuthorProfileHistoryMapper.insert(atTiktokAuthorProfileHistoryDO);
    }

    public List<AtTiktokAuthorProfileHistoryDO> queryTiktokAuthorHisByAuthorId(String authorId) {
        LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorProfileHistoryDO::getAuthorId, authorId);

        return atTiktokAuthorProfileHistoryMapper.selectList(queryWrapper);
    }

    public List<AtTiktokAuthorProfileHistoryDO> queryTiktokAuthorHisByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorProfileHistoryDO::getUniqueId, uniqueId);

        return atTiktokAuthorProfileHistoryMapper.selectList(queryWrapper);
    }

    public List<AtTiktokAuthorProfileHistoryDO> queryTiktokAuthorHisBySecUid(String secUid) {
        LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorProfileHistoryDO::getSecUid, secUid);

        return atTiktokAuthorProfileHistoryMapper.selectList(queryWrapper);
    }

    public int insertTiktokAuthorSearchResultDO(AtTiktokAuthorSearchResultDO atTiktokAuthorSearchResultDO) {
        return atTiktokAuthorSearchResultMapper.insert(atTiktokAuthorSearchResultDO);
    }

    public List<AtTiktokAuthorSearchResultDO> queryTiktokAuthorSearchResultDOByAuthorId(String authorId) {
        LambdaQueryWrapper<AtTiktokAuthorSearchResultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorSearchResultDO::getAuthorId, authorId);

        return atTiktokAuthorSearchResultMapper.selectList(queryWrapper);
    }

    public List<AtTiktokAuthorSearchResultDO> queryTiktokAuthorSearchResultDOByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorSearchResultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorSearchResultDO::getUniqueId, uniqueId);

        return atTiktokAuthorSearchResultMapper.selectList(queryWrapper);
    }

    public List<AtTiktokAuthorSearchResultDO> queryTiktokAuthorSearchResultDOBySecUid(String secUid) {
        LambdaQueryWrapper<AtTiktokAuthorSearchResultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorSearchResultDO::getSecUid, secUid);

        return atTiktokAuthorSearchResultMapper.selectList(queryWrapper);
    }

}
