package com.bq.linkcore.client.emailSend;

import java.util.concurrent.*;


public class MailSendingPool {
    private MailSendingPool() {
    }
    private static class Inner{
        private static MailSendingPool instance = new MailSendingPool();
    }

    public static MailSendingPool getInstance(){
        return Inner.instance;
    }

    private static int nThreads = 2;
    private static int maxThreads = 5;
    private static ExecutorService executor = null;
    static {
        executor = new ThreadPoolExecutor(nThreads,maxThreads,
                1L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(100),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy());

    }

    public MailSendingPool addThread(MailSending sending){
        executor.execute(sending);
        return getInstance();
    }

    public void shutDown(){
        executor.shutdown();
    }
}