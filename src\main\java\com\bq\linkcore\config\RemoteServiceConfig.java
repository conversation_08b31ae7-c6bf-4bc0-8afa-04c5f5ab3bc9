package com.bq.linkcore.config;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/17 11:57
 * @className RemoteServiceConfig
 * @description
 */
public class RemoteServiceConfig {
    public static Map<String, String> tikhub_url_map = new HashMap<String, String>(){{
        put("base", "https://api.tikhub.io");
        put("proxy01", "http://***************:8788");
    }};

    public static String getTikhubBaseUrl() {
        return tikhub_url_map.get("proxy01");
    }
}
