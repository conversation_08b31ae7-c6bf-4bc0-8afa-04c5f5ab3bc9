package com.bq.linkcore.services;

import com.bq.data.base.bean.ResponseData;

public interface IArticleCommentService {

    ResponseData updateTxComment(Long userId, String uniqueId, String workId);

    ResponseData queryTxCommentList(Long userId, String uniqueId, String workId,Integer pageNo, Integer pageSize);

    ResponseData queryTxCommentUpdateStatus(Long userId, String uniqueId, String workId);
}
