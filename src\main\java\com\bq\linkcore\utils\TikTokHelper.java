package com.bq.linkcore.utils;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TikTokHelper {

    // TikTok 用户主页 URL 的正则表达式模式
    private static final Pattern TIKTOK_USER_URL_PATTERN = Pattern.compile(
            "https?://(?:www\\.|m\\.)?tiktok\\.com/@([a-zA-Z0-9_.]+)(?:/.*)?",
            Pattern.CASE_INSENSITIVE
    );

    /***
     * 从 TikTok 主页 URL 中提取 uniqueId（用户名）
     * 支持的 URL 格式：
     * - https://www.tiktok.com/@bleacherreport => bleacherreport
     * - https://m.tiktok.com/@username => username
     * - https://tiktok.com/@user.name => user.name
     * - https://www.tiktok.com/@user_123/video/123456 => user_123
     *
     * @param homeUrl TikTok 主页 URL
     * @return uniqueId（用户名），如果解析失败则返回 null
     */
    public static String regUniqueId(String homeUrl) {
        // 检查输入参数
        if (StringUtils.isBlank(homeUrl)) {
            return null;
        }

        try {
            // 使用正则表达式匹配 TikTok 用户 URL
            Matcher matcher = TIKTOK_USER_URL_PATTERN.matcher(homeUrl.trim());
            if (matcher.find()) {
                String uniqueId = matcher.group(1);
                // 确保提取的 uniqueId 不为空
                return StringUtils.isNotBlank(uniqueId) ? uniqueId : null;
            }
        } catch (Exception e) {
            // 如果正则匹配出现异常，返回 null
            return null;
        }

        return null;
    }

    /**
     * 验证是否为有效的 TikTok 用户主页 URL
     *
     * @param homeUrl TikTok 主页 URL
     * @return true 如果是有效的 TikTok 用户主页 URL，否则返回 false
     */
    public static boolean isValidTikTokUserUrl(String homeUrl) {
        return regUniqueId(homeUrl) != null;
    }
}
