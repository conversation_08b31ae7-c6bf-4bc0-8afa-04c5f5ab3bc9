package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.AtTiktokAuthorSearchResultDO;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorSearchResultMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class AtTtSearchBiz {

    @Resource
    private AtTiktokAuthorSearchResultMapper tiktokAuthorSearchResultMapper;

    /**
     * 插入TikTok作者搜索结果记录
     * @param authorSearchResultDO TikTok作者搜索结果实体对象
     * @return 插入影响的行数
     */
    public int insertAuthorSearchResult(AtTiktokAuthorSearchResultDO authorSearchResultDO) {
        return tiktokAuthorSearchResultMapper.insert(authorSearchResultDO);
    }

    /**
     * 根据uniqueId查询TikTok作者搜索结果记录
     * @param uniqueId 用户名
     * @return TikTok作者搜索结果实体对象，不存在则返回null
     */
    public AtTiktokAuthorSearchResultDO queryAuthorSearchResultByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorSearchResultDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorSearchResultDO>()
                .eq(AtTiktokAuthorSearchResultDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorSearchResultDO::getIsDel, 0);
        return tiktokAuthorSearchResultMapper.selectOne(queryWrapper);
    }

    /**
     * 根据uniqueId查询记录是否存在
     * @param uniqueId 用户名
     * @return 记录数量，大于0表示存在
     */
    public int queryAuthorSearchResultExistsByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorSearchResultDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorSearchResultDO>()
                .eq(AtTiktokAuthorSearchResultDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorSearchResultDO::getIsDel, 0);
        return tiktokAuthorSearchResultMapper.selectCount(queryWrapper);
    }

    /**
     * 根据ID更新TikTok作者搜索结果记录
     * @param authorSearchResultDO TikTok作者搜索结果实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateAuthorSearchResult(AtTiktokAuthorSearchResultDO authorSearchResultDO) {
        return tiktokAuthorSearchResultMapper.updateById(authorSearchResultDO);
    }

    /**
     * 根据uniqueId查询所有相关的TikTok作者搜索结果记录
     * @param uniqueId 用户名
     * @return TikTok作者搜索结果列表
     */
    public List<AtTiktokAuthorSearchResultDO> queryAuthorSearchResultListByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorSearchResultDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorSearchResultDO>()
                .eq(AtTiktokAuthorSearchResultDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorSearchResultDO::getIsDel, 0);
        return tiktokAuthorSearchResultMapper.selectList(queryWrapper);
    }

    /**
     * 根据ID查询TikTok作者搜索结果记录
     * @param id 主键ID
     * @return TikTok作者搜索结果实体对象，不存在则返回null
     */
    public AtTiktokAuthorSearchResultDO queryAuthorSearchResultById(Long id) {
        return tiktokAuthorSearchResultMapper.selectById(id);
    }

    /**
     * 根据authorId查询TikTok作者搜索结果记录
     * @param authorId 发布账号ID
     * @return TikTok作者搜索结果实体对象，不存在则返回null
     */
    public AtTiktokAuthorSearchResultDO queryAuthorSearchResultByAuthorId(String authorId) {
        LambdaQueryWrapper<AtTiktokAuthorSearchResultDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorSearchResultDO>()
                .eq(AtTiktokAuthorSearchResultDO::getAuthorId, authorId)
                .eq(AtTiktokAuthorSearchResultDO::getIsDel, 0);
        return tiktokAuthorSearchResultMapper.selectOne(queryWrapper);
    }

    /**
     * 根据secUid查询TikTok作者搜索结果记录
     * @param secUid 作者账号加密ID
     * @return TikTok作者搜索结果实体对象，不存在则返回null
     */
    public AtTiktokAuthorSearchResultDO queryAuthorSearchResultBySecUid(String secUid) {
        LambdaQueryWrapper<AtTiktokAuthorSearchResultDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorSearchResultDO>()
                .eq(AtTiktokAuthorSearchResultDO::getSecUid, secUid)
                .eq(AtTiktokAuthorSearchResultDO::getIsDel, 0);
        return tiktokAuthorSearchResultMapper.selectOne(queryWrapper);
    }
}
