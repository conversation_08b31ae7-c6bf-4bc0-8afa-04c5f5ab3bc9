package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bq.linkcore.bean.entity.RoleDO;
import com.bq.linkcore.bean.entity.UserRoleRelDO;
import com.bq.linkcore.bean.vo.RoleVo;
import com.bq.linkcore.dao.mapper.RoleMapper;
import com.bq.linkcore.dao.mapper.UserRoleRelMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Component
public class RoleLineBiz {

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private UserRoleRelMapper userRoleRelMapper;



    public int insertRole(RoleDO roleDO) {
        return roleMapper.insert(roleDO);
    }

    public int saveRole(RoleVo roleVo) {
        RoleDO roleDO = convertChangeVoToRoleDo(roleVo);
        return roleMapper.insert(roleDO);
    }

    public int updateUserRole(UserRoleRelDO userRoleRelDO) {
        return userRoleRelMapper.updateById(userRoleRelDO);
    }

    public int deleteRole(String roleCode, String tenantCode) {
        LambdaUpdateWrapper<RoleDO> queryWrapper = new LambdaUpdateWrapper<RoleDO>()
                .eq(RoleDO::getRoleCode, roleCode)
                .eq(RoleDO::getTenantCode, tenantCode)
                .eq(RoleDO::getIsDel, 0)
                .set(RoleDO::getIsDel, 1);
        return roleMapper.update(null, queryWrapper);
    }

    public int updateRole(RoleDO roleDO) {
        return roleMapper.updateById(roleDO);
    }

    public List<RoleDO> queryTenantRoleList(String tenantCode) {
        LambdaQueryWrapper<RoleDO> queryWrapper = new LambdaQueryWrapper<RoleDO>()
                .eq(RoleDO::getTenantCode, tenantCode)
                .eq(RoleDO::getIsDel, 0);
        return roleMapper.selectList(queryWrapper);
    }


    public int insertUserRoleRel(String roleCode, Long userId, String tenantCode) {
        UserRoleRelDO userRoleRelDO = UserRoleRelDO.builder()
                .roleCode(roleCode)
                .userId(userId)
                .tenantCode(tenantCode)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
        return userRoleRelMapper.insert(userRoleRelDO);
    }


    public UserRoleRelDO queryTenantUserRoleRel(Long userId, String tenantCode) {
        LambdaQueryWrapper<UserRoleRelDO> queryWrapper = new LambdaQueryWrapper<UserRoleRelDO>()
                .eq(UserRoleRelDO::getUserId, userId)
                .eq(UserRoleRelDO::getTenantCode, tenantCode);
        return userRoleRelMapper.selectOne(queryWrapper);
    }

    public RoleDO queryRoleByCode(String roleCode, String tenantCode) {

        LambdaQueryWrapper<RoleDO> queryWrapper = new LambdaQueryWrapper<RoleDO>()
                .eq(RoleDO::getRoleCode, roleCode)
                .eq(RoleDO::getTenantCode, tenantCode)
                .eq(RoleDO::getRoleType, 0)
                .eq(RoleDO::getIsDel, 0);
        return roleMapper.selectOne(queryWrapper);
    }


    public RoleDO queryEveryRoleByCode(String roleCode, String tenantCode) {
        LambdaQueryWrapper<RoleDO> queryWrapper = new LambdaQueryWrapper<RoleDO>()
                .eq(RoleDO::getRoleCode, roleCode)
                .eq(RoleDO::getTenantCode, tenantCode)
                .eq(RoleDO::getIsDel, 0);
        return roleMapper.selectOne(queryWrapper);
    }

//    public boolean isRoleNameAvailable(String tenantCode, String roleName) {
//        Wrapper wrapper = new LambdaQueryWrapper<RoleDO>()
//                .in(RoleDO::getTenantCode, tenantCode)
//                .eq(RoleDO::getRoleName, roleName)
//                .eq(RoleDO::getIsDel, DATA_STATUS_NORMAL.getIndex());
//
//        int i = roleMapper.selectCount(wrapper);
//        return i != 0;
//    }

    private RoleDO convertChangeVoToRoleDo(RoleVo vo) {
        RoleDO roleDO = new RoleDO();
        if (null != vo.getId()) {
            roleDO.setId(vo.getId());
        }
        roleDO.setRoleCode(vo.getRoleCode());
        roleDO.setRoleName(vo.getRoleName());
        roleDO.setRoleRemark(vo.getRoleRemark());
        roleDO.setRoleType(vo.getRoleType());
        roleDO.setTenantCode(vo.getTenantCode());
        roleDO.setIsDel(vo.getIsDel());
        roleDO.setCreator(vo.getCreateUser());
        roleDO.setCreateTime(null == vo.getCreateTime() ? LocalDateTime.now() : vo.getCreateTime());
        roleDO.setUpdater(vo.getUpdateUser());
        roleDO.setUpdateTime(LocalDateTime.now());

        return roleDO;
    }


//    public void updateRolePermiReleation(String roleCode,
//                                         String tenantCode,
//                                         Long userId,
//                                         List<String> permissions) {
//
//        //1、批量删除存量当前角色下关联权限
//        this.deleteByRole(roleCode, tenantCode);
//
//        //2、批量新增当前角色下最新关联权限
//        this.saveRolePermiListRel(roleCode, tenantCode, userId, permissions);
//    }


//    public int deleteByRole(String roleCode, String tenantCode) {
//        Map<String, Object> columnMap = new HashMap<>();
//        columnMap.put("role_code", roleCode);
//        columnMap.put("tenant_code", tenantCode);
//        return rolePermissionRelMapper.deleteByMap(columnMap);
//    }

//    public List<RoleDO> queryTenantRoleList(String roleName, String tenantCode) {
//        LambdaQueryWrapper<RoleDO> wrapper = new LambdaQueryWrapper<RoleDO>()
//                .eq(RoleDO::getIsDel, DATA_STATUS_NORMAL.getIndex())
//                .eq(RoleDO::getRoleType, 0);
//
//        if (StringUtils.isNotBlank(roleName)) {
//            wrapper.like(RoleDO::getRoleName, roleName);
//        }
//
//        if (StringUtils.isNotBlank(tenantCode)) {
//            wrapper.eq(RoleDO::getTenantCode, tenantCode);
//        }
//
//        wrapper.orderByDesc(RoleDO::getUpdateTime, RoleDO::getId);
//        return roleMapper.selectList(wrapper);
//    }


    public int getRoleUserCount(String roleCode, String tenantCode) {
        LambdaQueryWrapper<UserRoleRelDO> wrapper = new LambdaQueryWrapper<UserRoleRelDO>()
                .eq(UserRoleRelDO::getRoleCode, roleCode)
                .eq(UserRoleRelDO::getTenantCode, tenantCode);

        return userRoleRelMapper.selectCount(wrapper);
    }


//    public List<String> selectRolePermissions(String roleCode, String tenantCode) {
//        LambdaQueryWrapper<RolePermissionRelDO> wrapper = new LambdaQueryWrapper<RolePermissionRelDO>()
//                .eq(RolePermissionRelDO::getRoleCode, roleCode)
//                .eq(RolePermissionRelDO::getTenantCode, tenantCode);
//        List<RolePermissionRelDO> list = rolePermissionRelMapper.selectList(wrapper);
//        if (list.size() > 0) {
//            return list.stream().map(RolePermissionRelDO::getPermiCode).collect(Collectors.toList());
//        } else {
//            return new ArrayList<>();
//        }
//    }
//
//    public List<UserMenuRespVo> queryUserMenuList(String roleCode, String tenantCode) {
//        return rolePermissionRelMapper.queryUserMenuList(roleCode, tenantCode);
//    }
//
//    public Map<String,RoleDO> queryTenantRoleMap(List<String> roleCodes, String tenantCode){
//        LambdaQueryWrapper<RoleDO> wrapper = new LambdaQueryWrapper<RoleDO>()
//                .eq(RoleDO::getIsDel, DATA_STATUS_NORMAL.getIndex())
//                .in(RoleDO::getRoleCode, roleCodes)
//                .eq(RoleDO::getTenantCode, tenantCode);
//        List<RoleDO> list = roleMapper.selectList(wrapper);
//        if (list.size() > 0) {
//            return list.stream().collect(Collectors.toMap(RoleDO::getRoleCode, Function.identity()));
//        }
//        return null;
//    }

}
