package com.bq.linkcore.controller.openapi;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.vo.TenantAddReqVo;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ServiceException;
import com.bq.linkcore.services.ITenantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@Api(value = "租户相关接口", tags = "租户相关接口")
@RestController
@Slf4j
@RequestMapping("/api")
public class HashTagController {

    @Resource
    private ITenantService tenantService;


}
