package com.bq.linkcore.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.bq.linkcore.bean.dto.UserMemberExtendDTO;
import com.bq.linkcore.bean.dto.UserMemberInfo;
import com.bq.linkcore.bean.entity.UserMemberInfoDO;
import com.bq.linkcore.dao.mapper.UserMemberInfoMapper;
import com.bq.linkcore.utils.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.bq.linkcore.common.Constants.USER_MEMBER_INFO_;


/**
 * @ClassName: UserMemberInfoBiz
 * @Description:
 * @author: lmy
 * @date: 2024年04月23日 13:28
 */
@Slf4j
@Component
public class UserMemberInfoBiz {
    @Resource
    private UserMemberInfoMapper userMemberInfoMapper;

    @Autowired
    private RedissonClient redissonClient;

    public UserMemberInfoDO queryMemberInfoFromDB(Long userId) {
        Wrapper wrapper = new LambdaQueryWrapper<UserMemberInfoDO>()
                .eq(UserMemberInfoDO::getUserId, userId);

        return userMemberInfoMapper.selectOne(wrapper);
    }

    public UserMemberInfoDO insertUserMemberInfo(UserMemberInfoDO userMemberInfoDO) {
        boolean ret = SqlHelper.retBool(userMemberInfoMapper.insert(userMemberInfoDO));
        if (ret) {
            return userMemberInfoDO;
        }

        return null;
    }

    public boolean updateUserMemberInfo(UserMemberInfoDO userMemberInfoDO) {
        return SqlHelper.retBool(userMemberInfoMapper.updateById(userMemberInfoDO));
    }

    public UserMemberInfo queryTxUserMember(Long userId) {
        UserMemberInfo userMemberInfo = getUserMemberInfoFromCache(userId);
        if (userMemberInfo == null) {
            UserMemberInfoDO userMemberInfoDO = queryMemberInfoFromDB(userId);
            if (userMemberInfoDO != null) {
                userMemberInfo = updateUserMemberInfoCache(userMemberInfoDO);
            }
        }

        return userMemberInfo;
    }

    public UserMemberInfo updateUserMemberInfoCache(UserMemberInfoDO userMemberInfoDO) {
        UserMemberInfo memberInfo = new UserMemberInfo();
        BeanUtils.copyProperties(userMemberInfoDO, memberInfo);
        if (StringUtils.isNotBlank(userMemberInfoDO.getExtend())) {
            JSONObject object = JSON.parseObject(userMemberInfoDO.getExtend());
            UserMemberExtendDTO userMemberExtendDTO = BeanUtil.convert(object, UserMemberExtendDTO.class);
            if (userMemberExtendDTO != null) {
                memberInfo.setUserMemberExtendDTO(userMemberExtendDTO);
            } else {
                log.error("updateUserMemberInfoCache error, user_id={}", userMemberInfoDO.getUserId());
            }
        }

        String key = USER_MEMBER_INFO_ + userMemberInfoDO.getUserId();
        RBucket<UserMemberInfo> bucketLoginUser = redissonClient.getBucket(key);
        bucketLoginUser.set(memberInfo);

        return memberInfo;
    }

    public UserMemberInfo getUserMemberInfoFromCache(Long userId) {
        String key = USER_MEMBER_INFO_ + userId;
        RBucket<UserMemberInfo> bucketLoginUser = redissonClient.getBucket(key);

        return bucketLoginUser.get();
    }

    public void updateUserMemberInfoCache(UserMemberInfo userMemberInfo) {
        String key = USER_MEMBER_INFO_ + userMemberInfo.getUserId();
        RBucket<UserMemberInfo> bucketLoginUser = redissonClient.getBucket(key);
        bucketLoginUser.set(userMemberInfo);
    }
}
