package com.bq.linkcore.bean.vo;

import com.bq.linkcore.services.impl.aiAction.models.HitVideoAnalysisVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 简化的账号拆解结果返回对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "返回结构体")
public class SimpleDisassemblyResultVo {
    
    @ApiModelProperty(value = "创建时间戳")
    private Long createTime;
    
    @ApiModelProperty(value = "更新时间戳")
    private Long updateTime;
    
    @ApiModelProperty(value = "账号唯一标识")
    private String atUniqueId;
    
    @ApiModelProperty(value = "任务信息")
    private TaskInfo taskInfo;
    
    @ApiModelProperty(value = "任务ID")
    private String taskId;
    
    @ApiModelProperty(value = "任务状态：0-进行中，1-已完成，2-失败")
    private Integer taskStatus;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TaskInfo {
        @ApiModelProperty(value = "账号诊断")
        private String accountDiagnosis;
        
        @ApiModelProperty(value = "内容分析")
        private List<ContentAnalysisItem> contentAnalysis;
        
        @ApiModelProperty(value = "爆款视频分析")
        private List<HitVideoAnalysisItem> hitVideoAnalysis;

        @ApiModelProperty(value = "爆款模板拆解")
        private List<HotScriptTemplate> hotScriptTemplates;
        
        @ApiModelProperty(value = "商业化分析")
        private String commercialAnalysis;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContentAnalysisItem {
        @ApiModelProperty(value = "内容描述")
        private String content;
        
        @ApiModelProperty(value = "相关作品")
        private List<String> relationWorks;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HitVideoAnalysisItem {
        @ApiModelProperty(value = "内容描述")
        private String content;
        
        @ApiModelProperty(value = "相关作品")
        private List<String> relationWorks;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HotScriptTemplate {
        @ApiModelProperty(value = "模版类型")
        private String type;

        @ApiModelProperty(value = "模版特征描述")
        private String typeFeature;

        @ApiModelProperty(value = "相关作品")
        private List<String> relationWorks;
    }
}
