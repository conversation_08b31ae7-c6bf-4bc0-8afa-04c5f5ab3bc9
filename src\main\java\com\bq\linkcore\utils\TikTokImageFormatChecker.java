package com.bq.linkcore.utils;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TikTokImageFormatChecker {

    // TikTok 支持的图片格式映射表
    private static final Map<String, String> EXTENSION_TO_FORMAT = new HashMap<>();
    private static final Map<String, String> CONTENT_TYPE_TO_FORMAT = new HashMap<>();

    static {
        // 初始化扩展名到格式的映射
        EXTENSION_TO_FORMAT.put("jpg", "JPEG");
        EXTENSION_TO_FORMAT.put("jpeg", "JPEG");
        EXTENSION_TO_FORMAT.put("png", "PNG");
        EXTENSION_TO_FORMAT.put("gif", "GIF");
        EXTENSION_TO_FORMAT.put("webp", "WebP");
        EXTENSION_TO_FORMAT.put("heic", "HEIC");
        EXTENSION_TO_FORMAT.put("heif", "HEIF");
        EXTENSION_TO_FORMAT.put("avif", "AVIF");
        EXTENSION_TO_FORMAT.put("jfif", "JFIF");
        EXTENSION_TO_FORMAT.put("tiff", "TIFF");
        EXTENSION_TO_FORMAT.put("tif", "TIFF");
        EXTENSION_TO_FORMAT.put("bmp", "BMP");

        // 初始化Content-Type到格式的映射
        CONTENT_TYPE_TO_FORMAT.put("image/jpeg", "JPEG");
        CONTENT_TYPE_TO_FORMAT.put("image/jpg", "JPEG");
        CONTENT_TYPE_TO_FORMAT.put("image/png", "PNG");
        CONTENT_TYPE_TO_FORMAT.put("image/gif", "GIF");
        CONTENT_TYPE_TO_FORMAT.put("image/webp", "WebP");
        CONTENT_TYPE_TO_FORMAT.put("image/heic", "HEIC");
        CONTENT_TYPE_TO_FORMAT.put("image/heif", "HEIF");
        CONTENT_TYPE_TO_FORMAT.put("image/avif", "AVIF");
        CONTENT_TYPE_TO_FORMAT.put("image/jfif", "JFIF");
        CONTENT_TYPE_TO_FORMAT.put("image/tiff", "TIFF");
        CONTENT_TYPE_TO_FORMAT.put("image/bmp", "BMP");
        CONTENT_TYPE_TO_FORMAT.put("image/svg+xml", "SVG");
    }

    public static String getImageFormat(String imageUrl) {
        // 1. 尝试通过文件扩展名判断
        String formatFromExtension = getFormatFromUrlExtension(imageUrl);
        if (!"Unknown".equals(formatFromExtension)) {
            return formatFromExtension;
        }

        // 2. TikTok 特殊URL格式处理
        String tiktokFormat = getFormatFromTikTokUrl(imageUrl);
        if (!"Unknown".equals(tiktokFormat)) {
            return tiktokFormat;
        }

        // 3. 扩展名无法判断时，通过HTTP请求获取
        return getFormatFromHttpContentType(imageUrl);
    }

    // 处理TikTok特有URL格式
    private static String getFormatFromTikTokUrl(String imageUrl) {
        // TikTok URL 示例:
        // https://p16-sign-va.tiktokcdn.com/obj/tos-maliva-p-0068/7996b6e3a0c44a8d8d5c8a8c8d5c8a8c_1620000000?x-expires=1650000000&x-signature=abc123
        // https://v16m.tiktokcdn.com/img/tos-alisg-v-21000000/e853c9c2d9d945b0a2e8a0c5a5f8e9f8~tplv-noop.image

        // 匹配 ~tplv- 后的格式指示符
        Pattern pattern = Pattern.compile("~tplv-[a-z0-9-]+\\.([a-z]+)");
        Matcher matcher = pattern.matcher(imageUrl);
        if (matcher.find()) {
            String format = matcher.group(1).toLowerCase();
            return EXTENSION_TO_FORMAT.getOrDefault(format, "Unknown");
        }

        // 匹配 /obj/ 路径中的格式指示
        if (imageUrl.contains("/obj/")) {
            String[] parts = imageUrl.split("/");
            for (String part : parts) {
                if (part.contains(".") && part.lastIndexOf('.') < part.length() - 1) {
                    String ext = part.substring(part.lastIndexOf('.') + 1).toLowerCase();
                    if (EXTENSION_TO_FORMAT.containsKey(ext)) {
                        return EXTENSION_TO_FORMAT.get(ext);
                    }
                }
            }
        }

        return "Unknown";
    }

    private static String getFormatFromUrlExtension(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            String path = url.getPath();

            if (path != null && path.contains(".")) {
                // 获取最后一个点之后的部分
                String fileName = path.substring(path.lastIndexOf('/') + 1);
                String extension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();

                // 移除查询参数和锚点
                if (extension.contains("?")) {
                    extension = extension.substring(0, extension.indexOf('?'));
                }
                if (extension.contains("#")) {
                    extension = extension.substring(0, extension.indexOf('#'));
                }

                // 处理复合扩展名 (如 .jpg.webp)
                if (extension.contains(".")) {
                    String lastExt = extension.substring(extension.lastIndexOf('.') + 1);
                    if (EXTENSION_TO_FORMAT.containsKey(lastExt)) {
                        return EXTENSION_TO_FORMAT.get(lastExt);
                    }
                }

                return EXTENSION_TO_FORMAT.getOrDefault(extension, "Unknown");
            }
        } catch (Exception e) {
            // URL格式错误时忽略
        }
        return "Unknown";
    }

    private static String getFormatFromHttpContentType(String imageUrl) {
        HttpURLConnection connection = null;
        try {
            URL url = new URL(imageUrl);
            connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法为HEAD（只获取头部信息）
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(3000);  // 3秒连接超时
            connection.setReadTimeout(5000);     // 5秒读取超时

            // 设置TikTok用户代理（有些CDN需要）
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                String contentType = connection.getContentType();
                if (contentType != null) {
                    // 移除内容类型中的额外参数（如 charset）
                    String mimeType = contentType.split(";")[0].trim().toLowerCase();

                    // 从映射表中获取格式
                    String format = CONTENT_TYPE_TO_FORMAT.get(mimeType);
                    if (format != null) {
                        return format;
                    }

                    // 处理特殊Content-Type情况
                    if (mimeType.startsWith("image/")) {
                        return mimeType.substring(6).toUpperCase();
                    }
                }
            }
        } catch (IOException e) {
            System.err.println("HTTP请求失败: " + e.getMessage());
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
        return "Unknown";
    }

    public static void main(String[] args) {
        // TikTok 图片URL测试用例
        String[] tiktokUrls = {
                // 标准URL格式
                "https://p16-sign.tiktokcdn.com/obj/tos-maliva-p-0068/7996b6e3a0c44a8d8d5c8a8c8d5c8a8c_1620000000.webp",
                "https://v16m.tiktokcdn.com/img/tos-alisg-v-21000000/e853c9c2d9d945b0a2e8a0c5a5f8e9f8~tplv-noop.image",

                // 无扩展名URL（常见于TikTok CDN）
                "https://p16-tiktok-common.ibyteimg.com/img/tos-alisg-v-21000000/e853c9c2d9d945b0a2e8a0c5a5f8e9f8",
                "https://p16-tiktok.ibyteimg.com/obj/video-artifact/1234567890abcdef",

                // 特殊格式URL
                "https://v19.tiktokcdn.com/tr/non-sg/1234567890abcdef_1234567890.heic",
                "https://p16-tiktok-sg.ibyteimg.com/obj/tos-alisg-i-0000/1234567890abcdef1234567890abcdef~tplv-avif.image",

                // 带查询参数的URL
                "https://p16-amd-va.tiktokcdn.com/img/tos-maliva-p-0068/7996b6e3a0c44a8d8d5c8a8c8d5c8a8c_1620000000?x-expires=1650000000&x-signature=abc123",

                // 复合扩展名
                "https://cdn.tiktok.com/image.jpg.webp",

                // 可能的新格式
                "https://p16-tiktok-common.ibyteimg.com/obj/video-artifact/abcdef123456.avif"
        };

        System.out.println("TikTok 图片格式检测:");
        for (String url : tiktokUrls) {
            String format = getImageFormat(url);
            System.out.println("URL: " + url);
            System.out.println("格式: " + format);
            System.out.println("------");
        }
    }
}
