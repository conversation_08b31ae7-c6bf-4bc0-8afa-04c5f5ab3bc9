<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.TiktokArticleTagRelMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.TiktokArticleTagRelDO">
        <id column="id" property="id" />
        <result column="work_id" property="workId" />
        <result column="tag" property="tag" />
        <result column="create_time" property="createTime" />
        <result column="is_del" property="isDel" />
    </resultMap>
    <sql id="Base_Column_List">
        id, work_id, tag, create_time, is_del
    </sql>

  

  
</mapper>
