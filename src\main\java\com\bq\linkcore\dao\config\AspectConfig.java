package com.bq.linkcore.dao.config;

import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import org.springframework.aop.aspectj.AspectJExpressionPointcut;
import org.springframework.aop.support.DefaultPointcutAdvisor;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.TransactionManagementConfigurer;
import org.springframework.transaction.interceptor.*;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


@Configuration
@AutoConfigureAfter(MybatisPlusProperties.class)
@EnableTransactionManagement
public class AspectConfig implements TransactionManagementConfigurer{

	@Resource
    private DataSource DataSource;


    @Bean
    @Override
    @ConditionalOnMissingBean
    public PlatformTransactionManager annotationDrivenTransactionManager() {
        return new DataSourceTransactionManager(DataSource);
    }
//
//    @Bean
//    @ConditionalOnMissingBean
//    public PlatformTransactionManager transactionManager() {
//        return new DataSourceTransactionManager(DataSource);
//    }

    @Bean("customTransactionInterceptor")
    public TransactionInterceptor transactionInterceptor(PlatformTransactionManager transactionManager) {

		NameMatchTransactionAttributeSource source =new NameMatchTransactionAttributeSource();
		Map<String, TransactionAttribute> nameMap =new HashMap<>(16);
		//只读事物、不做更新删除等
		//事务管理规则
		RuleBasedTransactionAttribute readOnlyRule =new RuleBasedTransactionAttribute();
		//设置当前事务是否为只读事务，true为只读
		readOnlyRule.setReadOnly(true);
		// transactiondefinition 定义事务的隔离级别；
		//PROPAGATION_REQUIRED 如果当前没有事务，就新建一个事务，如果已经存在一个事务中，加入到这个事务中
		readOnlyRule.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
		RuleBasedTransactionAttribute requireRule =new RuleBasedTransactionAttribute();
		//抛出异常后执行切点回滚
		requireRule.setRollbackRules(Collections.singletonList(new RollbackRuleAttribute(Exception.class)));
		//PROPAGATION_REQUIRED:事务隔离性为1，若当前存在事务，则加入该事务；如果当前没有事务，则创建一个新的事务。这是默认值。
		requireRule.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
		//设置事务失效时间，超过10秒,可根据hytrix，则回滚事务
		requireRule.setTimeout(10);
		nameMap.put("addTx*", requireRule);
		nameMap.put("saveTx*", requireRule);
		nameMap.put("insertTx*", requireRule);
		nameMap.put("updateTx*", requireRule);
		nameMap.put("deleteTx*", requireRule);
		nameMap.put("removeTx*", requireRule);
		nameMap.put("postTx*", requireRule);
		nameMap.put("putTx*", requireRule);
		nameMap.put("applyTx*", requireRule);
		//进行批量操作时
		nameMap.put("batchTx*", requireRule);
		nameMap.put("getTx*", readOnlyRule);
		nameMap.put("queryTx*", readOnlyRule);
		nameMap.put("findTx*", readOnlyRule);
		nameMap.put("selectTx*", readOnlyRule);
		nameMap.put("countTx*", readOnlyRule);
		nameMap.put("notifyTx*", readOnlyRule);
		nameMap.put("canTx*", readOnlyRule);
		source.setNameMap(nameMap);
		return new TransactionInterceptor(transactionManager, source);
    }


	@Bean
	public AspectJExpressionPointcut aspectJExpressionPointcut(){
		AspectJExpressionPointcut pointcut = new AspectJExpressionPointcut();
        String transactionExecution = "execution(* com.bq.data.*.services.impl.*.*(..))";
		pointcut.setExpression(transactionExecution);
		return pointcut;
	}

	@Bean
	public DefaultPointcutAdvisor defaultPointcutAdvisor(AspectJExpressionPointcut aspectJExpressionPointcut, TransactionInterceptor customTransactionInterceptor){
		DefaultPointcutAdvisor advisor = new DefaultPointcutAdvisor();
		advisor.setPointcut(aspectJExpressionPointcut);
		advisor.setAdvice(customTransactionInterceptor);
		return advisor;
	}

}
