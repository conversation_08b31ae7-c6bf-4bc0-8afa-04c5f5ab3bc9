package com.bq.linkcore.services.impl.aiAction.models;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 爆款脚本模版分析结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "爆款脚本模版分析结果")
public class ScriptTemplateAnalysisVo {
    
    @ApiModelProperty(value = "账号唯一ID")
    private String uniqueId;
    
    @ApiModelProperty(value = "爆款阈值")
    private Long hitThreshold;
    
    @ApiModelProperty(value = "分析视频数量")
    private Integer totalVideoCount;
    
    @ApiModelProperty(value = "脚本模版列表")
    private List<ScriptTemplate> scriptTemplates;
    
    @ApiModelProperty(value = "分析报告JSON")
    private String analysisReportJson;
    
    @ApiModelProperty(value = "分析报告原始文本")
    private String analysisReportText;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ScriptTemplate {

        @ApiModelProperty(value = "模版类型")
        private String type;

        @ApiModelProperty(value = "模版特征描述")
        private String typeFeature;

        @ApiModelProperty(value = "关联作品URL")
        private List<String> relationWorks;

    }
}
