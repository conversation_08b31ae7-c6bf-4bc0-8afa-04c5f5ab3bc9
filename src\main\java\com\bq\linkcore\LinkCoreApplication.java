package com.bq.linkcore;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.redisson.spring.starter.RedissonAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@SpringBootApplication(scanBasePackages = {"com.bq"})
@MapperScan({"com.bq.linkcore.dao.mapper"})
@Slf4j
@EnableScheduling
@EnableSwagger2
@EnableAsync
@EnableRetry
public class LinkCoreApplication {

    public static void main(String[] args) {
        SpringApplication.run(LinkCoreApplication.class, args);
    }

}
