package com.bq.linkcore.utils;

import com.bq.linkcore.client.speechmatics.model.VideoCopyModel;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SpeechmaticsHelper 修复测试类
 */
public class SpeechmaticsHelperFixTest {

    @Test
    public void testExtractOriginalTranscriptWithCorrectFormat() {
        // 使用实际的 JSON 格式，attaches_to 是字符串 "previous"
        String transcriptionResult = "{\n" +
                "    \"results\": [\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"Okay\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 0.88,\n" +
                "            \"start_time\": 0.16,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \".\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"attaches_to\": \"previous\",\n" +
                "            \"end_time\": 0.88,\n" +
                "            \"is_eos\": true,\n" +
                "            \"start_time\": 0.88,\n" +
                "            \"type\": \"punctuation\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"How\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 1.24,\n" +
                "            \"start_time\": 1.04,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"cute\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 1.52,\n" +
                "            \"start_time\": 1.24,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"is\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 1.72,\n" +
                "            \"start_time\": 1.52,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"this\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 2.16,\n" +
                "            \"start_time\": 1.72,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"?\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"attaches_to\": \"previous\",\n" +
                "            \"end_time\": 2.16,\n" +
                "            \"is_eos\": true,\n" +
                "            \"start_time\": 2.16,\n" +
                "            \"type\": \"punctuation\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";

        // 测试提取原文
        List<VideoCopyModel> originalTranscript = SpeechmaticsHelper.extractOriginalTranscript(transcriptionResult);
        
        assertNotNull(originalTranscript, "原文列表不应为空");
        assertEquals(2, originalTranscript.size(), "应该提取到2个原文段落");
        
        // 验证第一个原文段落
        VideoCopyModel firstOriginal = originalTranscript.get(0);
        assertEquals("Okay.", firstOriginal.getContent(), "第一个原文内容应该正确");
        assertEquals(0.16, firstOriginal.getStartTime(), 0.001, "第一个原文开始时间应该正确");
        assertEquals(0.88, firstOriginal.getEndTime(), 0.001, "第一个原文结束时间应该正确");
        
        // 验证第二个原文段落
        VideoCopyModel secondOriginal = originalTranscript.get(1);
        assertEquals("How cute is this?", secondOriginal.getContent(), "第二个原文内容应该正确");
        assertEquals(1.04, secondOriginal.getStartTime(), 0.001, "第二个原文开始时间应该正确");
        assertEquals(2.16, secondOriginal.getEndTime(), 0.001, "第二个原文结束时间应该正确");
        
        System.out.println("修复后的原文提取测试通过！");
        for (int i = 0; i < originalTranscript.size(); i++) {
            VideoCopyModel copy = originalTranscript.get(i);
            System.out.println(String.format("原文段落 %d: [%.2f-%.2f] %s", 
                    i + 1, copy.getStartTime(), copy.getEndTime(), copy.getContent()));
        }
    }

    @Test
    public void testExtractOriginalTranscriptWithComplexPunctuation() {
        // 测试更复杂的标点符号情况
        String transcriptionResult = "{\n" +
                "    \"results\": [\n" +
                "        {\n" +
                "            \"alternatives\": [{\"content\": \"Hello\", \"confidence\": 1.0}],\n" +
                "            \"end_time\": 1.0, \"start_time\": 0.0, \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [{\"content\": \",\", \"confidence\": 1.0}],\n" +
                "            \"attaches_to\": \"previous\", \"end_time\": 1.0, \"start_time\": 1.0, \"type\": \"punctuation\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [{\"content\": \"world\", \"confidence\": 1.0}],\n" +
                "            \"end_time\": 2.0, \"start_time\": 1.5, \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [{\"content\": \"!\", \"confidence\": 1.0}],\n" +
                "            \"attaches_to\": \"previous\", \"end_time\": 2.0, \"is_eos\": true,\n" +
                "            \"start_time\": 2.0, \"type\": \"punctuation\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";

        List<VideoCopyModel> originalTranscript = SpeechmaticsHelper.extractOriginalTranscript(transcriptionResult);
        
        assertNotNull(originalTranscript, "原文列表不应为空");
        assertEquals(1, originalTranscript.size(), "应该提取到1个原文段落");
        
        VideoCopyModel original = originalTranscript.get(0);
        assertEquals("Hello, world!", original.getContent(), "原文内容应该正确处理标点符号");
        assertEquals(0.0, original.getStartTime(), 0.001, "开始时间应该正确");
        assertEquals(2.0, original.getEndTime(), 0.001, "结束时间应该正确");
        
        System.out.println("复杂标点符号测试通过！");
        System.out.println(String.format("原文: [%.2f-%.2f] %s", 
                original.getStartTime(), original.getEndTime(), original.getContent()));
    }

    @Test
    public void testExtractOriginalTranscriptWithMissingFields() {
        // 测试缺少某些字段的情况
        String transcriptionResult = "{\n" +
                "    \"results\": [\n" +
                "        {\n" +
                "            \"alternatives\": [{\"content\": \"Test\", \"confidence\": 1.0}],\n" +
                "            \"end_time\": 1.0, \"start_time\": 0.0, \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [{\"content\": \".\", \"confidence\": 1.0}],\n" +
                "            \"end_time\": 1.0, \"start_time\": 1.0, \"type\": \"punctuation\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";

        List<VideoCopyModel> originalTranscript = SpeechmaticsHelper.extractOriginalTranscript(transcriptionResult);
        
        assertNotNull(originalTranscript, "原文列表不应为空");
        assertEquals(1, originalTranscript.size(), "应该提取到1个原文段落");
        
        VideoCopyModel original = originalTranscript.get(0);
        assertEquals("Test .", original.getContent(), "缺少 attaches_to 字段时应该用空格分隔");
        
        System.out.println("缺少字段测试通过！");
        System.out.println(String.format("原文: [%.2f-%.2f] %s", 
                original.getStartTime(), original.getEndTime(), original.getContent()));
    }
}
