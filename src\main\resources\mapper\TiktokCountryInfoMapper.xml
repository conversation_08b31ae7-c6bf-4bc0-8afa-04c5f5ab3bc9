<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.TiktokCountryInfoMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.TiktokCountryInfoDO">
        <id column="id" property="id" />
        <result column="country_id" property="countryId" />
        <result column="country_value" property="countryValue" />
        <result column="country_label" property="countryLabel" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, country_id, country_value, country_label, create_time, update_time
    </sql>

  

  
</mapper>
