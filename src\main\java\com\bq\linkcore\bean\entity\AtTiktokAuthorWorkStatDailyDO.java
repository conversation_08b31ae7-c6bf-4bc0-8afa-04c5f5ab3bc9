package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-15 17:03:42
 * @ClassName: AtTiktokAuthorWorkStatDailyDO
 * @Description: 作品数据每日统计
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("at_tiktok_author_work_stat_daily")
public class AtTiktokAuthorWorkStatDailyDO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 作品唯一标识
     */
    private String workId;

    /**
     * 三方生成唯一ID
     */
    private String workUuid;

    /**
     * 发布账号ID
     */
    private String authorId;

    /**
     * 作者唯一用户名
     */
    private String uniqueId;

    /**
     * 作者账号加密ID
     */
    private String secUid;

    /**
     * 播放量
     */
    private Integer playCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 转发数
     */
    private Integer shareCount;

    /**
     * 收藏数
     */
    private Integer collectCount;

    /**
     * 统计日期（天）
     */
    private String statDay;

    /**
     * 统计时间
     */
    private String statTime;

    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;




}
