package com.bq.linkcore.services.impl.monitor.provider.tiktok;

import com.bq.linkcore.bean.dto.AuthorTaggingModel;
import com.bq.linkcore.bean.entity.*;
import com.bq.linkcore.client.tikhub.models.AuthorInfo;
import com.bq.linkcore.client.tikhub.models.TikHubArticle;
import com.bq.linkcore.client.tikhub.models.TikHubArticleComment;
import com.bq.linkcore.client.tikhub.tiktok.TikHubTiktokAccountRequester;
import com.bq.linkcore.client.tikhub.tiktok.TikHubTiktokArticleRequester;
import com.bq.linkcore.common.AuthorUpdateStatusEnum;
import com.bq.linkcore.services.pool.ThreadRejectPolicy;
import com.bq.linkcore.services.pool.WorkThreadFactory;
import com.bq.linkcore.services.pool.WorkThreadPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * TikTok监控提供者
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Slf4j
@Service
public class TiktokMonitoringProvider {

    private final static String TIKTOK_THREAD_TAG = "tiktok-provider";

    /**
     * TikTok专用线程池
     */
    private final WorkThreadPool tiktokThreadPool = new WorkThreadPool(
            2,
            8,
            2L,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(5000),
            new WorkThreadFactory(TIKTOK_THREAD_TAG),
            new ThreadRejectPolicy(TIKTOK_THREAD_TAG)
    );

    @Resource
    private TikHubTiktokAccountRequester accountRequester;

    @Resource
    private TikHubTiktokArticleRequester tikHubTiktokArticleRequester;

    @Resource
    private TiktokDataStorageHelper tiktokDataStorageService;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 处理用户列表 - 批量处理作品主页 & 作品数据更新
     */
    public void processAuthorAndWorksBatchAsync(List<AuthorTaggingModel> authorTaggingModels, Integer isDaily) {
        log.info("TikTok提供者开始处理用户，数量: {}", authorTaggingModels.size());

        for (AuthorTaggingModel authorTaggingModel : authorTaggingModels) {
            tiktokThreadPool.addTask(() -> {
                try {
                    processAuthorInternal(authorTaggingModel, isDaily);
                } catch (Exception e) {
                    log.error("TikTok提供者处理用户异常，authorTaggingModel: {}", authorTaggingModel, e);
                }
            });
        }
    }

    /**
     * 处理用户列表 - 批量处理作品主页数据更新
     */
    public void processAuthorAndWorksBatchSync(List<AuthorTaggingModel> authorTaggingModels, Integer isDaily) {
        log.info("TikTok提供者开始处理用户，数量: {}", authorTaggingModels.size());

        for (AuthorTaggingModel authorTaggingModel : authorTaggingModels) {
            try {
                processAuthorInternal(authorTaggingModel, isDaily);
                Thread.sleep(1000);
            } catch (Exception e) {
                log.error("TikTok提供者处理用户异常，authorTaggingModel: {}", authorTaggingModel, e);
            }
        }
    }


    /**
     * 处理达人主页数据更新或者新增
     */
    public void processAuthorProfilesBatchAsync(List<AuthorTaggingModel> authorTaggingModels, Integer isDaily) {
        log.info("TikTok提供者开始处理作者信息，数量: {}", authorTaggingModels.size());

        for (AuthorTaggingModel authorTaggingModel : authorTaggingModels) {
            tiktokThreadPool.addTask(() -> {
                try {
                    processAuthorProfileInternal(authorTaggingModel.getUniqueId(), isDaily);
                } catch (Exception e) {
                    log.error("TikTok提供者处理作者信息异常，uniqueId: {}", authorTaggingModel.getUniqueId(), e);
                }
            });
        }
    }

    /**
     * 处理达人主页数据更新或者新增
     */
    public void processAuthorProfilesBatchSync(List<AuthorTaggingModel> authorTaggingModels, Integer isDaily) {
        log.info("TikTok提供者开始处理作者信息，数量: {}", authorTaggingModels.size());

        for (AuthorTaggingModel authorTaggingModel : authorTaggingModels) {
            try {
                processAuthorProfileInternal(authorTaggingModel.getUniqueId(), isDaily);
                Thread.sleep(1000);
            } catch (Exception e) {
                log.error("TikTok提供者处理作者信息异常，uniqueId: {}", authorTaggingModel.getUniqueId(), e);
            }
        }
    }

    /**
     * 批量处理用户作品数据更新-只处理数据更新
     */
    public void processWorksBatch(List<String> uniqueIds) {
        log.info("TikTok提供者开始处理用户作品，数量: {}", uniqueIds.size());

        for (String uniqueId : uniqueIds) {
            tiktokThreadPool.addTask(() -> {
                try {
                    processAuthorWorksInternal(uniqueId, null);
                } catch (Exception e) {
                    log.error("TikTok提供者处理用户作品异常，uniqueId: {}", uniqueId, e);
                }
            });
        }
    }

    /**
     * 内部用户处理逻辑：更新达人详情 & 作品数据
     */
    private void processAuthorInternal(AuthorTaggingModel authorTaggingModel, Integer isDaily) {
        processAuthorInternal(authorTaggingModel.getUniqueId(), authorTaggingModel.getSecUid(), isDaily);
    }


    /**
     * 内部用户处理逻辑：更新达人详情 & 作品数据
     */
    private void processAuthorInternal(String uniqueId, String secUid, Integer isDaily) {
        try {
            log.debug("开始处理TikTok用户: uniqueId={}", uniqueId);

            AtTiktokAuthorUpdateRecordDO authorUpdateRecordDO = tiktokDataStorageService.queryAuthorUpdateRecord(uniqueId);
            if (authorUpdateRecordDO != null) {
                if (authorUpdateRecordDO.getStatus().equals(AuthorUpdateStatusEnum.Waiting.getCode()) ||
                        authorUpdateRecordDO.getStatus().equals(AuthorUpdateStatusEnum.Updating.getCode())) {
                    log.debug("任务已经等待或执行中，TikTok用户 uniqueId={}", uniqueId);
                    return;
                }
            }

            AtTiktokAuthorPoolDO atTiktokAuthorPoolDO = tiktokDataStorageService.getUserProfile(uniqueId);
            AtTiktokAuthorUpdateRecordDO atTiktokAuthorUpdateRecordDO = tiktokDataStorageService.insertAuthorUpdateRecord(0L, atTiktokAuthorPoolDO);

            atTiktokAuthorUpdateRecordDO.setStatus(AuthorUpdateStatusEnum.Updating.getCode());
            tiktokDataStorageService.updateAuthorUpdateRecord(atTiktokAuthorUpdateRecordDO);

            // 获取用户基本信息
            AuthorInfo userProfile = accountRequester.getUserProfileWebV1(uniqueId, secUid);
            if (userProfile != null) {
                tiktokDataStorageService.saveUserProfile(userProfile, isDaily);
            } else {
                tiktokDataStorageService.insertAuthorRefreshFailureRecord(atTiktokAuthorPoolDO);
                return;
            }

            // 获取用户作品 - 使用现有的TiktokAuthorWorkService
            log.debug("开始TikTok作品监控: {}", uniqueId);
            processAuthorWorksInternal(uniqueId, secUid);

            atTiktokAuthorUpdateRecordDO.setStatus(AuthorUpdateStatusEnum.Success.getCode());
            tiktokDataStorageService.updateAuthorUpdateRecord(atTiktokAuthorUpdateRecordDO);

            // 更新处理完成
            log.debug("完成处理TikTok用户: uniqueId={}", uniqueId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 作者主页数据 - 单个达人详情数据，不包含作品
     */
    private void processAuthorProfileInternal(String uniqueId, Integer isDaily) {
        log.debug("开始处理TikTok作者信息: {}", uniqueId);

        AuthorInfo userProfile = accountRequester.getUserProfileWebV1(uniqueId, null);
        if (userProfile != null) {
            tiktokDataStorageService.saveUserProfile(userProfile, isDaily);
            log.debug("成功保存TikTok作者信息: {}", uniqueId);
        } else {
            log.warn("未获取到TikTok作者信息: {}", uniqueId);
        }
    }

    /**
     * 批量处理用户
     */
    public void batchProcessUsers(List<AuthorTaggingModel> authorTaggingModels, int batchSize, Integer isDaily) {
        log.info("TikTok提供者开始批量处理用户，总数量: {}, 批次大小: {}", authorTaggingModels.size(), batchSize);

        for (int i = 0; i < authorTaggingModels.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, authorTaggingModels.size());
            List<AuthorTaggingModel> batch = authorTaggingModels.subList(i, endIndex);

            tiktokThreadPool.addTask(() -> {
                try {
                    processBatchInternal(batch, isDaily);
                } catch (Exception e) {
                    log.error("TikTok提供者批量处理异常，{}", endIndex, e);
                }
            });
            try {
                Thread.sleep(1000);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 内部批次处理逻辑
     */
    private void processBatchInternal(List<AuthorTaggingModel> authorTaggingModels, Integer isDaily) {
        log.debug("开始处理TikTok批次，数量: {}", authorTaggingModels.size());

        for (AuthorTaggingModel authorTaggingModel : authorTaggingModels) {
            try {
                processAuthorInternal(authorTaggingModel, isDaily);

                // 添加延迟避免频繁请求
                Thread.sleep(800);

            } catch (Exception e) {
                log.error("批次处理单个用户异常，uniqueId: {}", authorTaggingModel.getUniqueId(), e);
            }
        }

        log.debug("完成处理TikTok批次，数量: {}", authorTaggingModels.size());
    }

    /**
     * 处理特定用户的作品监控
     */
    public void processAuthorWorksAsync(String uniqueId, String secUid) {
        tiktokThreadPool.addTask(() -> {
            try {
                AtTiktokAuthorPoolDO atTiktokAuthorPoolDO = tiktokDataStorageService.getUserProfile(uniqueId);
                AtTiktokAuthorUpdateRecordDO atTiktokAuthorUpdateRecordDO = tiktokDataStorageService.insertAuthorUpdateRecord(0L, atTiktokAuthorPoolDO);

                atTiktokAuthorUpdateRecordDO.setStatus(AuthorUpdateStatusEnum.Updating.getCode());
                tiktokDataStorageService.updateAuthorUpdateRecord(atTiktokAuthorUpdateRecordDO);

                log.debug("开始TikTok作品监控: {}", uniqueId);
                processAuthorWorksInternal(uniqueId, secUid);


                atTiktokAuthorUpdateRecordDO.setStatus(AuthorUpdateStatusEnum.Success.getCode());
                tiktokDataStorageService.updateAuthorUpdateRecord(atTiktokAuthorUpdateRecordDO);
            } catch (Exception e) {
                log.error("TikTok作品监控异常，uniqueId: {}", uniqueId, e);
            }
        });
    }

    public void processRefreshWorkSync(String workId) {
        processRefreshWorkDetailInternal(workId);
    }

    public void processRefreshWorkDetailInternal(String workId) {
        TikHubArticle tikHubArticle = queryArticleDetail(workId);
        try {
            String recordDay = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            AtTiktokAuthorWorkRecordDO workRecordDO = tiktokDataStorageService.processWorkRecord(tikHubArticle, tikHubArticle.getUniqueId());
            tiktokDataStorageService.processWorkHistory(workRecordDO, recordDay);
        } catch (Exception e) {
            log.error("处理单个作品数据异常，workId: {}, uniqueId: {}",
                    tikHubArticle.getWorkId(), workId, e);
        }
    }

    /**
     * 处理特定用户的作品监控
     */
    public void processAuthorWorksSync(String uniqueId, String secUid) {
        tiktokThreadPool.addTask(() -> {
            try {
                AtTiktokAuthorPoolDO atTiktokAuthorPoolDO = tiktokDataStorageService.getUserProfile(uniqueId);
                AtTiktokAuthorUpdateRecordDO atTiktokAuthorUpdateRecordDO = tiktokDataStorageService.insertAuthorUpdateRecord(0L, atTiktokAuthorPoolDO);

                atTiktokAuthorUpdateRecordDO.setStatus(AuthorUpdateStatusEnum.Updating.getCode());
                tiktokDataStorageService.updateAuthorUpdateRecord(atTiktokAuthorUpdateRecordDO);

                log.debug("开始TikTok作品监控: {}", uniqueId);
                processAuthorWorksInternal(uniqueId, secUid);


                atTiktokAuthorUpdateRecordDO.setStatus(AuthorUpdateStatusEnum.Success.getCode());
                tiktokDataStorageService.updateAuthorUpdateRecord(atTiktokAuthorUpdateRecordDO);
            } catch (Exception e) {
                log.error("TikTok作品监控异常，uniqueId: {}", uniqueId, e);
            }
        });
    }

    /**
     * 内部接口实现 更新达人的作品列表
     *
     * @param uniqueId
     * @param secUid
     */
    private void processAuthorWorksInternal(String uniqueId, String secUid) {
        List<TikHubArticle> tikHubArticleList = getArticleDetailsList(uniqueId, secUid);

        if (CollectionUtils.isEmpty(tikHubArticleList)) {
            log.warn("未获取到作品数据，uniqueId: {}, secUid: {}", uniqueId, secUid);
            return;
        }

        log.info("获取到作品数据 {} 条，uniqueId: {}", tikHubArticleList.size(), uniqueId);

        String recordDay = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        for (TikHubArticle tikHubArticle : tikHubArticleList) {
            try {
                AtTiktokAuthorWorkRecordDO workRecordDO = tiktokDataStorageService.processWorkRecord(tikHubArticle, uniqueId);
                tiktokDataStorageService.processWorkHistory(workRecordDO, recordDay);
            } catch (Exception e) {
                log.error("处理单个作品数据异常，workId: {}, uniqueId: {}",
                        tikHubArticle.getWorkId(), uniqueId, e);
            }
        }

        log.info("完成处理作者作品数据，uniqueId: {}, 处理数量: {}", uniqueId, tikHubArticleList.size());
    }

    public TikHubArticle queryArticleDetail(String workId) {
        TikHubArticle tikHubArticle = tikHubTiktokArticleRequester.queryArticleAppV3(workId);
        if (tikHubArticle == null) {
            tikHubArticle = tikHubTiktokArticleRequester.queryArticleWebV1(workId);
        }

        return tikHubArticle;
    }

    /**
     * 获取作品详情（包含mock数据）
     */
    private List<TikHubArticle> getArticleDetailsList(String uniqueId, String secUid) {
        try {
            // 调用真实API（目前返回空列表）
            List<TikHubArticle> realData = accountRequester.getUserArticlesByAppV3(uniqueId, secUid, 50);

            // 如果真实API有数据，直接返回
            if (realData != null && !realData.isEmpty()) {
                return realData;
            }

            return null;

        } catch (Exception e) {
            log.error("获取作品详情异常，uniqueId: {}, secUid: {}", uniqueId, secUid, e);
            // 异常情况下也返回mock数据
            return null;
        }
    }


    /**
     * 处理特定用户的作品监控
     */
    public void processArtileCommentAsync(String workId, Integer count) {
        if (queryIsUpdatingComment(workId)) {
            return;
        }

        tiktokThreadPool.addTask(() -> {
            try {
                setIsUpdatingComment(workId);

                List<TikHubArticleComment> articleComments = tikHubTiktokArticleRequester.queryArticleCommentWebV1(workId, count);
                if (articleComments == null) {
                    articleComments = tikHubTiktokArticleRequester.queryArticleCommentAppV3(workId, count);
                }

                if (CollectionUtils.isNotEmpty(articleComments)) {
                    log.debug("开始TikTok更新评论: {}", workId);
                    articleComments.forEach(tikHubArticleComment -> {
                        TikhubArticleCommentDO articleCommentDO = new TikhubArticleCommentDO();
                        BeanUtils.copyProperties(tikHubArticleComment, articleCommentDO);

                        tiktokDataStorageService.updateArticleComment(articleCommentDO);
                    });
                }
            } catch (Exception e) {
                log.error("TikTok作品更新评论:异常，workId: {}, {}", workId, e);
            }

            resetIsUpdatingComment(workId);
        });
    }

    /**
     * 停止提供者
     */
    public void shutdown() {
        log.info("开始停止TikTok监控提供者");
        tiktokThreadPool.stop();
        log.info("TikTok监控提供者已停止");
    }

    public boolean queryIsUpdatingComment(String workId) {
        String key = "API_KEY_UPDATE_ARTICLE_COMMENT_" + workId;
        RBucket<String> bucket = redissonClient.getBucket(key);
        return bucket.isExists();
    }

    private void setIsUpdatingComment(String workId) {
        String key = "API_KEY_UPDATE_ARTICLE_COMMENT_" + workId;
        RBucket<String> bucket = redissonClient.getBucket(key);
        bucket.set(workId, 5, TimeUnit.MINUTES);
    }

    private void resetIsUpdatingComment(String workId) {
        String key = "API_KEY_UPDATE_ARTICLE_COMMENT_" + workId;
        RBucket<String> bucket = redissonClient.getBucket(key);
        bucket.delete();
    }
}
