package com.bq.linkcore.controller;

import com.bq.data.base.bean.BaseEnterpriseUser;
import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.vo.WorkDashboardRespVo;
import com.bq.linkcore.bean.vo.WorkInfoVo;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ServiceException;
import com.bq.linkcore.services.IArticleService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.bq.linkcore.common.ResponseMsg.ERROR_UNKNOWN_TOKEN;
import static com.bq.linkcore.common.ResponseMsg.FAIL;

@Api(value = "作品相关接口", tags = "作品相关接口")
@RestController
@Slf4j
@RequestMapping("/article")
public class ArticleController extends BaseController {

    @Resource
    private IArticleService articleService;

    @ApiOperation(value = "作品")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回信息", response = WorkDashboardRespVo.class)})
    @GetMapping("/info")
    public ResponseData queryTxArticleInfo(
            @ApiParam(value = "达人唯一ID", required = true) @RequestParam("atUniqueId") String atUniqueId,
            @ApiParam(value = "作品唯一ID", required = true) @RequestParam("workId") String workId) {
        try {
            log.info("/author/dashboard/topicTagList, atUniqueId={}", atUniqueId);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            WorkDashboardRespVo workInfoVo = articleService.queryTxArticleInfo(baseUser.getId(), workId, atUniqueId);
            return RD.ok(workInfoVo);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }


    @ApiOperation(value = "作品刷新接口")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回信息", response = Void.class)})
    @GetMapping("/sync")
    public ResponseData syncArticleInfo(
            @ApiParam(value = "达人唯一ID", required = true) @RequestParam("atUniqueId") String atUniqueId,
            @ApiParam(value = "作品唯一ID", required = true) @RequestParam("workId") String workId) {
        try {
            log.info("/author/dashboard/topicTagList, atUniqueId={}", atUniqueId);
            BaseEnterpriseUser baseUser = getTokenBaseUser();
            if (baseUser == null) {
                return RD.fail(ERROR_UNKNOWN_TOKEN.getCode());
            }
            return articleService.syncArticleInfo(baseUser.getId(), workId, atUniqueId);
        } catch (ServiceException e) {
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return RD.fail(FAIL.getCode(), e.getMessage());
        }
    }
}
