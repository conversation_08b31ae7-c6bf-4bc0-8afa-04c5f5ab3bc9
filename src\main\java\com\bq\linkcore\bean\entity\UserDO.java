package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-13 00:16:22
 * @ClassName: UserDO
 * @Description: 用户表
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("user")
public class UserDO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 自增长ID
     */
    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 手机号或第三方账号或企业员工名称，用于显示个人信息页
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户头像url
     */
    private String avatar;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 删除标记：1已删除，0=正常
     */
    private Integer isDel;

    /**
     * 备注
     */
    private String remark;

    /**
     * RAM账号
     */
    private String ramAccount;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Long updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;




}
