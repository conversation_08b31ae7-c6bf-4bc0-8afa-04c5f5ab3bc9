package com.bq.linkcore.utils;

/**
 * <AUTHOR>
 * @date 2025/5/28 12:11
 * @className XHSDateParser
 * @description
 */
public class XHSNumberParser {
    public static int parseNumber(String text) {
        text = text.toLowerCase().replace("+", "").trim();

        double number = 0;
        try {
            if (text.endsWith("万") || text.endsWith("w")) {
                String numPart = text.substring(0, text.length() - 1);
                number = Double.parseDouble(numPart) * 10_000;
            } else if (text.endsWith("k")) {
                String numPart = text.substring(0, text.length() - 1);
                number = Double.parseDouble(numPart) * 1_000;
            } else {
                // 纯数字，直接转
                number = Double.parseDouble(text);
            }
        } catch (NumberFormatException e) {
            // 无法解析时返回0或者-1，根据需求调整
            return 0;
        }

        return (int) number;
    }

    public static void main(String[] args) {
        String[] testStrs = {"1.3万", "1.9w", "10+", "1w+", "1k+", "500"};

        for (String s : testStrs) {
            int value = parseNumber(s);
            System.out.println(s + " -> " + value);
        }
    }
}

