package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-17 15:48:22
 * @ClassName: UserMonitorRuleSettingDO
 * @Description: 用户自定义配置
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("user_monitor_rule_setting")
public class UserMonitorRuleSettingDO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 自增长ID
     */
    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 视频爆款阈值
     */
    private String hotVideoRule;

    /**
     * 达人粉丝阈值规则
     */
    private String authorFansRule;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Long updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否被删除
     */
    private Integer isDel;




}
