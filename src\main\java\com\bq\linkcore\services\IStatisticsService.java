package com.bq.linkcore.services;

import com.bq.data.base.bean.ResponseData;

public interface IStatisticsService {

    /**
     * 查询作品数据信息
     *
     * @param userId
     * @param atUniqueId
     * @return
     */
    ResponseData refreshAuthorStatistics(Long userId, String atUniqueId, String recordDay);

    /**
     * 查询作品数据信息
     *
     * @param userId
     * @param atUniqueId
     * @return
     */
    ResponseData refreshWorkStatistics(Long userId, String atUniqueId, String recordDay);


}
