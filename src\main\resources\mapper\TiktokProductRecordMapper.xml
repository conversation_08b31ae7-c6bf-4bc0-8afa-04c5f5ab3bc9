<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.TiktokProductRecordMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.TiktokProductRecordDO">
        <id column="id" property="id" />
        <result column="product_id" property="productId" />
        <result column="keyword" property="keyword" />
        <result column="product_type" property="productType" />
        <result column="title" property="title" />
        <result column="cover" property="cover" />
        <result column="images" property="images" />
        <result column="market_price" property="marketPrice" />
        <result column="price" property="price" />
        <result column="currency" property="currency" />
        <result column="url" property="url" />
        <result column="product_status" property="productStatus" />
        <result column="in_shop" property="inShop" />
        <result column="source" property="source" />
        <result column="seller_ud" property="sellerUd" />
        <result column="adLabel_name" property="adlabelName" />
        <result column="platform" property="platform" />
        <result column="categories" property="categories" />
        <result column="biz_type" property="bizType" />
        <result column="skus" property="skus" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, product_id, keyword, product_type, title, cover, images, market_price, price, currency, url, product_status, in_shop, source, seller_ud, adLabel_name, platform, categories, biz_type, skus, creator, create_time, updater, update_time
    </sql>

  

  
</mapper>
