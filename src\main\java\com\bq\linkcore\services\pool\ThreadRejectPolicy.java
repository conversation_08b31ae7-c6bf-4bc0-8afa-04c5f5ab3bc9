package com.bq.linkcore.services.pool;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2022/5/21 20:42
 * @className ThreadRejectPolicy
 * @description
 */
@Slf4j
public class ThreadRejectPolicy implements RejectedExecutionHandler {
    /**
     * 线程池标签
     */
    private String threadPoolTag = "";

    public ThreadRejectPolicy(String tag) {
        this.threadPoolTag = tag;
    }

    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
        log.info("ThreadRejectPolicy task:{} executor:{} terminating:{} terminated:{}",
                r, executor.toString(), executor.isTerminating(), executor.isTerminated());
    }
}
