package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bq.linkcore.bean.entity.AtTiktokAuthorDailyRefreshFailureRecordDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorPoolDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorProfileHistoryDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorUpdateRecordDO;
import com.bq.linkcore.common.AuthorMonitorEnum;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorDailyRefreshFailureRecordMapper;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorPoolMapper;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorProfileHistoryMapper;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorUpdateRecordMapper;
import com.bq.linkcore.utils.DateTimeUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Component
public class AtTtAuthorPoolBiz {

    @Resource
    private AtTiktokAuthorPoolMapper tiktokAuthorPoolMapper;

    @Resource
    private AtTiktokAuthorProfileHistoryMapper atTiktokAuthorProfileHistoryMapper;

    @Resource
    private AtTiktokAuthorUpdateRecordMapper atTiktokAuthorUpdateRecordMapper;

    @Resource
    private AtTiktokAuthorDailyRefreshFailureRecordMapper refreshFailureRecordMapper;

    /**
     * 插入TikTok作者池记录
     *
     * @param authorPoolDO TikTok作者池实体对象
     * @return 插入影响的行数
     */
    public int insertAuthorPool(AtTiktokAuthorPoolDO authorPoolDO) {
        return tiktokAuthorPoolMapper.insert(authorPoolDO);
    }

    /**
     * 根据uniqueId查询TikTok作者池记录
     *
     * @param uniqueId 用户名
     * @return TikTok作者池实体对象，不存在则返回null
     */
    public AtTiktokAuthorPoolDO queryAuthorPoolByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorPoolDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorPoolDO>()
                .eq(AtTiktokAuthorPoolDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorPoolDO::getIsDel, 0);
        return tiktokAuthorPoolMapper.selectOne(queryWrapper);
    }

    /**
     * 根据uniqueId查询记录是否存在
     *
     * @param uniqueId 用户名
     * @return 记录数量，大于0表示存在
     */
    public int queryAuthorPoolExistsByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorPoolDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorPoolDO>()
                .eq(AtTiktokAuthorPoolDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorPoolDO::getIsDel, 0);
        return tiktokAuthorPoolMapper.selectCount(queryWrapper);
    }

    /**
     * 根据ID更新TikTok作者池记录
     *
     * @param authorPoolDO TikTok作者池实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateAuthorPool(AtTiktokAuthorPoolDO authorPoolDO) {
        return tiktokAuthorPoolMapper.updateById(authorPoolDO);
    }

    /**
     * 逻辑删除TikTok作者池记录
     *
     * @param uniqueId 用户名
     * @return 更新影响的行数
     */
    public int logicalDeleteAuthorPool(String uniqueId) {
        LambdaUpdateWrapper<AtTiktokAuthorPoolDO> queryWrapper = new LambdaUpdateWrapper<AtTiktokAuthorPoolDO>()
                .eq(AtTiktokAuthorPoolDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorPoolDO::getIsDel, 0)
                .set(AtTiktokAuthorPoolDO::getStatus, AuthorMonitorEnum.Unmonitored.getCode());

        return tiktokAuthorPoolMapper.update(null, queryWrapper);
    }

    public List<AtTiktokAuthorPoolDO> queryUpdateNext() {
        LocalDateTime localDateTime = DateTimeUtil.generateMinTime(LocalDateTime.now());

        LambdaQueryWrapper<AtTiktokAuthorPoolDO> wrapper = new LambdaQueryWrapper<AtTiktokAuthorPoolDO>()
                .eq(AtTiktokAuthorPoolDO::getIsDel, 0)
                .eq(AtTiktokAuthorPoolDO::getStatus, AuthorMonitorEnum.Monitoring.getCode())
                .le(AtTiktokAuthorPoolDO::getUpdateTime, localDateTime);
        return tiktokAuthorPoolMapper.selectList(wrapper);
    }

    // ==================== AtTiktokAuthorProfileRecord 相关方法 ====================

    /**
     * 插入TikTok作者主页记录
     *
     * @param authorProfileRecordDO TikTok作者主页记录实体对象
     * @return 插入影响的行数
     */
    public AtTiktokAuthorProfileHistoryDO insertAuthorProfileHis(AtTiktokAuthorProfileHistoryDO authorProfileRecordDO) {
        atTiktokAuthorProfileHistoryMapper.insert(authorProfileRecordDO);

        return authorProfileRecordDO;
    }

    /**
     * 根据uniqueId和recordDay查询TikTok作者主页记录
     *
     * @param uniqueId  用户名
     * @param recordDay 数据获取日期
     * @return TikTok作者主页记录实体对象，不存在则返回null
     */
    public AtTiktokAuthorProfileHistoryDO queryAuthorProfileHisByUniqueIdAndRecordDay(String uniqueId, String recordDay) {
        LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO>()
                .eq(AtTiktokAuthorProfileHistoryDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorProfileHistoryDO::getRecordDay, recordDay)
                .eq(AtTiktokAuthorProfileHistoryDO::getIsDel, 0);
        return atTiktokAuthorProfileHistoryMapper.selectOne(queryWrapper);
    }

    /**
     * 根据uniqueId查询TikTok作者主页记录列表
     *
     * @param uniqueId 用户名
     * @return TikTok作者主页记录列表
     */
    public List<AtTiktokAuthorProfileHistoryDO> queryAuthorProfileHisListByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO>()
                .eq(AtTiktokAuthorProfileHistoryDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorProfileHistoryDO::getIsDel, 0)
                .orderByDesc(AtTiktokAuthorProfileHistoryDO::getRecordDay);
        return atTiktokAuthorProfileHistoryMapper.selectList(queryWrapper);
    }

    /**
     * 根据uniqueId和recordDay查询记录是否存在
     *
     * @param uniqueId  用户名
     * @param recordDay 数据获取日期
     * @return 记录数量，大于0表示存在
     */
    public int queryAuthorProfileHisExistsByUniqueIdAndRecordDay(String uniqueId, String recordDay) {
        LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO>()
                .eq(AtTiktokAuthorProfileHistoryDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorProfileHistoryDO::getRecordDay, recordDay)
                .eq(AtTiktokAuthorProfileHistoryDO::getIsDel, 0);

        return atTiktokAuthorProfileHistoryMapper.selectCount(queryWrapper);
    }

    /**
     * 根据uniqueId和recordDay查询记录是否存在
     *
     * @param uniqueId  用户名
     * @param recordDay 数据获取日期
     * @return
     */
    public AtTiktokAuthorProfileHistoryDO queryAuthorByUniqueIdAndRecordDay(String uniqueId, String recordDay, Integer isDaily) {
        LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO>()
                .eq(AtTiktokAuthorProfileHistoryDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorProfileHistoryDO::getRecordDay, recordDay)
                .eq(AtTiktokAuthorProfileHistoryDO::getIsDaily, isDaily)
                .eq(AtTiktokAuthorProfileHistoryDO::getIsDel, 0)
                .orderByAsc(AtTiktokAuthorProfileHistoryDO::getCreateTime)
                .last("limit 1");

        return atTiktokAuthorProfileHistoryMapper.selectOne(queryWrapper);
    }

    /**
     * 根据ID更新TikTok作者主页记录
     *
     * @param authorProfileRecordDO TikTok作者主页记录实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateAuthorProfileHis(AtTiktokAuthorProfileHistoryDO authorProfileRecordDO) {
        return atTiktokAuthorProfileHistoryMapper.updateById(authorProfileRecordDO);
    }

    /************************* tiktok 达人数据更新记录表 *******************************/
    public AtTiktokAuthorUpdateRecordDO insertAuthorUpdateRecord(AtTiktokAuthorUpdateRecordDO authorUpdateRecordDO) {
        atTiktokAuthorUpdateRecordMapper.insert(authorUpdateRecordDO);

        return authorUpdateRecordDO;
    }

    public int updateAuthorUpdateRecord(AtTiktokAuthorUpdateRecordDO authorUpdateRecordDO) {
        return atTiktokAuthorUpdateRecordMapper.updateById(authorUpdateRecordDO);
    }

    public AtTiktokAuthorUpdateRecordDO queryAuthorUpdateRecord(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorUpdateRecordDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorUpdateRecordDO>()
                .eq(AtTiktokAuthorUpdateRecordDO::getUniqueId, uniqueId)
                .orderByDesc(AtTiktokAuthorUpdateRecordDO::getUpdateTime)
                .last("limit 1");

        return atTiktokAuthorUpdateRecordMapper.selectOne(queryWrapper);
    }

    /************************* tiktok 达人数据更新失败记录表 *******************************/
    public AtTiktokAuthorDailyRefreshFailureRecordDO insertAuthorRefreshFailureRecord(AtTiktokAuthorDailyRefreshFailureRecordDO dailyRefreshFailureRecordDO) {
        refreshFailureRecordMapper.insert(dailyRefreshFailureRecordDO);

        return dailyRefreshFailureRecordDO;
    }

    public int updateAuthorRefreshFailureRecord(AtTiktokAuthorDailyRefreshFailureRecordDO authorUpdateRecordDO) {
        return refreshFailureRecordMapper.updateById(authorUpdateRecordDO);
    }

    public AtTiktokAuthorDailyRefreshFailureRecordDO queryAuthorRefreshFailureRecord(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorDailyRefreshFailureRecordDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorDailyRefreshFailureRecordDO>()
                .eq(AtTiktokAuthorDailyRefreshFailureRecordDO::getUniqueId, uniqueId)
                .orderByDesc(AtTiktokAuthorDailyRefreshFailureRecordDO::getUpdateTime)
                .last("limit 1");

        return refreshFailureRecordMapper.selectOne(queryWrapper);
    }
}

