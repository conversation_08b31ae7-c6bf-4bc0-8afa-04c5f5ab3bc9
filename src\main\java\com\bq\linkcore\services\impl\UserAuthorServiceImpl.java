package com.bq.linkcore.services.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.dto.AtTiktokAuthorUpdateRecordDTO;
import com.bq.linkcore.bean.dto.AuthorFansRuleModel;
import com.bq.linkcore.bean.dto.AuthorTaggingModel;
import com.bq.linkcore.bean.dto.HotVideoRuleModel;
import com.bq.linkcore.bean.entity.*;
import com.bq.linkcore.bean.vo.*;
import com.bq.linkcore.biz.*;
import com.bq.linkcore.client.tikhub.models.AuthorInfo;
import com.bq.linkcore.client.tikhub.tiktok.TikHubTiktokAccountRequester;
import com.bq.linkcore.common.*;
import com.bq.linkcore.services.IUserAuthorService;
import com.bq.linkcore.services.impl.monitor.provider.tiktok.TiktokMonitoringProvider;
import com.bq.linkcore.utils.DateTimeUtil;
import com.bq.linkcore.utils.TikTokHelper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.bq.linkcore.common.ResponseMsg.ERROR_FREQUENTLY;
import static com.bq.linkcore.common.ResponseMsg.ERROR_TIKTOK_AUTHOR_URL;

@Slf4j
@Service
public class UserAuthorServiceImpl implements IUserAuthorService {

    @Resource
    private TikHubTiktokAccountRequester tikHubTiktokAccountRequester;

    @Resource
    private AtTtSearchBiz ttSearchBiz;

    @Resource
    private AtTtAuthorPoolBiz ttAuthorPoolBiz;

    @Autowired
    private TiktokMonitoringProvider tiktokMonitoringProvider;

    @Resource
    private AtTtStatBiz ttStatBiz;

    @Resource
    private AtUserAccountBiz atUserAccountBiz;

    @Resource
    private UserMonitorRuleBiz userMonitorRuleBiz;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public void initTxUserSetting(Long userId) {
        try {
            log.info("开始初始化用户爆款监控配置, userId: {}", userId);

            // 1. 参数校验
            if (userId == null) {
                log.warn("用户ID不能为空");
                return;
            }

            // 2. 检查用户是否已有监控规则配置
            if (userMonitorRuleBiz.existsUserMonitorRuleSetting(userId)) {
                log.info("用户已有监控规则配置，跳过初始化, userId: {}", userId);
                return;
            }

            // 3. 创建默认的热门视频规则配置
            HotVideoRuleModel hotVideoRule = HotVideoRuleModel.builder()
                    .playThreshold(10000)        // 播放量阈值：1万
                    .likeThreshold(500)          // 点赞数阈值：500
                    .commentThreshold(100)       // 评论数阈值：100
                    .shareThreshold(50)          // 分享数阈值：50
                    .collectThreshold(50)        // 收藏数阈值：50
                    .build();

            // 4. 创建默认的达人粉丝规则配置
            AuthorFansRuleModel authorFansRule = AuthorFansRuleModel.builder()
                    .fansThreshold(1000)         // 粉丝暴涨阈值：1000
                    .build();

            // 5. 将对象转换为JSON字符串
            String hotVideoRuleJson = JSON.toJSONString(hotVideoRule);
            String authorFansRuleJson = JSON.toJSONString(authorFansRule);

            // 6. 保存用户监控规则配置
            int result = userMonitorRuleBiz.saveOrUpdateUserMonitorRuleSetting(
                    userId,
                    hotVideoRuleJson,
                    authorFansRuleJson,
                    userId  // 操作用户ID设为当前用户
            );

            if (result > 0) {
                log.info("成功初始化用户爆款监控配置, userId: {}", userId);
            } else {
                log.error("初始化用户爆款监控配置失败, userId: {}", userId);
            }

        } catch (Exception e) {
            log.error("初始化用户爆款监控配置异常, userId: {}", userId, e);
        }
    }

    @Override
    public ResponseData updateTxUserMonitorSetting(Long userId, UserMonitorSettingVo vo) {

        String hotVideoRuleJson = JSON.toJSONString(vo.getVideoSetting());
        String authorFansRuleJson = JSON.toJSONString(vo.getVideoSetting());

        int result = userMonitorRuleBiz.saveOrUpdateUserMonitorRuleSetting(
                userId,
                hotVideoRuleJson,
                authorFansRuleJson,
                userId
        );

        if (result <= 0) {
            throw new ServiceException(ResponseMsg.FAIL.getCode(), "设置用户作品失败");
        }
        return null;
    }

    @Override
    public ResponseData<AuthorInfo> searchAuthor(Long userId, String homeUrl) {
        try {
            if (checkApiLimit(userId)) {
                return RD.fail(ERROR_FREQUENTLY);
            }

            if (StringUtils.isBlank(homeUrl)) {
                return RD.ok(ResponseMsg.ERROR_INVALID_PARAMS);
            }
            String uniqueId = TikTokHelper.regUniqueId(homeUrl);
            if (StringUtils.isBlank(uniqueId)) {
                return RD.fail(ERROR_TIKTOK_AUTHOR_URL);
            }
            AtTiktokAuthorSearchResultDO existingRecord = ttSearchBiz.queryAuthorSearchResultByUniqueId(uniqueId);
            if (existingRecord != null) {
                AuthorInfo build = AuthorInfo.builder().build();
                BeanUtils.copyProperties(existingRecord, build);
                return RD.ok(build);
            }

            AuthorInfo userProfileWebV1 = tikHubTiktokAccountRequester.getUserProfileWebV1(uniqueId, null);
            if (userProfileWebV1 == null) {
                log.error("获取 tikhub 用户信息失败 :{}", homeUrl);
                return RD.ok(ResponseMsg.FAIL.getCode());
            }
            AtTiktokAuthorSearchResultDO newRecord = convertToSearchResultDO(userProfileWebV1, uniqueId, userId);

            int insertResult = ttSearchBiz.insertAuthorSearchResult(newRecord);
            if (insertResult <= 0) {
                throw new ServiceException(ResponseMsg.FAIL.getCode(), "保存用户信息失败");
            }
            return RD.ok(userProfileWebV1);
        } catch (ServiceException e) {
            log.error("搜索作者信息失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("搜索作者信息发生未知错误: {}", e.getMessage(), e);
            throw new ServiceException(ResponseMsg.FAIL.getCode(), "搜索作者信息失败");
        }
    }

    private AtTiktokAuthorSearchResultDO convertToSearchResultDO(AuthorInfo authorInfo, String uniqueId, Long userId) {
        return AtTiktokAuthorSearchResultDO.builder()
                .authorId(authorInfo.getAuthorId())
                .uniqueId(uniqueId)
                .authorName(authorInfo.getAuthorName())
                .authorAvatar(authorInfo.getAuthorAvatar())
                .secUid(authorInfo.getSecUid())
                .authorUrl(authorInfo.getAuthorUrl())
                .desc(authorInfo.getDesc())
                .commerceUser(authorInfo.getCommerceUser())
                .commerceCategory(authorInfo.getCategory())
                .friendCount(authorInfo.getFriendCount())
                .followerCount(authorInfo.getFollowerCount())
                .followingCount(authorInfo.getFollowingCount())
                .language(authorInfo.getLanguage())
                .creator(userId)
                .createTime(LocalDateTime.now())
                .updater(userId)
                .updateTime(LocalDateTime.now())
                .isDel(0)
                .build();
    }

    @Override
    public ResponseData addTxAuthorMonitor(Long userId, String atUniqueId) {
        try {
            log.info("开始添加账户监控, userId: {}, atUniqueId: {}", userId, atUniqueId);

            if (userId == null || StringUtils.isBlank(atUniqueId)) {
                return RD.fail(ResponseMsg.FAIL.getCode(), "参数不能为空");
            }

            // 检查用户是否已经在监控该账户
            AtAccountUserSettingDO existingUserAccount = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
            if (existingUserAccount != null) {
                log.warn("用户已经在监控该账户, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "已经存在账户监控");
            }

            // 从搜索结果表获取账户信息
            AtTiktokAuthorSearchResultDO searchResultDO = ttSearchBiz.queryAuthorSearchResultByUniqueId(atUniqueId);
            if (searchResultDO == null) {
                return RD.fail(ERROR_TIKTOK_AUTHOR_URL);
            }

            // 插入用户账户设置
            AtAccountUserSettingDO userAccountSetting = new AtAccountUserSettingDO();
            userAccountSetting.setUserId(userId);
            userAccountSetting.setAtUniqueId(atUniqueId);
            userAccountSetting.setHomeUrl(searchResultDO.getAuthorUrl());
            userAccountSetting.setPlatform("tiktok");
            userAccountSetting.setCreator(userId);
            userAccountSetting.setCreateTime(LocalDateTime.now());
            userAccountSetting.setUpdater(userId);
            userAccountSetting.setUpdateTime(LocalDateTime.now());
            userAccountSetting.setIsDel(0);

            int insertResult = atUserAccountBiz.insertUserAccountSetting(userAccountSetting);
            if (insertResult <= 0) {
                log.error("插入用户账户设置失败, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "添加监控失败");
            }

            log.info("成功插入用户账户设置, userId: {}, atUniqueId: {}", userId, atUniqueId);

            // 5. 处理作者池数据
            AtTiktokAuthorPoolDO tiktokAuthorPoolDO = ttAuthorPoolBiz.queryAuthorPoolByUniqueId(atUniqueId);

            if (tiktokAuthorPoolDO == null) {
                // 如果作者池中没有数据，则插入
                tiktokAuthorPoolDO = new AtTiktokAuthorPoolDO();
                BeanUtils.copyProperties(searchResultDO, tiktokAuthorPoolDO);
                tiktokAuthorPoolDO.setCreator(userId);
                tiktokAuthorPoolDO.setCreateTime(LocalDateTime.now());
                tiktokAuthorPoolDO.setUpdater(userId);
                tiktokAuthorPoolDO.setUpdateTime(LocalDateTime.now());
                tiktokAuthorPoolDO.setStatus(AuthorMonitorEnum.Monitoring.getCode());
                tiktokAuthorPoolDO.setIsDel(0);

                ttAuthorPoolBiz.insertAuthorPool(tiktokAuthorPoolDO);
                log.info("成功插入作者池数据, atUniqueId: {}", atUniqueId);
            } else {
                // 如果作者池中已有数据，则更新（可能之前被逻辑删除了，需要恢复）
                BeanUtils.copyProperties(searchResultDO, tiktokAuthorPoolDO);
                tiktokAuthorPoolDO.setUpdater(userId);
                tiktokAuthorPoolDO.setUpdateTime(LocalDateTime.now());
                tiktokAuthorPoolDO.setStatus(AuthorMonitorEnum.Monitoring.getCode());
                tiktokAuthorPoolDO.setIsDel(0); // 确保不是删除状态
                ttAuthorPoolBiz.updateAuthorPool(tiktokAuthorPoolDO);
                log.info("成功更新作者池数据, atUniqueId: {}", atUniqueId);
            }

            // 6. 处理作者作品数据
            tiktokMonitoringProvider.processAuthorWorksAsync(atUniqueId, searchResultDO.getSecUid());

            return RD.ok(searchResultDO);
        } catch (Exception e) {
            log.error("添加账户监控异常, userId: {}, atUniqueId: {}", userId, atUniqueId, e);
            return RD.fail(ResponseMsg.FAIL.getCode(), "添加监控失败");
        }
    }

    @Override
    public ResponseData<PageResultVO<AtTtIncrStatVo>> queryAccountMonitorInCrList(Long userId, UserPageRequestVo vo) {
        try {
            // 1. 先获取与用户关联的账户列表
            List<AtAccountUserSettingDO> userAccountList = atUserAccountBiz.queryUserTiktokAccountsByUserId(userId);

            if (CollectionUtils.isEmpty(userAccountList)) {
                PageResultVO<AtTtIncrStatVo> emptyResult = new PageResultVO<>();
                emptyResult.initialize(vo);
                return RD.ok(emptyResult);
            }

            // 3. 获取今天和昨天的日期字符串
            String today = DateTimeUtil.formatDateString(LocalDateTime.now());
            String yesterday = DateTimeUtil.formatDateString(LocalDateTime.now().minusDays(1));

            // 4. 构建返回结果
            List<AtTtIncrStatVo> resultList = new ArrayList<>();

            for (AtAccountUserSettingDO userAccount : userAccountList) {
                // 4.1 根据uniqueId获取作者池信息
                AtTiktokAuthorPoolDO author = ttAuthorPoolBiz.queryAuthorPoolByUniqueId(userAccount.getAtUniqueId());

                if (author == null) {
                    // 如果作者池中没有数据，跳过该账户
                    log.warn("作者池中未找到账户信息: {}", userAccount.getAtUniqueId());
                    continue;
                }

                AtTtIncrStatVo incrStatVo = new AtTtIncrStatVo();

                // 4.2 设置账号基本信息（结合用户设置和作者池信息）
                AtAccountSimpleInfoVo accountInfo = buildAccountSimpleInfo(author, userAccount);
                incrStatVo.setAccountInfo(accountInfo);

                // 4.3 获取今天的增量统计
                TtIncrStatItemVo todayStats = buildIncrStatItem(author.getUniqueId(), today);
                incrStatVo.setToday(todayStats);

                // 4.4 获取昨天的增量统计
                TtIncrStatItemVo yesterdayStats = buildIncrStatItem(author.getUniqueId(), yesterday);
                incrStatVo.setYesterday(yesterdayStats);

                resultList.add(incrStatVo);
            }

            // 5. 构建分页结果
            PageResultVO<AtTtIncrStatVo> pageResult = new PageResultVO<>();
            pageResult.setPageNo(vo.getPageNo());
            pageResult.setPageSize(vo.getPageSize());
            pageResult.setData(resultList);
            pageResult.setTotalCount(resultList.size());

            return RD.ok(pageResult);

        } catch (Exception e) {
            log.error("查询账号监控增量统计列表异常", e);
            return RD.fail("查询失败");
        }
    }

    @Override
    public ResponseData removeTxMonitor(Long userId, String atUniqueId) {
        try {
            log.info("开始移除账户监控, userId: {}, atUniqueId: {}", userId, atUniqueId);

            if (StringUtils.isBlank(atUniqueId)) {
                return RD.fail(ResponseMsg.FAIL.getCode(), "参数不能为空");
            }

            // 2. 检查用户是否确实在监控该账户
            AtAccountUserSettingDO userAccount = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
            if (userAccount == null) {
                return RD.fail(ResponseMsg.FAIL.getCode(), "未找到该监控账户");
            }

            // 3. 逻辑删除用户账户设置
            int deleteResult = atUserAccountBiz.logicalDeleteUserAccount(userId, atUniqueId);
            if (deleteResult <= 0) {
                log.error("逻辑删除用户账户设置失败, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return RD.fail(ResponseMsg.FAIL.getCode(), "移除监控失败");
            }

            log.info("成功移除用户账户监控设置, userId: {}, atUniqueId: {}", userId, atUniqueId);

            // 4. 检查是否还有其他用户在监控该账户
            boolean hasOtherUsers = atUserAccountBiz.hasAnyUserMonitoring(atUniqueId);

            if (!hasOtherUsers) {
                // 5. 如果没有其他用户监控该账户，则从作者池中逻辑删除
                int authorPoolDeleteResult = ttAuthorPoolBiz.logicalDeleteAuthorPool(atUniqueId);
                if (authorPoolDeleteResult > 0) {
                    log.info("成功从作者池中逻辑删除账户, atUniqueId: {}", atUniqueId);
                } else {
                    log.warn("从作者池中逻辑删除账户失败或账户不存在, atUniqueId: {}", atUniqueId);
                }
            } else {
                log.info("还有其他用户在监控该账户，保留作者池数据, atUniqueId: {}", atUniqueId);
            }

            return RD.ok();

        } catch (Exception e) {
            log.error("移除账户监控异常, userId: {}, atUniqueId: {}", userId, atUniqueId, e);
            return RD.fail();
        }
    }

    @Override
    public ResponseData updateTxAuthor(Long userId, String atUniqueId) {
        try {
            // 检查用户是否确实在监控该账户
            AtAccountUserSettingDO userAccount = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
            if (userAccount == null) {
                return RD.fail(ResponseMsg.FAIL.getCode(), "未找到该监控账户");
            }

            AtTiktokAuthorPoolDO atTiktokAuthorPoolDO = ttAuthorPoolBiz.queryAuthorPoolByUniqueId(atUniqueId);
            if (atTiktokAuthorPoolDO == null) {
                return RD.fail(ResponseMsg.FAIL.getCode(), "达人账号未收录");
            }

            AuthorTaggingModel authorTaggingModel = new AuthorTaggingModel();
            BeanUtils.copyProperties(atTiktokAuthorPoolDO, authorTaggingModel);

            // 处理作者作品数据
            tiktokMonitoringProvider.processAuthorAndWorksBatchAsync(Collections.singletonList(authorTaggingModel), 0);

            return RD.ok();

        } catch (Exception e) {
            log.error("移除账户监控异常, userId: {}, atUniqueId: {}", userId, atUniqueId, e);
            return RD.fail();
        }
    }

    @Override
    public ResponseData queryUpdateInfo(Long userId, String atUniqueId) {
        try {
            AtAccountUserSettingDO userAccount = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
            if (userAccount == null) {
                return RD.fail(ResponseMsg.FAIL.getCode(), "未找到该监控账户");
            }

            AtTiktokAuthorUpdateRecordDO authorUpdateRecordDO = ttAuthorPoolBiz.queryAuthorUpdateRecord(atUniqueId);
            if (authorUpdateRecordDO != null) {
                AtTiktokAuthorUpdateRecordDTO authorUpdateRecordDTO = new AtTiktokAuthorUpdateRecordDTO();
                BeanUtils.copyProperties(authorUpdateRecordDO, authorUpdateRecordDTO);

                return RD.ok(authorUpdateRecordDTO);
            }

            return RD.ok();

        } catch (Exception e) {
            log.error("移除账户监控异常, userId: {}, atUniqueId: {}", userId, atUniqueId, e);
            return RD.fail();
        }
    }

    /**
     * 构建账号简单信息
     *
     * @param author      作者池信息
     * @param userAccount 用户账户设置信息
     * @return 账号简单信息VO
     */
    private AtAccountSimpleInfoVo buildAccountSimpleInfo(AtTiktokAuthorPoolDO author, AtAccountUserSettingDO userAccount) {
        AtAccountSimpleInfoVo accountInfo = new AtAccountSimpleInfoVo();
        accountInfo.setAtUniqueId(author.getUniqueId());

        // 优先使用用户设置的备注名称，如果没有则使用作者名称
        String displayName = userAccount.getNickname() != null && !userAccount.getNickname().trim().isEmpty()
                ? userAccount.getNickname() : author.getAuthorName();
        accountInfo.setName(displayName);

        accountInfo.setAvatar(author.getAuthorAvatar());
        accountInfo.setDesc(author.getDesc());
        accountInfo.setFansCount(author.getFollowerCount());
        accountInfo.setLikeCount(author.getHeartCount());
        accountInfo.setResourceCount(author.getVideoCount());
        accountInfo.setPlatform("tiktok");

        // 从作者池获取基础数据，播放量、评论数、分享数、收藏数需要从统计表获取
        accountInfo.setPlayCount(0L);
        accountInfo.setCommentCount(0);
        accountInfo.setShareCount(0);
        accountInfo.setCollectCount(0);

        return accountInfo;
    }

    /**
     * 构建增量统计项
     *
     * @param uniqueId 用户唯一ID
     * @param statDay  统计日期
     * @return 增量统计项VO
     */
    private TtIncrStatItemVo buildIncrStatItem(String uniqueId, String statDay) {
        TtIncrStatItemVo statItem = new TtIncrStatItemVo();

        try {
            // 获取作者主页统计数据（at_tiktok_author_stat_daily表包含了所有需要的统计数据）
            AtTiktokAuthorStatDailyDO authorStat = ttStatBiz.queryAuthorProfileStatDailyByUniqueIdAndStatDay(uniqueId, statDay);

            if (authorStat != null) {
                // 设置粉丝、关注、点赞、视频数等基础数据
                statItem.setAddFansCount(authorStat.getFollowerCount() != null ? authorStat.getFollowerCount() : 0);
                statItem.setAddLikeCount(authorStat.getHeartCount() != null ? authorStat.getHeartCount() : 0);
                statItem.setAddResourceCount(authorStat.getVideoCount() != null ? authorStat.getVideoCount() : 0);
                statItem.setFollowingCount(authorStat.getFollowingCount() != null ? authorStat.getFollowingCount() : 0);
                statItem.setAddFollowingCount(0); // 需要计算增量，这里暂时设为0

                // 播放量（从Integer转换为Long）
                Long playCount = 0L;
                if (authorStat.getPlayCount() != null) {
                    playCount = Long.valueOf(authorStat.getPlayCount());
                }
                statItem.setAddPlayCount(playCount);

                // 评论数
                statItem.setAddCommentCount(authorStat.getCommentCount() != null ? authorStat.getCommentCount() : 0);

                statItem.setAddShareCount(authorStat.getShareCount() != null ? authorStat.getShareCount() : 0);
                statItem.setAddCollectCount(authorStat.getCollectCount() != null ? authorStat.getCollectCount() : 0);

                // 使用资源数设置为视频数（表示有数据的作品数量）
                statItem.setAddUsingResourceCount(authorStat.getVideoCount() != null ? authorStat.getVideoCount() : 0);

            } else {
                // 如果没有统计数据，设置默认值
                setDefaultStatValues(statItem);
            }

        } catch (Exception e) {
            log.error("构建增量统计项异常, uniqueId: {}, statDay: {}", uniqueId, statDay, e);
            // 异常时返回默认值
            setDefaultStatValues(statItem);
        }

        return statItem;
    }

    /**
     * 设置默认统计值
     *
     * @param statItem 统计项
     */
    private void setDefaultStatValues(TtIncrStatItemVo statItem) {
        statItem.setAddFansCount(0);
        statItem.setAddLikeCount(0);
        statItem.setAddResourceCount(0);
        statItem.setAddUsingResourceCount(0);
        statItem.setAddPlayCount(0L);
        statItem.setAddCommentCount(0);
        statItem.setAddShareCount(0);
        statItem.setAddCollectCount(0);
        statItem.setFollowingCount(0);
        statItem.setAddFollowingCount(0);
    }

    /**
     * 构建AccountInfoVo对象
     *
     * @param authorPoolDO 作者池数据对象
     * @return AccountInfoVo对象
     */
    private AccountInfoVo buildAccountInfoVo(AtTiktokAuthorPoolDO authorPoolDO) {
        AccountInfoVo accountInfoVo = new AccountInfoVo();

        // 基本信息
        accountInfoVo.setUniqueId(authorPoolDO.getUniqueId());
        accountInfoVo.setPlatform("tiktok");
        accountInfoVo.setAuthorName(authorPoolDO.getAuthorName());
        accountInfoVo.setAuthorAvatar(authorPoolDO.getAuthorAvatar());
        accountInfoVo.setDescription(authorPoolDO.getDesc());
        accountInfoVo.setSecUid(authorPoolDO.getSecUid());
        accountInfoVo.setHomeUrl(authorPoolDO.getAuthorUrl());

        // 统计信息
        accountInfoVo.setFollowerCount(authorPoolDO.getFollowerCount());
        accountInfoVo.setLikeCount(authorPoolDO.getHeartCount());
        accountInfoVo.setFollowingCount(authorPoolDO.getFollowingCount());
        accountInfoVo.setRegistryLocation(authorPoolDO.getRegion());

        if (authorPoolDO.getRegisterTime() != null) {
            accountInfoVo.setRegisterTime(authorPoolDO.getRegisterTime());
        }
        if (authorPoolDO.getUpdateTime() != null) {
            accountInfoVo.setLastSyncTime(DateTimeUtil.convertUnixTs(authorPoolDO.getUpdateTime()));
        }

        return accountInfoVo;
    }

    private boolean checkApiLimit(Long userId) {
        String key = "API_LIMIT_KEY_SEARCH_AUTHOR_" + userId;
        RBucket<String> bucket = redissonClient.getBucket(key);
        if (bucket.isExists()) {
            return true;
        }

        bucket.set(key, 3, TimeUnit.SECONDS);
        return false;
    }

    public AtTiktokAuthorUpdateRecordDO buildAuthorUpdateRecordDO(Long userId, AtTiktokAuthorPoolDO atTiktokAuthorPoolDO) {
        return AtTiktokAuthorUpdateRecordDO.builder()
                .authorId(atTiktokAuthorPoolDO.getAuthorId())
                .uniqueId(atTiktokAuthorPoolDO.getUniqueId())
                .secUid(atTiktokAuthorPoolDO.getSecUid())
                .status(AuthorUpdateStatusEnum.Waiting.getCode())
                .creator(userId)
                .updater(userId)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
    }
}
