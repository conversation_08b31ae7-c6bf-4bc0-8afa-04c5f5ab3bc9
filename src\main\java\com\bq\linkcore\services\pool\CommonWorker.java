package com.bq.linkcore.services.pool;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/9/8 22:03
 * @className CommonWorker
 * @description
 */
@Slf4j
@Component
public class CommonWorker {
    private final static String TAG = "common-worker-zone";
    /**
     * HANDLER_TAG
     */
    private final static String HANDLER_TAG = "common-worker-handler";


    /**
     * Zone线程池，用于接受
     */
    private final WorkThreadPool threadPool = new WorkThreadPool(
            5,
            50,
            1L,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(50000),
            new WorkThreadFactory(HANDLER_TAG),
            new ThreadRejectPolicy(HANDLER_TAG));

    /**
     * 添加任务
     *
     * @param workTask
     */
    public void addTask(Runnable workTask) {
        threadPool.addTask(workTask);
    }

}
