
package com.bq.linkcore.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;


/**
 * @ClassName: MailSendingConfig
 * @Description:
 * @author: rendong.ck@naturobot
 * @date: 2021年08月06日 11:21 上午
 */
@Configuration
@Slf4j
@Component
@Data
public class MailSendingConfig {

    @Value("${myMail.host}")
    private String host;
    @Value("${myMail.account}")
    private String account;
    @Value("${myMail.password}")
    private String password;
    @Value("${myMail.port}")
    private String port;
    @Value("${myMail.protocol}")
    private String protocol;
}