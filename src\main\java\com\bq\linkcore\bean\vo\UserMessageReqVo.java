package com.bq.linkcore.bean.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: lmy
 * @date: 2021年08月09日 11:14:32
 * @description:
 */
@ApiModel(description = "获取短信验证码请求参数对象")
@Data
@Slf4j
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class UserMessageReqVo implements Serializable {

    @ApiModelProperty(value = "邮箱")
    @NotBlank(message = "邮箱")
    private String email;

    private String phone;

    @ApiModelProperty(value = "获取验证码类型，1=登录，2=注册，3=修改密码")
    @NotNull(message = "获取验证码类型，1=登录，2=注册，3=修改密码")
    @Min(1)
    @Max(3)
    private Integer type;

    @ApiModelProperty(value = "时间戳：毫秒")
    private Long timestamp;

    @ApiModelProperty(value = "签名md5(phone+type+client+timestamp+salt)，ts是毫秒")
    private String sign;
}
