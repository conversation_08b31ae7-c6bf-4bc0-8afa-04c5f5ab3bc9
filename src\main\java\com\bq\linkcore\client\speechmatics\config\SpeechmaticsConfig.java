package com.bq.linkcore.client.speechmatics.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;

/**
 * Speechmatics API 配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "speechmatics.api")
public class SpeechmaticsConfig {
    
    /**
     * API 基础URL
     */
    private String baseUrl = "https://asr.api.speechmatics.com/v2";
    
    /**
     * API Token
     */
    private String token = "HzKT1DaWZqf9JEs4vTVsJWZAyPE0E0hk";
    
    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;
    
    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 60000;
    
    /**
     * 默认语言
     */
    private String defaultLanguage = "en";
    
    /**
     * 是否启用说话人分离
     */
    private boolean enableDiarization = true;
    
    /**
     * 说话人分离类型
     */
    private String diarizationType = "speaker";
}
