package com.bq.linkcore.services.impl;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.entity.AtAccountUserSettingDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorPoolDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorWorkRecordDO;
import com.bq.linkcore.bean.entity.TiktokArticleVideosDO;
import com.bq.linkcore.bean.vo.AccountInfoVo;
import com.bq.linkcore.bean.vo.WorkDashboardRespVo;
import com.bq.linkcore.bean.vo.WorkInfoVo;
import com.bq.linkcore.biz.AtTtAuthorPoolBiz;
import com.bq.linkcore.biz.AtTtAuthorWorkBiz;
import com.bq.linkcore.biz.AtUserAccountBiz;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ResponseMsg;
import com.bq.linkcore.services.IArticleService;
import com.bq.linkcore.services.impl.monitor.provider.tiktok.TiktokMonitoringProvider;
import com.bq.linkcore.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class ArticleServiceImpl implements IArticleService {

    @Resource
    private AtTtAuthorWorkBiz atTtAuthorWorkBiz;

    @Resource
    private AtUserAccountBiz atUserAccountBiz;
    @Resource
    private AtTtAuthorPoolBiz atTtAuthorPoolBiz;

    @Resource
    private TiktokMonitoringProvider tiktokMonitoringProvider;

    @Override
    public WorkDashboardRespVo queryTxArticleInfo(Long userId, String workId, String atUniqueId) {
        try {
            log.info("开始查询作品详情, userId: {}, workId: {}, atUniqueId: {}", userId, workId, atUniqueId);

            if (StringUtils.isBlank(workId) || StringUtils.isBlank(atUniqueId)) {
                log.warn("参数不能为空, userId: {}, workId: {}, atUniqueId: {}", userId, workId, atUniqueId);
                return null;
            }

            AtAccountUserSettingDO userAccount = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
            if (userAccount == null) {
                log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, atUniqueId);
                return null;
            }
            WorkDashboardRespVo respVo = new WorkDashboardRespVo();

            AtTiktokAuthorPoolDO atTiktokAuthorPoolDO = atTtAuthorPoolBiz.queryAuthorPoolByUniqueId(atUniqueId);
            AccountInfoVo accountInfoVo = covertToAccountInfo(atTiktokAuthorPoolDO);
            respVo.setAuthorInfo(accountInfoVo);
            AtTiktokAuthorWorkRecordDO workRecord = atTtAuthorWorkBiz.queryAuthorWorkRecordByWorkIdAndUniqueId(atUniqueId,workId);
            if (workRecord == null) {
                log.warn("未找到对应的作品记录, workId: {}", workId);
                return null;
            }

            WorkInfoVo workInfoVo = convertToWorkInfoVo(workRecord);
            respVo.setWorkInfo(workInfoVo);

            log.info("成功查询作品详情, userId: {}, workId: {}, atUniqueId: {}", userId, workId, atUniqueId);
            return respVo;

        } catch (Exception e) {
            log.error("查询作品详情异常, userId: {}, workId: {}, atUniqueId: {}", userId, workId, atUniqueId, e);
            return null;
        }
    }

    @Override
    public ResponseData syncArticleInfo(Long userId, String workId, String atUniqueId, Integer isDaily) {

        AtAccountUserSettingDO userAccount = atUserAccountBiz.queryUserAccountByUserIdAndUniqueId(userId, atUniqueId);
        if (userAccount == null) {
            log.warn("用户未监控该账户, userId: {}, atUniqueId: {}", userId, atUniqueId);
            return RD.ok(ResponseMsg.ERROR_USER_ACCOUNT_EXIST);
        }

        AtTiktokAuthorWorkRecordDO workRecord
                = atTtAuthorWorkBiz.queryAuthorWorkRecordByWorkIdAndUniqueId(atUniqueId,workId);
        if (workRecord == null) {
            log.warn("未找到对应的作品记录, workId: {}", workId);
            return RD.ok(ResponseMsg.ERROR_OPEN_API_NO_ARTICLE_DETAIL);
        }

        tiktokMonitoringProvider.processRefreshWorkSync(workId, isDaily);

        return ResponseData.ok();
    }


    private WorkInfoVo convertToWorkInfoVo(AtTiktokAuthorWorkRecordDO workRecord) {
        if (workRecord == null) {
            return null;
        }

        WorkInfoVo workInfoVo = new WorkInfoVo();
        BeanUtils.copyProperties(workRecord, workInfoVo);
        workInfoVo.setUpdateTime(DateTimeUtil.convertUnixTs(workRecord.getUpdateTime()));
        workInfoVo.setCreateTime(DateTimeUtil.convertUnixTs(workRecord.getCreateTime()));

        TiktokArticleVideosDO tiktokArticleVideosDO = atTtAuthorWorkBiz.queryArticleVideo(workRecord.getVideoId());
        if (tiktokArticleVideosDO == null) {
            log.error("获取视频信息失败: workRecord {}", workRecord);
            return workInfoVo;
        }
        workInfoVo.setVideoUrl(tiktokArticleVideosDO.getUrl());
        return workInfoVo;
    }

    private AccountInfoVo covertToAccountInfo(AtTiktokAuthorPoolDO authorPoolDO){
        AccountInfoVo accountInfoVo = new AccountInfoVo();
        BeanUtils.copyProperties(authorPoolDO,accountInfoVo);
        accountInfoVo.setCreateTime(DateTimeUtil.convertUnixTs(authorPoolDO.getCreateTime()));
        accountInfoVo.setLastSyncTime(DateTimeUtil.convertUnixTs(authorPoolDO.getUpdateTime()));
        return accountInfoVo;
    }
}
