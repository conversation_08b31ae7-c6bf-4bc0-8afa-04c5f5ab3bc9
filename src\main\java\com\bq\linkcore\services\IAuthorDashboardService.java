package com.bq.linkcore.services;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.vo.*;
import com.bq.linkcore.common.PageResultVO;

import java.util.List;

public interface IAuthorDashboardService {

    /**
     * 获取用户数据详情
     * @param userId
     * @param atUniqueId
     * @return
     */
    ResponseData<AtTtIncrStatVo> queryTxAuthorInfo(Long userId, String atUniqueId);

    /**
     * 互动数据趋势图， 可选 7 15 30
     * @param userId
     * @param atUniqueId
     * @param datType
     * @return
     */
    ResponseData<AuthorInteractiveTingVo> queryTxAccountInteractiveChart(Long userId, String atUniqueId, Integer datType);

    /**
     * 发布作品频率的柱状图
     * @param userId
     * @param vo
     * @return
     */
    ResponseData<AuthorWorkReleaseVo> queryTxWorkReleaseFrequency(Long userId, AtWorkFrequencyRuleVo vo);


    /**
     * 作品标签排行，可按照 1:点赞数, 2:评论数, 3:转发数, 4:收藏数
     * @param userId
     * @param sortType
     * @return
     */
    ResponseData<PageResultVO<AtTagRankingVo>> queryTxWorkTagRanking(Long userId, String atUniqueId, String sortType);


    /**
     * 获取监控达人作品列表
     * @param userId
     * @param vo
     * @return
     */
    ResponseData<PageResultVO<AtAuthorWorkInfoVo>> queryTxAuthorWorkList(Long userId, AuthorWorkFilterReqVo vo);

    /**
     * 查询标签 菜单
     * @param userId
     * @param atUniqueId
     * @return
     */
    ResponseData<List<TagFilterVo>> queryTxTopicTagList(Long userId, String atUniqueId);

    /**
     * 查询音乐列表
     * @param userId
     * @param atUniqueId
     * @return
     */
    ResponseData<List<MusicFilterVo>> queryTxMusicList(Long userId, String atUniqueId);
}
