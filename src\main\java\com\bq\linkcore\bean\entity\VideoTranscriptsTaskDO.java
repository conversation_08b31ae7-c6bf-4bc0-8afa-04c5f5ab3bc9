package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-19 17:17:31
 * @ClassName: VideoTranscriptsTaskDO
 * @Description: 视频转写任务
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("video_transcripts_task")
public class VideoTranscriptsTaskDO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 作品唯一标识
     */
    private String workId;

    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 视频时长 (毫秒)
     */
    private Long videoDuration;

    /**
     * 任务 id
     */
    private String taskId;

    /**
     * dy、xhs
     */
    private String platform;

    /**
     * 分析结果
     */
    private String result;

    /**
     * 分析结果json
     */
    private String resultJson;

    /**
     * 任务状态 0 分析中 1 分析完成 2 分析失败
     */
    private Integer status;

    /**
     * 错误信息
     */
    private String errMsg;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 数据请求时间
     */
    private LocalDateTime createTime;

    /**
     * 是否被删除
     */
    private Integer isDel;




}
