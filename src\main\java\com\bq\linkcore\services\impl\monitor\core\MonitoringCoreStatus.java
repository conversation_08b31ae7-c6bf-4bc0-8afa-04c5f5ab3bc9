package com.bq.linkcore.services.impl.monitor.core;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 监控核心服务状态
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonitoringCoreStatus {

    /**
     * 核心线程池任务数
     */
    private Long threadPoolTaskCount;

    /**
     * 核心线程池线程数
     */
    private Long threadPoolThreadCount;

    /**
     * 抖音提供者状态
     */
    private String douyinProviderStatus;

    /**
     * TikTok提供者状态
     */
    private String tiktokProviderStatus;

    /**
     * 服务运行状态
     */
    private String serviceStatus = "RUNNING";

    /**
     * 最后更新时间
     */
    private String lastUpdateTime;

    @Override
    public String toString() {
        return String.format(
                "MonitoringCore[核心线程池: %d任务/%d线程, 抖音: %s, TikTok: %s, 状态: %s]",
                threadPoolTaskCount, threadPoolThreadCount,
                douyinProviderStatus, tiktokProviderStatus, serviceStatus
        );
    }
}
