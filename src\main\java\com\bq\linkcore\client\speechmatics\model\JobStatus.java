package com.bq.linkcore.client.speechmatics.model;

/**
 * Speechmatics 任务状态枚举
 */
public enum JobStatus {
    
    /**
     * 任务已创建，等待处理
     */
    RUNNING("running", "任务运行中"),
    
    /**
     * 任务已完成
     */
    DONE("done", "任务已完成"),
    
    /**
     * 任务失败
     */
    REJECTED("rejected", "任务被拒绝"),
    
    /**
     * 任务已取消
     */
    CANCELLED("cancelled", "任务已取消"),
    
    /**
     * 任务已删除
     */
    DELETED("deleted", "任务已删除"),
    
    /**
     * 未知状态
     */
    EXPIRED("expired", "系统任务删除");
    
    private final String code;
    private final String description;
    
    JobStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据状态码获取枚举
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static JobStatus fromCode(String code) {
        if (code == null) {
            return EXPIRED;
        }
        
        for (JobStatus status : JobStatus.values()) {
            if (status.code.equalsIgnoreCase(code)) {
                return status;
            }
        }
        return EXPIRED;
    }
    
    /**
     * 判断任务是否已完成
     * @return true-已完成，false-未完成
     */
    public boolean isCompleted() {
        return this == DONE;
    }
    
    /**
     * 判断任务是否失败
     * @return true-失败，false-未失败
     */
    public boolean isFailed() {
        return this == REJECTED || this == CANCELLED || this == DELETED;
    }
    
    /**
     * 判断任务是否正在运行
     * @return true-运行中，false-未运行
     */
    public boolean isRunning() {
        return this == RUNNING;
    }
}
