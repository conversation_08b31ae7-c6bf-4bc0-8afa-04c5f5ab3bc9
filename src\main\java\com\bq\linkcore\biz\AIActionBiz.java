package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bq.linkcore.bean.entity.AuthorDisassemblyTaskRecordDO;
import com.bq.linkcore.bean.entity.VideoTranscriptsTaskDO;
import com.bq.linkcore.dao.mapper.AuthorDisassemblyTaskRecordMapper;
import com.bq.linkcore.dao.mapper.VideoTranscriptsTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * AI 操作业务逻辑类
 */
@Slf4j
@Component
public class AIActionBiz {

    @Resource
    private VideoTranscriptsTaskMapper videoTranscriptsTaskMapper;


    @Resource
    private AuthorDisassemblyTaskRecordMapper authorDisassemblyTaskRecordMapper;


    // ==================== 插入操作 ====================

    /**
     * 插入视频转写任务记录
     *
     * @param taskDO 视频转写任务实体对象
     * @return 插入影响的行数
     */
    public int insertVideoTranscriptsTask(VideoTranscriptsTaskDO taskDO) {
        if (taskDO == null) {
            log.warn("插入视频转写任务失败：任务对象为空");
            return 0;
        }

        // 设置创建时间
        if (taskDO.getCreateTime() == null) {
            taskDO.setCreateTime(LocalDateTime.now());
        }

        // 设置更新时间
        if (taskDO.getUpdateTime() == null) {
            taskDO.setUpdateTime(LocalDateTime.now());
        }

        // 设置默认删除标记
        if (taskDO.getIsDel() == null) {
            taskDO.setIsDel(0);
        }

        return videoTranscriptsTaskMapper.insert(taskDO);
    }

    /**
     * 创建新的视频转写任务
     *
     * @param workId 作品唯一标识
     * @param videoUrl 视频链接
     * @param videoDuration 视频时长（毫秒）
     * @param taskId 任务ID
     * @param platform 平台（dy、xhs等）
     * @return 插入影响的行数
     */
    public int createVideoTranscriptsTask(String workId, String videoUrl, Long videoDuration,
                                         String taskId, String platform) {
        VideoTranscriptsTaskDO taskDO = VideoTranscriptsTaskDO.builder()
                .workId(workId)
                .videoUrl(videoUrl)
                .videoDuration(videoDuration)
                .taskId(taskId)
                .platform(platform)
                .status(0) // 0-分析中
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .isDel(0)
                .build();

        return insertVideoTranscriptsTask(taskDO);
    }

    /**
     * 批量插入视频转写任务记录
     *
     * @param taskList 视频转写任务实体对象列表
     * @return 插入成功的记录数
     */
    public int batchInsertVideoTranscriptsTasks(List<VideoTranscriptsTaskDO> taskList) {
        if (taskList == null || taskList.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (VideoTranscriptsTaskDO task : taskList) {
            try {
                int result = insertVideoTranscriptsTask(task);
                if (result > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                log.warn("批量插入视频转写任务失败, workId: {}, taskId: {}",
                        task.getWorkId(), task.getTaskId(), e);
            }
        }
        return successCount;
    }

    // ==================== 更新操作 ====================

    /**
     * 根据ID更新视频转写任务记录
     *
     * @param taskDO 视频转写任务实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateVideoTranscriptsTask(VideoTranscriptsTaskDO taskDO) {
        if (taskDO == null || taskDO.getId() == null) {
            log.warn("更新视频转写任务失败：任务对象或ID为空");
            return 0;
        }

        // 设置更新时间
        taskDO.setUpdateTime(LocalDateTime.now());

        return videoTranscriptsTaskMapper.updateById(taskDO);
    }

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 任务状态（0-分析中，1-分析完成，2-分析失败）
     * @return 更新影响的行数
     */
    public int updateTaskStatus(String taskId, Integer status) {
        if (!StringUtils.hasText(taskId) || status == null) {
            log.warn("更新任务状态失败：参数为空, taskId: {}, status: {}", taskId, status);
            return 0;
        }

        LambdaUpdateWrapper<VideoTranscriptsTaskDO> updateWrapper = new LambdaUpdateWrapper<VideoTranscriptsTaskDO>()
                .eq(VideoTranscriptsTaskDO::getTaskId, taskId)
                .eq(VideoTranscriptsTaskDO::getIsDel, 0)
                .set(VideoTranscriptsTaskDO::getStatus, status)
                .set(VideoTranscriptsTaskDO::getUpdateTime, LocalDateTime.now());

        return videoTranscriptsTaskMapper.update(null, updateWrapper);
    }

    /**
     * 更新任务结果
     *
     * @param taskId 任务ID
     * @param result 分析结果
     * @param resultJson 分析结果JSON
     * @param status 任务状态
     * @return 更新影响的行数
     */
    public int updateTaskResult(String taskId, String result, String resultJson, Integer status) {
        if (!StringUtils.hasText(taskId)) {
            log.warn("更新任务结果失败：taskId为空");
            return 0;
        }

        LambdaUpdateWrapper<VideoTranscriptsTaskDO> updateWrapper = new LambdaUpdateWrapper<VideoTranscriptsTaskDO>()
                .eq(VideoTranscriptsTaskDO::getTaskId, taskId)
                .eq(VideoTranscriptsTaskDO::getIsDel, 0)
                .set(VideoTranscriptsTaskDO::getResult, result)
                .set(VideoTranscriptsTaskDO::getResultJson, resultJson)
                .set(VideoTranscriptsTaskDO::getStatus, status)
                .set(VideoTranscriptsTaskDO::getUpdateTime, LocalDateTime.now());

        return videoTranscriptsTaskMapper.update(null, updateWrapper);
    }

    /**
     * 更新任务错误信息
     *
     * @param taskId 任务ID
     * @param errMsg 错误信息
     * @return 更新影响的行数
     */
    public int updateTaskError(String taskId, String errMsg) {
        if (!StringUtils.hasText(taskId)) {
            log.warn("更新任务错误信息失败：taskId为空");
            return 0;
        }

        LambdaUpdateWrapper<VideoTranscriptsTaskDO> updateWrapper = new LambdaUpdateWrapper<VideoTranscriptsTaskDO>()
                .eq(VideoTranscriptsTaskDO::getTaskId, taskId)
                .eq(VideoTranscriptsTaskDO::getIsDel, 0)
                .set(VideoTranscriptsTaskDO::getErrMsg, errMsg)
                .set(VideoTranscriptsTaskDO::getStatus, 2) // 2-分析失败
                .set(VideoTranscriptsTaskDO::getUpdateTime, LocalDateTime.now());

        return videoTranscriptsTaskMapper.update(null, updateWrapper);
    }

    /**
     * 根据workId删除视频转写任务记录（逻辑删除）
     *
     * @param workId 作品唯一标识
     * @return 删除影响的行数
     */
    public int deleteTaskByWorkId(String workId) {
        if (!StringUtils.hasText(workId)) {
            log.warn("删除任务失败：workId为空");
            return 0;
        }

        LambdaUpdateWrapper<VideoTranscriptsTaskDO> updateWrapper = new LambdaUpdateWrapper<VideoTranscriptsTaskDO>()
                .eq(VideoTranscriptsTaskDO::getWorkId, workId)
                .eq(VideoTranscriptsTaskDO::getIsDel, 0)
                .set(VideoTranscriptsTaskDO::getIsDel, 1)
                .set(VideoTranscriptsTaskDO::getUpdateTime, LocalDateTime.now());

        return videoTranscriptsTaskMapper.update(null, updateWrapper);
    }

    // ==================== 查询操作 ====================

    /**
     * 根据ID查询视频转写任务记录
     *
     * @param id 主键ID
     * @return 视频转写任务记录
     */
    public VideoTranscriptsTaskDO queryVideoTranscriptsTaskById(Long id) {
        if (id == null) {
            return null;
        }
        return videoTranscriptsTaskMapper.selectById(id);
    }

    /**
     * 根据taskId查询视频转写任务记录
     *
     * @param taskId 任务ID
     * @return 视频转写任务记录
     */
    public VideoTranscriptsTaskDO queryVideoTranscriptsTaskByTaskId(String taskId) {
        if (!StringUtils.hasText(taskId)) {
            return null;
        }

        LambdaQueryWrapper<VideoTranscriptsTaskDO> queryWrapper = new LambdaQueryWrapper<VideoTranscriptsTaskDO>()
                .eq(VideoTranscriptsTaskDO::getTaskId, taskId)
                .eq(VideoTranscriptsTaskDO::getIsDel, 0);

        return videoTranscriptsTaskMapper.selectOne(queryWrapper);
    }

    /**
     * 根据workId查询视频转写任务记录
     *
     * @param workId 作品唯一标识
     * @return 视频转写任务记录
     */
    public VideoTranscriptsTaskDO queryVideoTranscriptsTaskByWorkId(String workId) {
        if (!StringUtils.hasText(workId)) {
            return null;
        }

        LambdaQueryWrapper<VideoTranscriptsTaskDO> queryWrapper = new LambdaQueryWrapper<VideoTranscriptsTaskDO>()
                .eq(VideoTranscriptsTaskDO::getWorkId, workId)
                .eq(VideoTranscriptsTaskDO::getIsDel, 0)
                .orderByDesc(VideoTranscriptsTaskDO::getCreateTime);

        return videoTranscriptsTaskMapper.selectOne(queryWrapper);
    }

    /**
     * 根据workId列表查询视频转写任务记录列表
     *
     * @param workIdList 作品唯一标识列表
     * @return 视频转写任务记录列表
     */
    public List<VideoTranscriptsTaskDO> queryVideoTranscriptsTaskListByWorkIds(List<String> workIdList) {
        if (workIdList == null || workIdList.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<VideoTranscriptsTaskDO> queryWrapper = new LambdaQueryWrapper<VideoTranscriptsTaskDO>()
                .in(VideoTranscriptsTaskDO::getWorkId, workIdList)
                .eq(VideoTranscriptsTaskDO::getIsDel, 0)
                .orderByDesc(VideoTranscriptsTaskDO::getCreateTime);

        return videoTranscriptsTaskMapper.selectList(queryWrapper);
    }

    /**
     * 根据平台查询视频转写任务记录列表
     *
     * @param platform 平台（dy、xhs等）
     * @return 视频转写任务记录列表
     */
    public List<VideoTranscriptsTaskDO> queryVideoTranscriptsTaskListByPlatform(String platform) {
        if (!StringUtils.hasText(platform)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<VideoTranscriptsTaskDO> queryWrapper = new LambdaQueryWrapper<VideoTranscriptsTaskDO>()
                .eq(VideoTranscriptsTaskDO::getPlatform, platform)
                .eq(VideoTranscriptsTaskDO::getIsDel, 0)
                .orderByDesc(VideoTranscriptsTaskDO::getCreateTime);

        return videoTranscriptsTaskMapper.selectList(queryWrapper);
    }

    /**
     * 根据状态查询视频转写任务记录列表
     *
     * @param status 任务状态（0-分析中，1-分析完成，2-分析失败）
     * @return 视频转写任务记录列表
     */
    public List<VideoTranscriptsTaskDO> queryVideoTranscriptsTaskListByStatus(Integer status) {
        if (status == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<VideoTranscriptsTaskDO> queryWrapper = new LambdaQueryWrapper<VideoTranscriptsTaskDO>()
                .eq(VideoTranscriptsTaskDO::getStatus, status)
                .eq(VideoTranscriptsTaskDO::getIsDel, 0)
                .orderByDesc(VideoTranscriptsTaskDO::getCreateTime);

        return videoTranscriptsTaskMapper.selectList(queryWrapper);
    }

    /**
     * 查询正在分析中的任务列表
     *
     * @return 正在分析中的任务列表
     */
    public List<VideoTranscriptsTaskDO> queryRunningTasks() {
        return queryVideoTranscriptsTaskListByStatus(0);
    }

    /**
     * 查询已完成的任务列表
     *
     * @return 已完成的任务列表
     */
    public List<VideoTranscriptsTaskDO> queryCompletedTasks() {
        return queryVideoTranscriptsTaskListByStatus(1);
    }

    /**
     * 查询失败的任务列表
     *
     * @return 失败的任务列表
     */
    public List<VideoTranscriptsTaskDO> queryFailedTasks() {
        return queryVideoTranscriptsTaskListByStatus(2);
    }

    /**
     * 检查任务是否存在
     *
     * @param taskId 任务ID
     * @return true-存在，false-不存在
     */
    public boolean existsTask(String taskId) {
        if (!StringUtils.hasText(taskId)) {
            return false;
        }

        LambdaQueryWrapper<VideoTranscriptsTaskDO> queryWrapper = new LambdaQueryWrapper<VideoTranscriptsTaskDO>()
                .eq(VideoTranscriptsTaskDO::getTaskId, taskId)
                .eq(VideoTranscriptsTaskDO::getIsDel, 0)
                .last("LIMIT 1");

        return videoTranscriptsTaskMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 检查作品是否已有转写任务
     *
     * @param workId 作品唯一标识
     * @return true-存在，false-不存在
     */
    public boolean existsTaskByWorkId(String workId) {
        if (!StringUtils.hasText(workId)) {
            return false;
        }

        LambdaQueryWrapper<VideoTranscriptsTaskDO> queryWrapper = new LambdaQueryWrapper<VideoTranscriptsTaskDO>()
                .eq(VideoTranscriptsTaskDO::getWorkId, workId)
                .eq(VideoTranscriptsTaskDO::getIsDel, 0)
                .last("LIMIT 1");

        return videoTranscriptsTaskMapper.selectCount(queryWrapper) > 0;
    }

    // ==================== AuthorDisassemblyTaskRecord 操作 ====================

    /**
     * 插入作者拆解任务记录
     *
     * @param taskDO 作者拆解任务实体对象
     * @return 插入影响的行数
     */
    public int insertAuthorDisassemblyTask(AuthorDisassemblyTaskRecordDO taskDO) {
        if (taskDO == null) {
            log.warn("插入作者拆解任务失败：任务对象为空");
            return 0;
        }

        // 设置创建时间
        if (taskDO.getCreateTime() == null) {
            taskDO.setCreateTime(LocalDateTime.now());
        }

        // 设置更新时间
        if (taskDO.getUpdateTime() == null) {
            taskDO.setUpdateTime(LocalDateTime.now());
        }

        // 设置默认删除标记
        if (taskDO.getIsDel() == null) {
            taskDO.setIsDel(0);
        }

        // 设置默认任务状态
        if (taskDO.getTaskStatus() == null) {
            taskDO.setTaskStatus(0); // 0-开始
        }

        return authorDisassemblyTaskRecordMapper.insert(taskDO);
    }

    /**
     * 创建新的作者拆解任务
     *
     * @param authorId 作者ID
     * @param uniqueId 用户名
     * @param taskId 任务ID
     * @param creator 创建者ID
     * @return 插入影响的行数
     */
    public int createAuthorDisassemblyTask(String authorId, String uniqueId, String taskId, Long creator) {
        AuthorDisassemblyTaskRecordDO taskDO = AuthorDisassemblyTaskRecordDO.builder()
                .authorId(authorId)
                .uniqueId(uniqueId)
                .taskId(taskId)
                .taskStatus(0) // 0-开始
                .creator(creator)
                .updater(creator)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .isDel(0)
                .build();

        return insertAuthorDisassemblyTask(taskDO);
    }

    /**
     * 查询指定账号正在进行中的拆解任务
     *
     * @param uniqueId 用户名
     * @return 正在进行中的任务记录，如果没有则返回null
     */
    public AuthorDisassemblyTaskRecordDO queryRunningDisassemblyTaskByUniqueId(String uniqueId) {
        if (!StringUtils.hasText(uniqueId)) {
            return null;
        }

        LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO> queryWrapper = new LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO>()
                .eq(AuthorDisassemblyTaskRecordDO::getUniqueId, uniqueId)
                .eq(AuthorDisassemblyTaskRecordDO::getTaskStatus, 0) // 0-进行中
                .eq(AuthorDisassemblyTaskRecordDO::getIsDel, 0)
                .orderByDesc(AuthorDisassemblyTaskRecordDO::getCreateTime);

        return authorDisassemblyTaskRecordMapper.selectOne(queryWrapper);
    }

    /**
     * 根据taskId查询作者拆解任务记录
     *
     * @param taskId 任务ID
     * @return 作者拆解任务记录
     */
    public AuthorDisassemblyTaskRecordDO queryAuthorDisassemblyTaskByTaskId(String taskId) {
        if (!StringUtils.hasText(taskId)) {
            return null;
        }

        LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO> queryWrapper = new LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO>()
                .eq(AuthorDisassemblyTaskRecordDO::getTaskId, taskId)
                .eq(AuthorDisassemblyTaskRecordDO::getIsDel, 0)
                .orderByDesc(AuthorDisassemblyTaskRecordDO::getCreateTime);

        return authorDisassemblyTaskRecordMapper.selectOne(queryWrapper);
    }

    /**
     * 批量插入作者拆解任务记录
     *
     * @param taskList 作者拆解任务实体对象列表
     * @return 插入成功的记录数
     */
    public int batchInsertAuthorDisassemblyTasks(List<AuthorDisassemblyTaskRecordDO> taskList) {
        if (taskList == null || taskList.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (AuthorDisassemblyTaskRecordDO task : taskList) {
            try {
                int result = insertAuthorDisassemblyTask(task);
                if (result > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                log.warn("批量插入作者拆解任务失败, authorId: {}, uniqueId: {}",
                        task.getAuthorId(), task.getUniqueId(), e);
            }
        }
        return successCount;
    }

    // ==================== AuthorDisassemblyTaskRecord 更新操作 ====================

    /**
     * 根据ID更新作者拆解任务记录
     *
     * @param taskDO 作者拆解任务实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateAuthorDisassemblyTask(AuthorDisassemblyTaskRecordDO taskDO) {
        if (taskDO == null || taskDO.getId() == null) {
            log.warn("更新作者拆解任务失败：任务对象或ID为空");
            return 0;
        }

        // 设置更新时间
        taskDO.setUpdateTime(LocalDateTime.now());

        return authorDisassemblyTaskRecordMapper.updateById(taskDO);
    }

    /**
     * 更新作者拆解任务状态
     *
     * @param id 任务ID
     * @param taskStatus 任务状态（0-开始，1-完成，2-失败）
     * @param updater 更新者ID
     * @return 更新影响的行数
     */
    public int updateAuthorDisassemblyTaskStatus(Long id, Integer taskStatus, Long updater) {
        if (id == null || taskStatus == null) {
            log.warn("更新作者拆解任务状态失败：参数为空, id: {}, taskStatus: {}", id, taskStatus);
            return 0;
        }

        LambdaUpdateWrapper<AuthorDisassemblyTaskRecordDO> updateWrapper = new LambdaUpdateWrapper<AuthorDisassemblyTaskRecordDO>()
                .eq(AuthorDisassemblyTaskRecordDO::getId, id)
                .eq(AuthorDisassemblyTaskRecordDO::getIsDel, 0)
                .set(AuthorDisassemblyTaskRecordDO::getTaskStatus, taskStatus)
                .set(AuthorDisassemblyTaskRecordDO::getUpdater, updater)
                .set(AuthorDisassemblyTaskRecordDO::getUpdateTime, LocalDateTime.now());

        return authorDisassemblyTaskRecordMapper.update(null, updateWrapper);
    }

    /**
     * 更新作者拆解任务状态（根据authorId和uniqueId）
     *
     * @param authorId 作者ID
     * @param uniqueId 用户名
     * @param taskStatus 任务状态（0-开始，1-完成，2-失败）
     * @param updater 更新者ID
     * @return 更新影响的行数
     */
    public int updateAuthorDisassemblyTaskStatusByAuthor(String authorId, String uniqueId,
                                                        Integer taskStatus, Long updater) {
        if (!StringUtils.hasText(authorId) || !StringUtils.hasText(uniqueId) || taskStatus == null) {
            log.warn("更新作者拆解任务状态失败：参数为空, authorId: {}, uniqueId: {}, taskStatus: {}",
                    authorId, uniqueId, taskStatus);
            return 0;
        }

        LambdaUpdateWrapper<AuthorDisassemblyTaskRecordDO> updateWrapper = new LambdaUpdateWrapper<AuthorDisassemblyTaskRecordDO>()
                .eq(AuthorDisassemblyTaskRecordDO::getAuthorId, authorId)
                .eq(AuthorDisassemblyTaskRecordDO::getUniqueId, uniqueId)
                .eq(AuthorDisassemblyTaskRecordDO::getIsDel, 0)
                .set(AuthorDisassemblyTaskRecordDO::getTaskStatus, taskStatus)
                .set(AuthorDisassemblyTaskRecordDO::getUpdater, updater)
                .set(AuthorDisassemblyTaskRecordDO::getUpdateTime, LocalDateTime.now());

        return authorDisassemblyTaskRecordMapper.update(null, updateWrapper);
    }

    /**
     * 更新作者拆解任务结果
     *
     * @param id 任务ID
     * @param resJson 结果JSON
     * @param taskStatus 任务状态
     * @param updater 更新者ID
     * @return 更新影响的行数
     */
    public int updateAuthorDisassemblyTaskResult(Long id, String resJson, Integer taskStatus, Long updater) {
        if (id == null) {
            log.warn("更新作者拆解任务结果失败：id为空");
            return 0;
        }

        LambdaUpdateWrapper<AuthorDisassemblyTaskRecordDO> updateWrapper = new LambdaUpdateWrapper<AuthorDisassemblyTaskRecordDO>()
                .eq(AuthorDisassemblyTaskRecordDO::getId, id)
                .eq(AuthorDisassemblyTaskRecordDO::getIsDel, 0)
                .set(AuthorDisassemblyTaskRecordDO::getResJson, resJson)
                .set(AuthorDisassemblyTaskRecordDO::getTaskStatus, taskStatus)
                .set(AuthorDisassemblyTaskRecordDO::getUpdater, updater)
                .set(AuthorDisassemblyTaskRecordDO::getUpdateTime, LocalDateTime.now());

        return authorDisassemblyTaskRecordMapper.update(null, updateWrapper);
    }

    /**
     * 根据authorId和uniqueId删除作者拆解任务记录（逻辑删除）
     *
     * @param authorId 作者ID
     * @param uniqueId 用户名
     * @param updater 更新者ID
     * @return 删除影响的行数
     */
    public int deleteAuthorDisassemblyTaskByAuthor(String authorId, String uniqueId, Long updater) {
        if (!StringUtils.hasText(authorId) || !StringUtils.hasText(uniqueId)) {
            log.warn("删除作者拆解任务失败：参数为空, authorId: {}, uniqueId: {}", authorId, uniqueId);
            return 0;
        }

        LambdaUpdateWrapper<AuthorDisassemblyTaskRecordDO> updateWrapper = new LambdaUpdateWrapper<AuthorDisassemblyTaskRecordDO>()
                .eq(AuthorDisassemblyTaskRecordDO::getAuthorId, authorId)
                .eq(AuthorDisassemblyTaskRecordDO::getUniqueId, uniqueId)
                .eq(AuthorDisassemblyTaskRecordDO::getIsDel, 0)
                .set(AuthorDisassemblyTaskRecordDO::getIsDel, 1)
                .set(AuthorDisassemblyTaskRecordDO::getUpdater, updater)
                .set(AuthorDisassemblyTaskRecordDO::getUpdateTime, LocalDateTime.now());

        return authorDisassemblyTaskRecordMapper.update(null, updateWrapper);
    }

    // ==================== AuthorDisassemblyTaskRecord 查询操作 ====================

    /**
     * 根据ID查询作者拆解任务记录
     *
     * @param id 主键ID
     * @return 作者拆解任务记录
     */
    public AuthorDisassemblyTaskRecordDO queryAuthorDisassemblyTaskById(Long id) {
        if (id == null) {
            return null;
        }
        return authorDisassemblyTaskRecordMapper.selectById(id);
    }

    /**
     * 根据authorId查询作者拆解任务记录
     *
     * @param authorId 作者ID
     * @return 作者拆解任务记录
     */
    public AuthorDisassemblyTaskRecordDO queryAuthorDisassemblyTaskByAuthorId(String authorId) {
        if (!StringUtils.hasText(authorId)) {
            return null;
        }

        LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO> queryWrapper = new LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO>()
                .eq(AuthorDisassemblyTaskRecordDO::getAuthorId, authorId)
                .eq(AuthorDisassemblyTaskRecordDO::getIsDel, 0)
                .orderByDesc(AuthorDisassemblyTaskRecordDO::getCreateTime);

        return authorDisassemblyTaskRecordMapper.selectOne(queryWrapper);
    }

    /**
     * 根据uniqueId查询作者拆解任务记录
     *
     * @param uniqueId 用户名
     * @return 作者拆解任务记录
     */
    public AuthorDisassemblyTaskRecordDO queryAuthorDisassemblyTaskByUniqueId(String uniqueId) {
        if (!StringUtils.hasText(uniqueId)) {
            return null;
        }

        LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO> queryWrapper = new LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO>()
                .eq(AuthorDisassemblyTaskRecordDO::getUniqueId, uniqueId)
                .eq(AuthorDisassemblyTaskRecordDO::getIsDel, 0)
                .orderByDesc(AuthorDisassemblyTaskRecordDO::getCreateTime);

        return authorDisassemblyTaskRecordMapper.selectOne(queryWrapper);
    }

    /**
     * 根据authorId和uniqueId查询作者拆解任务记录
     *
     * @param authorId 作者ID
     * @param uniqueId 用户名
     * @return 作者拆解任务记录
     */
    public AuthorDisassemblyTaskRecordDO queryAuthorDisassemblyTaskByAuthorAndUniqueId(String authorId, String uniqueId) {
        if (!StringUtils.hasText(authorId) || !StringUtils.hasText(uniqueId)) {
            return null;
        }

        LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO> queryWrapper = new LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO>()
                .eq(AuthorDisassemblyTaskRecordDO::getAuthorId, authorId)
                .eq(AuthorDisassemblyTaskRecordDO::getUniqueId, uniqueId)
                .eq(AuthorDisassemblyTaskRecordDO::getIsDel, 0)
                .orderByDesc(AuthorDisassemblyTaskRecordDO::getCreateTime);

        return authorDisassemblyTaskRecordMapper.selectOne(queryWrapper);
    }

    /**
     * 根据authorId列表查询作者拆解任务记录列表
     *
     * @param authorIdList 作者ID列表
     * @return 作者拆解任务记录列表
     */
    public List<AuthorDisassemblyTaskRecordDO> queryAuthorDisassemblyTaskListByAuthorIds(List<String> authorIdList) {
        if (authorIdList == null || authorIdList.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO> queryWrapper = new LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO>()
                .in(AuthorDisassemblyTaskRecordDO::getAuthorId, authorIdList)
                .eq(AuthorDisassemblyTaskRecordDO::getIsDel, 0)
                .orderByDesc(AuthorDisassemblyTaskRecordDO::getCreateTime);

        return authorDisassemblyTaskRecordMapper.selectList(queryWrapper);
    }

    /**
     * 根据uniqueId列表查询作者拆解任务记录列表
     *
     * @param uniqueIdList 用户名列表
     * @return 作者拆解任务记录列表
     */
    public List<AuthorDisassemblyTaskRecordDO> queryAuthorDisassemblyTaskListByUniqueIds(List<String> uniqueIdList) {
        if (uniqueIdList == null || uniqueIdList.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO> queryWrapper = new LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO>()
                .in(AuthorDisassemblyTaskRecordDO::getUniqueId, uniqueIdList)
                .eq(AuthorDisassemblyTaskRecordDO::getIsDel, 0)
                .orderByDesc(AuthorDisassemblyTaskRecordDO::getCreateTime);

        return authorDisassemblyTaskRecordMapper.selectList(queryWrapper);
    }

    /**
     * 根据任务状态查询作者拆解任务记录列表
     *
     * @param taskStatus 任务状态（0-开始，1-完成，2-失败）
     * @return 作者拆解任务记录列表
     */
    public List<AuthorDisassemblyTaskRecordDO> queryAuthorDisassemblyTaskListByStatus(Integer taskStatus) {
        if (taskStatus == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO> queryWrapper = new LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO>()
                .eq(AuthorDisassemblyTaskRecordDO::getTaskStatus, taskStatus)
                .eq(AuthorDisassemblyTaskRecordDO::getIsDel, 0)
                .orderByDesc(AuthorDisassemblyTaskRecordDO::getCreateTime);

        return authorDisassemblyTaskRecordMapper.selectList(queryWrapper);
    }

    /**
     * 根据创建者查询作者拆解任务记录列表
     *
     * @param creator 创建者ID
     * @return 作者拆解任务记录列表
     */
    public List<AuthorDisassemblyTaskRecordDO> queryAuthorDisassemblyTaskListByCreator(Long creator) {
        if (creator == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO> queryWrapper = new LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO>()
                .eq(AuthorDisassemblyTaskRecordDO::getCreator, creator)
                .eq(AuthorDisassemblyTaskRecordDO::getIsDel, 0)
                .orderByDesc(AuthorDisassemblyTaskRecordDO::getCreateTime);

        return authorDisassemblyTaskRecordMapper.selectList(queryWrapper);
    }

    /**
     * 查询正在进行中的作者拆解任务列表
     *
     * @return 正在进行中的任务列表
     */
    public List<AuthorDisassemblyTaskRecordDO> queryRunningAuthorDisassemblyTasks() {
        return queryAuthorDisassemblyTaskListByStatus(0);
    }

    /**
     * 查询已完成的作者拆解任务列表
     *
     * @return 已完成的任务列表
     */
    public List<AuthorDisassemblyTaskRecordDO> queryCompletedAuthorDisassemblyTasks() {
        return queryAuthorDisassemblyTaskListByStatus(1);
    }

    /**
     * 查询失败的作者拆解任务列表
     *
     * @return 失败的任务列表
     */
    public List<AuthorDisassemblyTaskRecordDO> queryFailedAuthorDisassemblyTasks() {
        return queryAuthorDisassemblyTaskListByStatus(2);
    }

    /**
     * 检查作者拆解任务是否存在
     *
     * @param authorId 作者ID
     * @param uniqueId 用户名
     * @return true-存在，false-不存在
     */
    public boolean existsAuthorDisassemblyTask(String authorId, String uniqueId) {
        if (!StringUtils.hasText(authorId) || !StringUtils.hasText(uniqueId)) {
            return false;
        }

        LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO> queryWrapper = new LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO>()
                .eq(AuthorDisassemblyTaskRecordDO::getAuthorId, authorId)
                .eq(AuthorDisassemblyTaskRecordDO::getUniqueId, uniqueId)
                .eq(AuthorDisassemblyTaskRecordDO::getIsDel, 0)
                .last("LIMIT 1");

        return authorDisassemblyTaskRecordMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 检查作者是否已有拆解任务（根据authorId）
     *
     * @param authorId 作者ID
     * @return true-存在，false-不存在
     */
    public boolean existsAuthorDisassemblyTaskByAuthorId(String authorId) {
        if (!StringUtils.hasText(authorId)) {
            return false;
        }

        LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO> queryWrapper = new LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO>()
                .eq(AuthorDisassemblyTaskRecordDO::getAuthorId, authorId)
                .eq(AuthorDisassemblyTaskRecordDO::getIsDel, 0)
                .last("LIMIT 1");

        return authorDisassemblyTaskRecordMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 检查用户是否已有拆解任务（根据uniqueId）
     *
     * @param uniqueId 用户名
     * @return true-存在，false-不存在
     */
    public boolean existsAuthorDisassemblyTaskByUniqueId(String uniqueId) {
        if (!StringUtils.hasText(uniqueId)) {
            return false;
        }

        LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO> queryWrapper = new LambdaQueryWrapper<AuthorDisassemblyTaskRecordDO>()
                .eq(AuthorDisassemblyTaskRecordDO::getUniqueId, uniqueId)
                .eq(AuthorDisassemblyTaskRecordDO::getIsDel, 0)
                .last("LIMIT 1");

        return authorDisassemblyTaskRecordMapper.selectCount(queryWrapper) > 0;
    }

}
