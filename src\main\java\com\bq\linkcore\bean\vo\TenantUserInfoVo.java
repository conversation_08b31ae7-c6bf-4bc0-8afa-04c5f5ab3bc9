package com.bq.linkcore.bean.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TenantUserInfoVo {


    private Long userId;

    private String nickname;

    private String phone;

    private String roleCode;

    private String roleName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private Integer redBookAuthCount;

}
