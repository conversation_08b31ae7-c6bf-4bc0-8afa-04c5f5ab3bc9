package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-15 02:16:14
 * @ClassName: TiktokArticleVideosDO
 * @Description: TikTok作品视频详情表
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("tiktok_article_videos")
public class TiktokArticleVideosDO implements Serializable{

    private static final long serialVersionUID = 1L;

    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 视频ID
     */
    private String videoId;

    /**
     * 视频质量标签
     */
    private String videoQuality;

    /**
     * 视频质量评分(VQScore)
     */
    private String vqScore;

    /**
     * 视频比特率(bps)
     */
    private Long bitrate;

    /**
     * 编解码器类型(H.264/AV1等)
     */
    private String codecType;

    /**
     * 分辨率(540p/1080p等)
     */
    private String definition;

    /**
     * 视频时长(毫秒)
     */
    private Long duration;

    /**
     * 视频大小(字节)
     */
    private Long dataSize;

    /**
     * 视频高度(像素)
     */
    private Integer height;

    /**
     * 视频宽度(像素)
     */
    private Integer width;

    /**
     * 视频封面URL
     */
    private String cover;

    /**
     * 视频页面URL
     */
    private String url;




}
