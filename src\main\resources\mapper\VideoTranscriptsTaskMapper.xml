<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.VideoTranscriptsTaskMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.VideoTranscriptsTaskDO">
        <id column="id" property="id" />
        <result column="work_id" property="workId" />
        <result column="video_url" property="videoUrl" />
        <result column="video_duration" property="videoDuration" />
        <result column="task_id" property="taskId" />
        <result column="platform" property="platform" />
        <result column="result" property="result" />
        <result column="result_json" property="resultJson" />
        <result column="status" property="status" />
        <result column="err_msg" property="errMsg" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="is_del" property="isDel" />
    </resultMap>
    <sql id="Base_Column_List">
        id, work_id, video_url, video_duration, task_id, platform, result, result_json, status, err_msg, update_time, create_time, is_del
    </sql>

  

  
</mapper>
