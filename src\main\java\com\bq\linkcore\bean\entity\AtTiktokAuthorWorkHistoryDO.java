package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-22 15:40:20
 * @ClassName: AtTiktokAuthorWorkHistoryDO
 * @Description: TikTok作品主表(JSON格式)
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("at_tiktok_author_work_history")
public class AtTiktokAuthorWorkHistoryDO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 作品唯一标识
     */
    private String workId;

    /**
     * 三方生成唯一ID
     */
    private String workUuid;

    /**
     * 发布账号ID
     */
    private String authorId;

    /**
     * 作者唯一用户名
     */
    private String uniqueId;

    /**
     * 作者账号加密ID
     */
    private String secUid;

    /**
     * 作品链接
     */
    private String url;

    /**
     * 分类类型(对应TikTok分类体系)
     */
    private Integer categoryType;

    /**
     * 封面图链接
     */
    private String thumbnailLink;

    /**
     * 是否广告(1:是,0:否)
     */
    private Integer isAd;

    /**
     * 作品标题
     */
    private String title;

    /**
     * 作品内容描述
     */
    private String content;

    /**
     * 话题标签数组，格式: ["#tag1","#tag2"]
     */
    private String hashtags;

    /**
     * 图片URL数组，格式: ["url1","url2"]
     */
    private String images;

    /**
     * 发布时间戳
     */
    private Integer publishTime;

    /**
     * 文本语言(ISO 639-1)
     */
    private String textLanguage;

    /**
     * 发布地理位置
     */
    private String locationIp;

    /**
     * 播放量
     */
    private Integer playCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 转发数
     */
    private Integer shareCount;

    /**
     * 收藏数
     */
    private Integer collectCount;

    /**
     * 视频ID
     */
    private String videoId;

    /**
     * 音乐ID
     */
    private String musicId;

    /**
     * 是否每日更新
     */
    private Integer isDaily;

    /**
     * 更新日期
     */
    private String recordDay;

    /**
     * 创建者
     */
    private Long creator;

    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;

    /**
     * 数据更新人
     */
    private Long updater;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;




}
