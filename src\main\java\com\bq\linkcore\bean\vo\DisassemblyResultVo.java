package com.bq.linkcore.bean.vo;

import com.bq.linkcore.services.impl.aiAction.models.CompleteReportVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 账号拆解结果返回对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "账号拆解结果")
public class DisassemblyResultVo {
    
    @ApiModelProperty(value = "任务ID")
    private Long taskId;
    
    @ApiModelProperty(value = "账号唯一标识")
    private String atUniqueId;
    
    @ApiModelProperty(value = "任务状态：0-进行中，1-已完成，2-失败")
    private Integer taskStatus;
    
    @ApiModelProperty(value = "任务状态描述")
    private String taskStatusDesc;
    
    @ApiModelProperty(value = "是否有结果数据")
    private Boolean hasResult;
    
    @ApiModelProperty(value = "拆解报告数据")
    private CompleteReportVo reportData;
    
    @ApiModelProperty(value = "错误信息（任务失败时）")
    private String errorMessage;
    
    @ApiModelProperty(value = "任务创建时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty(value = "任务更新时间")
    private LocalDateTime updateTime;
    
    @ApiModelProperty(value = "处理进度描述")
    private String progressDesc;
    
    /**
     * 获取处理进度描述
     */
    public String getProgressDesc() {
        if (taskStatus == null) {
            return "未知状态";
        }
        
        switch (taskStatus) {
            case 0:
                return "正在分析账号数据，请稍候...";
            case 1:
                if (hasResult != null && hasResult) {
                    return "分析完成，已生成拆解报告";
                } else {
                    return "分析完成，但结果数据异常";
                }
            case 2:
                return "分析失败：" + (errorMessage != null ? errorMessage : "未知错误");
            default:
                return "未知状态";
        }
    }
    
    /**
     * 判断任务是否完成（成功或失败）
     */
    public boolean isFinished() {
        return taskStatus != null && (taskStatus == 1 || taskStatus == 2);
    }
    
    /**
     * 判断任务是否成功完成
     */
    public boolean isSuccess() {
        return taskStatus != null && taskStatus == 1 && hasResult != null && hasResult;
    }
    
    /**
     * 判断任务是否失败
     */
    public boolean isFailed() {
        return taskStatus != null && taskStatus == 2;
    }
    
    /**
     * 判断任务是否正在进行中
     */
    public boolean isRunning() {
        return taskStatus != null && taskStatus == 0;
    }
}
