<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.AtTiktokAuthorStatDailyMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.AtTiktokAuthorStatDailyDO">
        <id column="id" property="id" />
        <result column="author_id" property="authorId" />
        <result column="unique_id" property="uniqueId" />
        <result column="follower_count" property="followerCount" />
        <result column="following_count" property="followingCount" />
        <result column="heart_count" property="heartCount" />
        <result column="video_count" property="videoCount" />
        <result column="friend_count" property="friendCount" />
        <result column="play_count" property="playCount" />
        <result column="like_count" property="likeCount" />
        <result column="comment_count" property="commentCount" />
        <result column="share_count" property="shareCount" />
        <result column="collect_count" property="collectCount" />
        <result column="stat_day" property="statDay" />
        <result column="stat_time" property="statTime" />
        <result column="is_del" property="isDel" />
    </resultMap>
    <sql id="Base_Column_List">
        id, author_id, unique_id, follower_count, following_count, heart_count, video_count, friend_count, play_count, like_count, comment_count, share_count, collect_count, stat_day, stat_time, is_del
    </sql>

  

  
</mapper>
