<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.TenantMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.TenantDO">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="is_appoint_agent" property="isAppointAgent" />
        <result column="is_del" property="isDel" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, name, code, is_appoint_agent, is_del, creator, create_time, updater, update_time
    </sql>

  

  
</mapper>
