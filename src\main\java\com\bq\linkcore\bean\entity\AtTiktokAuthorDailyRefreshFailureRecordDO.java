package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-22 00:38:37
 * @ClassName: AtTiktokAuthorDailyRefreshFailureRecordDO
 * @Description: 用户每日更新失败记录表
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("at_tiktok_author_daily_refresh_failure_record")
public class AtTiktokAuthorDailyRefreshFailureRecordDO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 发布账号ID
     */
    private String authorId;

    /**
     * 作者唯一用户名
     */
    private String uniqueId;

    /**
     * 作者账号加密ID
     */
    private String secUid;

    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;




}
