package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-18 14:52:51
 * @ClassName: TiktokHashtagInfoDO
 * @Description: TikTok标签排行榜表
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("tiktok_hashtag_info")
public class TiktokHashtagInfoDO implements Serializable{

    private static final long serialVersionUID = 1L;

    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 标签ID
     */
    private String hashtagId;

    /**
     * 标签名称
     */
    private String hashtagName;

    /**
     * 国家标签
     */
    private String countryId;

    /**
     * 行业标签
     */
    private Long industryId;

    /**
     * 是否推广,0=否；1=是
     */
    private Integer isPromoted;

    /**
     * 趋势数据列表
     */
    private String trend;

    /**
     * 创作者列表
     */
    private String creators;

    /**
     * 发布数量
     */
    private Long publishCnt;

    /**
     * 视频观看量
     */
    private Long videoViews;

    /**
     * 排名
     */
    private Integer rank;

    /**
     * 排名变化
     */
    private Integer rankDiff;

    /**
     * 排名变化类型
     */
    private Long rankDiffType;

    /**
     * 时间范围（天），如7、30、120天
     */
    private Integer rPeriod;

    /**
     * 国家代码，如US、UK、JP等
     */
    private String rCountryCode;

    /**
     * 排序方式，"popular"=热门，"new"=最新
     */
    private String rSortBy;

    /**
     * 行业ID，留空返回所有行业
     */
    private String rIndustryId;

    /**
     * 筛选条件，"new_on_board"=新上榜标签
     */
    private String rFilterBy;

    private String recordDay;

    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;




}
