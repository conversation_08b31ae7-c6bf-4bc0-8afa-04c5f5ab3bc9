package com.bq.linkcore.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;
import java.util.UUID;

/**
 * @ClassName: SecretKeyUtil
 * @Description:
 * @author: lmy
 * @date: 2022年01月13日 16:27
 */
public class SecretKeyUtil {

    /**
     * 构建licenseCode
     *
     * @return
     */
    public static String generateSecretKey() {
        String uuid = UUID.randomUUID().toString();
        uuid = SecretKeyUtil.encrypt32(uuid);
        return uuid.toLowerCase();
    }

    public static String encrypt32(String encryptStr) {
        MessageDigest md5;
        try {
            md5 = MessageDigest.getInstance("MD5");
            byte[] md5Bytes = md5.digest(encryptStr.getBytes());
            StringBuffer hexValue = new StringBuffer();
            for (int i = 0; i < md5Bytes.length; i++) {
                int val = ((int) md5Bytes[i]) & 0xff;
                if (val < 16) {
                    hexValue.append("0");
                }
                hexValue.append(Integer.toHexString(val));
            }
            encryptStr = hexValue.toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return encryptStr;
    }

    public static String encodeBase64String(String str) {
        return Base64.getEncoder().encodeToString(str.getBytes(StandardCharsets.UTF_8));
    }

    public static String decodeBase64String(String sign) {
        return new String(Base64.getDecoder().decode(sign.getBytes(StandardCharsets.UTF_8)));
    }

    public static void main(String[] args) {

    }

}