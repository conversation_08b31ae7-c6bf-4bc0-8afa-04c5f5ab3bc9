package com.bq.linkcore.services;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.dto.UserMemberInfo;
import com.bq.linkcore.bean.entity.UserDO;
import com.bq.linkcore.bean.vo.UserInfoVo;
import com.bq.linkcore.bean.vo.UserLoginReqVo;
import com.bq.linkcore.bean.vo.UserMessageReqVo;

public interface IUserService {
    ResponseData sendMessageByEmail(UserMessageReqVo vo);

    ResponseData accountLogin(UserLoginReqVo vo);

    ResponseData loginTxByPhone(UserLoginReqVo vo, UserDO userDO);

    boolean checkMessage(String email, Integer type, String messageCode);

    UserDO queryTxUserByEmail(String email);

    ResponseData insertTxUser(UserLoginReqVo vo);


    UserMemberInfo queryTxUserMember(Long userId);


    /**
     * 校验密码登录是否超过指定次数
     *
     * @param email
     * @return
     */
    boolean checkPwdRepeatLimit(String email);

    UserInfoVo queryTxUser(Long userId);
}
