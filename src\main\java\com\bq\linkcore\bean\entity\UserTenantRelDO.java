package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-13 16:35:54
 * @ClassName: UserTenantRelDO
 * @Description: 用户和企业或租户关系表
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("user_tenant_rel")
public class UserTenantRelDO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 自增长ID
     */
    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 用户外键ID
     */
    private Long userId;

    /**
     * 企业或租户外键ID
     */
    private String tenantCode;




}
