package com.bq.linkcore.services.impl.aiAction;


import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@Service
public class QianwenClient {

    private final Generation gen = new Generation();

    @Value("${qianwen.api-key:sk-806db520b387415fb052d66a4217bceb}")
    private String apiKey;

    @Value("${qianwen.model:qwen-max}")
    private String model;


    public String submitPrompt(String systemPrompt, String userPrompt) throws NoApiKeyException, ApiException, InputRequiredException {
        // 创建系统消息
        Message systemMsg = Message.builder()
                .role(Role.SYSTEM.getValue())
                .content(systemPrompt)
                .build();

        // 创建用户消息
        Message userMsg = Message.builder()
                .role(Role.USER.getValue())
                .content(userPrompt)
                .build();

        // 使用注入的配置属性，并采用官方推荐的消息列表方式
        GenerationParam param = GenerationParam.builder()
                .model(this.model)
                .messages(Arrays.asList(systemMsg, userMsg))
                .apiKey(this.apiKey)
                .resultFormat(GenerationParam.ResultFormat.MESSAGE)
                .build();

        GenerationResult result = gen.call(param);
        return result.getOutput().getChoices().get(0).getMessage().getContent();
    }
}
