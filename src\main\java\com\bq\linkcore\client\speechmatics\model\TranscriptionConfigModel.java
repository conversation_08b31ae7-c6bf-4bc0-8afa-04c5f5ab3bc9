package com.bq.linkcore.client.speechmatics.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * Speechmatics 转录任务配置模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TranscriptionConfigModel {
    
    /**
     * 任务类型，固定为 "transcription"
     */
    @JsonProperty("type")
    private String type;
    
    /**
     * 转录配置
     */
    @JsonProperty("transcription_config")
    private TranscriptionConfig transcriptionConfig;
    
    /**
     * 数据获取配置
     */
    @JsonProperty("fetch_data")
    private FetchData fetchData;

    @JsonProperty("translation_config")
    private TranslationConfig translationConfig;



    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TranslationConfig {
        /**
         * 语言代码，如 "en", "zh"
         */
        @JsonProperty("target_languages")
        private List<String> targetLanguages = Arrays.asList("en", "cmn");

        /**
         * 说话人分离，如 "speaker"
         */
        @JsonProperty("enable_partials")
        private Boolean enablePartials = true;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TranscriptionConfig {
        /**
         * 语言代码，如 "en", "zh"
         */
        @JsonProperty("language")
        private String language;
        
        /**
         * 说话人分离，如 "speaker"
         */
        @JsonProperty("diarization")
        private String diarization;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FetchData {
        /**
         * 音频/视频文件的URL
         */
        @JsonProperty("url")
        private String url;
    }
    
    /**
     * 创建默认的英语转录配置
     * @param fileUrl 文件URL
     * @return 转录配置
     */
    public static TranscriptionConfigModel createDefaultEnglishConfig(String fileUrl) {
        return TranscriptionConfigModel.builder()
                .type("transcription")
                .transcriptionConfig(TranscriptionConfig.builder()
                        .language("en")
                        .diarization("speaker")
                        .build())
                .fetchData(FetchData.builder()
                        .url(fileUrl)
                        .build())
                .build();
    }
    
    /**
     * 创建自定义语言的转录配置
     * @param fileUrl 文件URL
     * @param language 语言代码
     * @return 转录配置
     */
    public static TranscriptionConfigModel createCustomConfig(String fileUrl, String language) {
        return TranscriptionConfigModel.builder()
                .type("transcription")
                .transcriptionConfig(TranscriptionConfig.builder()
                        .language(language)
                        .diarization("speaker")
                        .build())
                .fetchData(FetchData.builder()
                        .url(fileUrl)
                        .build())
                .translationConfig(TranslationConfig.builder()
                        .targetLanguages(Arrays.asList("en", "cmn"))
                        .enablePartials(true)
                        .build())
                .build();
    }
}
