package com.bq.linkcore.utils;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

@Slf4j
public class DateTimeUtil {
    public static boolean verifyDateTime(LocalDateTime endDate, LocalTime endTime) {
        LocalDateTime endDateTime = endDate.withHour(endTime.getHour())
                .withMinute(endDate.getMinute()).withSecond(endTime.getSecond());

        return LocalDateTime.now().isBefore(endDateTime);
    }

    public static boolean verifyTs(LocalDateTime startTs, LocalDateTime endTs) {
        LocalDateTime n = LocalDateTime.now();
        return n.isBefore(endTs) && startTs.isBefore(endTs);
    }

    public static String formatDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        // 创建一个DateTimeFormatter实例来定义日期和时间的格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        String s = startTime.format(formatter);
        String e = endTime.format(formatter);

        return s + "~" + e;
    }

    public static String formatMonthString(LocalDateTime t) {
        // 创建一个DateTimeFormatter实例来定义日期和时间的格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        return t.format(formatter);
    }

    public static String formatDateString(LocalDateTime t) {
        // 创建一个DateTimeFormatter实例来定义日期和时间的格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return t.format(formatter);
    }

    public static LocalDateTime merge(LocalDateTime date, LocalTime time) {
        return date.withHour(time.getHour()).withMinute(time.getMinute()).withSecond(time.getSecond());
    }

    public static LocalDateTime parseDateString(String date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return LocalDate.parse(date, formatter).atStartOfDay();
    }

    public static String parseDateTimeString(LocalDateTime t) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return t.format(formatter);
    }

    public static LocalDateTime parseLocalDateTimeStr(String dateString) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return LocalDateTime.parse(dateString, formatter);
    }

    public static String parseDateTimeString(LocalDateTime t, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return t.format(formatter);
    }

    /**
     * 2025-06-08 19:41
     * @param dateTimeString
     * @return
     */
    public static LocalDateTime parseLocalDateTimeWithoutSeconds(String dateTimeString) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        return LocalDateTime.parse(dateTimeString, formatter);
    }

    public static LocalDateTime monthFirstDay(LocalDateTime n) {
        return n.with(TemporalAdjusters.firstDayOfMonth()).withHour(0).withMinute(0).withSecond(0);
    }

    public static LocalDateTime monthLastDay(LocalDateTime n) {
        return n.with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59).withSecond(59);
    }

    public static LocalDateTime generateMinTime(LocalDateTime t) {
        return t.withHour(0).withMinute(0).withSecond(0).withNano(0);
    }

    public static long minusDays(LocalDateTime t, int diffDays) {
        LocalDateTime dt = t.minusDays(diffDays).withHour(0).withMinute(0).withSecond(0).withNano(0);
        return covertTs(dt);
    }

    public static LocalDateTime generateMaxTime(LocalDateTime t) {
        return t.withHour(23).withMinute(59).withSecond(59).withNano(0);
    }

    public static Long calcCurrentMonthDuration(LocalDateTime startTs, LocalDateTime endTs,
                                                LocalDateTime monthFirstDay, LocalDateTime monthLastDay) {
        LocalDateTime s = startTs.isAfter(monthFirstDay) ? startTs : monthFirstDay;
        LocalDateTime e = endTs.isBefore(monthLastDay) ? endTs : monthLastDay;

        Duration diff = Duration.between(s, e);
        return diff.toMinutes();
    }

    public static LocalDateTime[] generateLast30Days() {
        LocalDateTime[] result = new LocalDateTime[30];
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0).minusDays(1); // 昨天的开始时间
        for (int i = 0; i < 30; i++) {
            result[i] = today.minusDays(i);
        }

        return result;
    }

    /**
     * 最近七天的数据，包含当天 包含下一天
     *
     * @return
     */
    public static List<LocalDateTime> generateLast7DaysContainToday() {
        List<LocalDateTime> result = new ArrayList<>();
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0); // 昨天的开始时间

        result.add(today.plusDays(1));
        for (int i = 0; i < 7; i++) {
            result.add(0, today.minusDays(i));
        }

        return result;
    }


    /**
     * 最近七天的数据，包含当天
     *
     * @return
     */
    public static List<LocalDateTime> generateLastWeekContainToday() {
        List<LocalDateTime> result = new ArrayList<>();
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0); // 昨天的开始时间

        for (int i = 6; i >= 0; i--) {
            result.add(today.minusDays(i));
        }

        return result;
    }

    /**
     * 最近6个月的数据，包含本月
     *
     * @return
     */
    public static List<LocalDateTime> generateLast6Months() {
        List<LocalDateTime> result = new ArrayList<>();

        // 获取当前的日期和时间
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0).minusDays(1); // 昨天的开始时间

        // 将LocalDateTime转换为YearMonth
        YearMonth yearMonth = YearMonth.from(today);

        // 获取当前月份的第一天
        LocalDateTime firstDayOfMonth = yearMonth.atDay(1).atStartOfDay();
        result.add(firstDayOfMonth.plusMonths(1));

        for (int i = 0; i < 6; i++) {
            result.add(0, firstDayOfMonth.minusMonths(i));
        }

        return result;
    }

    public static List<LocalDateTime> generateLastMonths(int recentMonth) {
        List<LocalDateTime> result = new ArrayList<>();
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0).minusDays(1); // 昨天的开始时间
        YearMonth yearMonth = YearMonth.from(today);
        LocalDateTime firstDayOfMonth = yearMonth.atDay(1).atStartOfDay();
        for (int i = 0; i < recentMonth; i++) {
            result.add(0, firstDayOfMonth.minusMonths(i));
        }
        return result;
    }


    public static LocalDateTime covertDateTime(Long ts) {
        Instant instant = Instant.ofEpochSecond(ts);
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    public static LocalDateTime covertDateTimeWithNull(Long ts) {
        if (ts == null) {
            return null;
        }
        Instant instant = Instant.ofEpochSecond(ts);
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    public static long covertTs(LocalDateTime dt) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = dt.atZone(zone).toInstant();

        return instant.getEpochSecond();
    }

    public static long convertUnixTs(LocalDateTime t) {
        // 将LocalDateTime转换为Unix时间戳
        return t.toEpochSecond(ZoneOffset.UTC);
    }

    public static List<LocalDateTime> generateMonthDurationTime(LocalDateTime et) {
        et = generateMaxTime(et);
        return Arrays.asList(generateMinTime(et.minusDays(30)), et);
    }

    public static long getTimeToMidnight(LocalDateTime localDateTime) {
        LocalDateTime midnight = localDateTime.toLocalDate().atTime(LocalTime.MIDNIGHT).plusDays(1);
        Duration duration = Duration.between(localDateTime, midnight);
        return duration.getSeconds();
    }

    public static LocalDateTime unixTimeToLocalDateTime(Long unixTimestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochSecond(unixTimestamp), ZoneId.systemDefault());
    }

    public static LocalDateTime getTodayDateTime() {
        return LocalDateTime.of(LocalDateTime.now().toLocalDate(), LocalTime.MIN);
    }

    public static LocalDateTime getYesterdayDateTime() {
        return LocalDateTime.of(LocalDateTime.now().toLocalDate().minusDays(1), LocalTime.MIN);
    }

    /**
     * yyyy-MM-dd
     *
     * @param dateStr
     * @return
     */
    public static boolean isValidDate(String dateStr) {

        if (StringUtils.isBlank(dateStr)) {
            return false;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        try {
            LocalDate.parse(dateStr, formatter);
        } catch (DateTimeParseException e) {
            return false;
        }
        return true;
    }

    /**
     * 定义时间范围，早上6点到下午9点
     *
     * @return
     */
    public static String generateRandomTime(int startHour, int endHour) {

        LocalTime start = LocalTime.of(startHour, 0);
        LocalTime end = LocalTime.of(endHour, 0);
        long minutesInRange = Duration.between(start, end).toMinutes();
        Random random = new Random();
        long randomMinutes = random.nextInt((int) minutesInRange);
        LocalTime randomTime = start.plusMinutes(randomMinutes);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        return randomTime.format(formatter);
    }

    public static String generateTaskId(){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        return LocalDateTime.now().format(formatter);
    }
    public static void main(String[] args) {
        List<LocalDateTime> days = generateLast7DaysContainToday();
        List<LocalDateTime> months = generateLast6Months();

        Long startTs = DateTimeUtil.minusDays(LocalDateTime.now(), 90);
        System.out.println(startTs);

        System.out.println(generateMaxTime(LocalDateTime.now().minusDays(1)));
        LocalDateTime[] last30Days = generateLast30Days();
        for (LocalDateTime dateTime : last30Days) {
            System.out.println(dateTime);
        }

        LocalDateTime endTime = LocalDateTime.now().minusDays(1);
        LocalDateTime startTime = endTime.minusDays(7);

        System.out.println(convertUnixTs(startTime));

        System.out.println(formatDateRange(startTime.minusDays(1).minusDays(7), startTime.minusDays(1)));

        System.out.println(generateLastMonths(4));

        System.out.println(covertTs(LocalDateTime.now()));

        System.out.println(getTodayDateTime());

        System.out.println(generateLast6Months());

        System.out.println(generateLastWeekContainToday());

        System.out.println(generateRandomTime(0, 6));

        System.out.println(generateTaskId());

//        System.out.println();

    }
}
