<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="CUSTOM_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread]%logger -%msg%n"/>
    <include resource="org/springframework/boot/logging/logback/base.xml"/>
    <appender name="ROLLING-FILE-INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/bq-data.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- daily rollover -->
            <fileNamePattern>logs/bq-data.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 保留30天的历史日志 -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${CUSTOM_LOG_PATTERN}</pattern>
        </encoder>
    </appender>
    <appender name="ROLLING-FILE-WARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/bq-data-warn.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- daily rollover -->
            <fileNamePattern>logs/bq-data-warn.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 保留30天的历史日志 -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${CUSTOM_LOG_PATTERN}</pattern>
        </encoder>
    </appender>
    <logger name="com.bq" level="DEBUG" additivity="false">
        <appender-ref ref="ROLLING-FILE-INFO"/>
        <appender-ref ref="ROLLING-FILE-WARN"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="org.springframework">
        <appender-ref ref="ROLLING-FILE-INFO"/>
        <appender-ref ref="ROLLING-FILE-WARN"/>
    </logger>

</configuration>