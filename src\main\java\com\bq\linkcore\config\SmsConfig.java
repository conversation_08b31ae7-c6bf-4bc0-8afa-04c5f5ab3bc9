package com.bq.linkcore.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName: FeedBackConfig
 * @Description:
 * @author: lmy
 * @date: 2021年10月10日 11:07
 */
@Configuration
@Data
public class SmsConfig {

    @Value("${sms.aliyun.accessKeyId}")
    private String accessKeyId;

    @Value("${sms.aliyun.accessKeySecret}")
    private String accessKeySecret;

    @Value("${sms.aliyun.endpoint}")
    private String endpoint;

    @Value("${sms.aliyun.signName}")
    private String signName;

    @Value("${sms.aliyun.dataCoreMessage}")
    private String dataCoreMessage;

    @Value("${sms.aliyun.quotaMessage}")
    private String quotaMessage;

}
