package com.bq.linkcore.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.Security;


/**
 * <AUTHOR>
 * @date 2021/11/26 19:30
 * @className CryptUtils
 * @description
 */
@Slf4j
public class NetworkEncoder {
    /**
     * 加解密算法
     */
    private static final String ALGORITHM = "AES/CFB/PKCS7Padding";

    private static final String SECRETKEY = "AES";


    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    /**
     * base64解码
     *
     * @param encodeData
     * @return
     */
    public static byte[] base64Decode(byte[] encodeData) {
        try {
            byte[] decodeData = Base64.decodeBase64(encodeData);
            return decodeData;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String decrypt(String content, String key, String ivStr) {
        try {
            Key k = toKey(key.getBytes());
            byte[] encoded = k.getEncoded();
            SecretKeySpec aes = new SecretKeySpec(encoded, SECRETKEY);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            IvParameterSpec iv = new IvParameterSpec(ivStr.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, aes, iv);

            byte[] bytes = cipher.doFinal(Base64.decodeBase64(content));

            return new String(bytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public static String encrypt(String data, String key, String ivStr) {
        try {
            Key k = toKey(key.getBytes());
            byte[] encoded = k.getEncoded();
            SecretKeySpec aes = new SecretKeySpec(encoded, SECRETKEY);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            IvParameterSpec iv = new IvParameterSpec(ivStr.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, aes, iv);
            byte[] bytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.encodeBase64String(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    private static Key toKey(byte[] key) {
        SecretKeySpec aes = new SecretKeySpec(key, "AES");
        return aes;
    }


    public static void main(String[] args) {

        try {
            System.out.println("1");
            return;
        } catch (Exception e) {
            System.out.println("2");
        } finally {
            System.out.println("3");
        }
        /*
        String data = "GV/O8xyxbp3vgyNAOBpt1vUtWw6wpz4cBjIYJoEhyzgd+YoFOeHQf/RJO5SiWGjH";
        String key = "05457a6c148f40c6914e67370e5c738a";
        String iv = "3kf93mg02ga83bj5";
        String outData = decrypt(data, key, iv);
        System.out.println(outData);
         */

        String data = "ABCabc123";
        String key = "83c933c49a5179dd1a8b9d69b19a12aa";
        String iv = "3kf93mg02ga56zbb";
        String outData = encrypt(data, key, iv);
        System.out.println(outData);

    }

}
