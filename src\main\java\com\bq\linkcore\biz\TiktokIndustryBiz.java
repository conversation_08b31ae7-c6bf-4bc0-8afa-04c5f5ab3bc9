package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.TiktokIndustryInfoDO;
import com.bq.linkcore.dao.mapper.TiktokIndustryInfoMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class TiktokIndustryBiz {

    @Resource
    private TiktokIndustryInfoMapper tiktokIndustryInfoMapper;

    public int insert(TiktokIndustryInfoDO industryInfoDO) {
        return tiktokIndustryInfoMapper.insert(industryInfoDO);
    }

    public int update(TiktokIndustryInfoDO industryInfoDO) {
        return tiktokIndustryInfoMapper.updateById(industryInfoDO);
    }

    public TiktokIndustryInfoDO query(Long industryId) {
        LambdaQueryWrapper<TiktokIndustryInfoDO> queryWrapper = new LambdaQueryWrapper<TiktokIndustryInfoDO>()
                .eq(TiktokIndustryInfoDO::getIndustryId, industryId);

        return tiktokIndustryInfoMapper.selectOne(queryWrapper);
    }

}
