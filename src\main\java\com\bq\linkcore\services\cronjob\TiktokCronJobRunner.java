package com.bq.linkcore.services.cronjob;

import com.bq.linkcore.client.tikhub.tiktok.TikHubTiktokAdsRequester;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/7/18 14:12
 * @className TiktokCronjobRunner
 * @description
 */
@Slf4j
@Component
public class TiktokCronJobRunner {
    @Autowired
    private TikHubTiktokAdsRequester tiktokAdsRequester;

    // @Scheduled
    public void refreshHashTag() {

        // tiktokAdsRequester.queryHashTag();
    }
}
