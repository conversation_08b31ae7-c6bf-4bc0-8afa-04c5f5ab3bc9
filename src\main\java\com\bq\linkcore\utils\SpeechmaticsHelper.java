package com.bq.linkcore.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bq.linkcore.client.speechmatics.model.TranscriptResultModel;
import com.bq.linkcore.client.speechmatics.model.VideoCopyModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 视频文案提取工具类
 */
@Slf4j
public class SpeechmaticsHelper {

    /**
     * 从 Speechmatics 转录结果中提取中文文案
     *
     * @param transcriptionResult Speechmatics 转录结果 JSON 字符串
     * @return 中文文案列表
     */
    public static List<VideoCopyModel> extractChineseTranscript(String transcriptionResult) {
        return extractTranscript(transcriptionResult, "cmn");
    }

    /**
     * 从 Speechmatics 转录结果中提取英文文案
     *
     * @param transcriptionResult Speechmatics 转录结果 JSON 字符串
     * @return 英文文案列表
     */
    public static List<VideoCopyModel> extractEnglishTranscript(String transcriptionResult) {
        return extractTranscript(transcriptionResult, "en");
    }

    /**
     * 从 Speechmatics 转录结果中提取原文文案
     * 从 results 数组中提取词和标点符号，重新组合成分段的原文
     *
     * @param transcriptionResult Speechmatics 转录结果 JSON 字符串
     * @return 原文文案列表
     */
    public static List<VideoCopyModel> extractOriginalTranscript(String transcriptionResult) {
        List<VideoCopyModel> copyList = new ArrayList<>();

        if (!StringUtils.hasText(transcriptionResult)) {
            log.warn("转录结果为空，无法提取原文");
            return copyList;
        }

        try {
            // 解析 JSON
            JSONObject jsonObject = JSON.parseObject(transcriptionResult);

            // 获取 results 数组
            JSONArray results = jsonObject.getJSONArray("results");
            if (results == null || results.isEmpty()) {
                log.warn("转录结果中未找到 results 字段");
                return copyList;
            }

            // 重新组合原文
            copyList = reconstructOriginalText(results);

            log.info("成功提取原文文案，共 {} 条", copyList.size());

        } catch (Exception e) {
            log.error("提取原文文案异常", e);
        }

        return copyList;
    }

    /**
     * 重新组合原文文本
     * 将 results 数组中的词和标点符号重新组合成分段的文案
     *
     * @param results results 数组
     * @return 重新组合后的原文文案列表
     */
    private static List<VideoCopyModel> reconstructOriginalText(JSONArray results) {
        List<VideoCopyModel> copyList = new ArrayList<>();

        if (results == null || results.isEmpty()) {
            return copyList;
        }

        StringBuilder currentSentence = new StringBuilder();
        Double sentenceStartTime = null;
        Double sentenceEndTime = null;

        for (int i = 0; i < results.size(); i++) {
            JSONObject item = results.getJSONObject(i);

            String type = item.getString("type");
            if (!"word".equals(type) && !"punctuation".equals(type)) {
                continue; // 只处理词和标点符号
            }

            // 获取内容和时间信息
            JSONArray alternatives = item.getJSONArray("alternatives");
            if (alternatives == null || alternatives.isEmpty()) {
                continue;
            }

            JSONObject alternative = alternatives.getJSONObject(0);
            String content = alternative.getString("content");
            Double startTime = item.getDouble("start_time");
            Double endTime = item.getDouble("end_time");

            if (!StringUtils.hasText(content) || startTime == null || endTime == null) {
                continue;
            }

            // 设置句子开始时间
            if (sentenceStartTime == null) {
                sentenceStartTime = startTime;
            }

            // 处理标点符号的附加规则
            String attachesToValue = item.getString("attaches_to");
            Boolean isEos = item.getBoolean("is_eos"); // end of sentence

            if ("word".equals(type)) {
                // 词之间用空格分隔
                if (currentSentence.length() > 0) {
                    currentSentence.append(" ");
                }
                currentSentence.append(content);
            } else if ("punctuation".equals(type)) {
                // 根据 attaches_to 值处理标点符号
                handlePunctuation(currentSentence, content, attachesToValue);
            }

            sentenceEndTime = endTime;

            // 判断是否句子结束
            if (Boolean.TRUE.equals(isEos) || isEndOfSentence(content)) {
                // 创建一个文案段落
                if (currentSentence.length() > 0 && sentenceStartTime != null && sentenceEndTime != null) {
                    VideoCopyModel copyModel = new VideoCopyModel();
                    copyModel.setContent(currentSentence.toString().trim());
                    copyModel.setStartTime(sentenceStartTime);
                    copyModel.setEndTime(sentenceEndTime);

                    copyList.add(copyModel);
                }

                // 重置句子构建器
                currentSentence = new StringBuilder();
                sentenceStartTime = null;
                sentenceEndTime = null;
            }
        }

        // 处理最后一个句子（如果没有明确的结束标记）
        if (currentSentence.length() > 0 && sentenceStartTime != null && sentenceEndTime != null) {
            VideoCopyModel copyModel = new VideoCopyModel();
            copyModel.setContent(currentSentence.toString().trim());
            copyModel.setStartTime(sentenceStartTime);
            copyModel.setEndTime(sentenceEndTime);

            copyList.add(copyModel);
        }

        return copyList;
    }

    /**
     * 处理标点符号的附加逻辑
     * 根据 attaches_to 值决定标点符号的位置和空格处理
     *
     * @param currentSentence 当前句子构建器
     * @param content 标点符号内容
     * @param attachesToValue attaches_to 字段值 (previous, next, both, none)
     */
    private static void handlePunctuation(StringBuilder currentSentence, String content, String attachesToValue) {
        if (attachesToValue == null) {
            // 如果没有 attaches_to 字段，默认按 none 处理（添加空格）
            attachesToValue = "none";
        }

        switch (attachesToValue) {
            case "previous":
                // 直接附加到前面的词，不加空格
                currentSentence.append(content);
                break;

            case "next":
                // 附加到下一个词，在标点符号后面不加空格
                // 但在标点符号前面可能需要加空格
                if (currentSentence.length() > 0) {
                    currentSentence.append(" ");
                }
                currentSentence.append(content);
                break;

            case "both":
                // 两边都附加，不加空格
                currentSentence.append(content);
                break;

            case "none":
            default:
                // 不附加，按普通处理（前面加空格）
                if (currentSentence.length() > 0) {
                    currentSentence.append(" ");
                }
                currentSentence.append(content);
                break;
        }
    }

    /**
     * 判断是否为句子结束标点符号
     *
     * @param content 内容
     * @return 是否为句子结束标点符号
     */
    private static boolean isEndOfSentence(String content) {
        if (!StringUtils.hasText(content)) {
            return false;
        }

        // 常见的句子结束标点符号
        return content.equals(".") || content.equals("!") || content.equals("?") ||
               content.equals("。") || content.equals("！") || content.equals("？");
    }

    /**
     * 从 Speechmatics 转录结果中提取指定语言的文案
     *
     * @param transcriptionResult Speechmatics 转录结果 JSON 字符串
     * @param language 语言代码 ("cmn" 为中文, "en" 为英文)
     * @return 文案列表
     */
    public static List<VideoCopyModel> extractTranscript(String transcriptionResult, String language) {
        List<VideoCopyModel> copyList = new ArrayList<>();

        if (!StringUtils.hasText(transcriptionResult) || !StringUtils.hasText(language)) {
            log.warn("转录结果或语言代码为空，无法提取文案");
            return copyList;
        }

        try {
            // 解析 JSON
            JSONObject jsonObject = JSON.parseObject(transcriptionResult);

            // 获取 translations 对象
            JSONObject translations = jsonObject.getJSONObject("translations");
            if (translations == null) {
                log.warn("转录结果中未找到 translations 字段");
                return copyList;
            }

            // 获取指定语言的翻译数组
            JSONArray languageArray = translations.getJSONArray(language);
            if (languageArray == null || languageArray.isEmpty()) {
                log.warn("转录结果中未找到语言 {} 的翻译内容", language);
                return copyList;
            }

            // 遍历翻译数组，提取文案
            for (int i = 0; i < languageArray.size(); i++) {
                JSONObject item = languageArray.getJSONObject(i);

                String content = item.getString("content");
                Double startTime = item.getDouble("start_time");
                Double endTime = item.getDouble("end_time");

                if (StringUtils.hasText(content) && startTime != null && endTime != null) {
                    VideoCopyModel copyModel = new VideoCopyModel();
                    copyModel.setContent(content);
                    copyModel.setStartTime(startTime);
                    copyModel.setEndTime(endTime);

                    copyList.add(copyModel);
                }
            }

            log.info("成功提取 {} 语言文案，共 {} 条", language, copyList.size());

        } catch (Exception e) {
            log.error("提取 {} 语言文案异常", language, e);
        }

        return copyList;
    }

    /**
     * 从 Speechmatics 转录结果中同时提取原文、中英文文案
     *
     * @param transcriptionResult Speechmatics 转录结果 JSON 字符串
     * @return 包含原文、中英文文案的对象
     */
    public static TranscriptResultModel extractAllTranscripts(String transcriptionResult) {
        TranscriptResultModel result = new TranscriptResultModel();
        result.setOriginalTranscript(extractOriginalTranscript(transcriptionResult));
        result.setChineseTranscript(extractChineseTranscript(transcriptionResult));
        result.setEnglishTranscript(extractEnglishTranscript(transcriptionResult));
        return result;
    }

    /**
     * 从 Speechmatics 转录结果中同时提取中英文文案（保持向后兼容）
     *
     * @param transcriptionResult Speechmatics 转录结果 JSON 字符串
     * @return 包含中英文文案的对象
     */
    public static TranscriptResultModel extractBothLanguages(String transcriptionResult) {
        TranscriptResultModel result = new TranscriptResultModel();
        result.setChineseTranscript(extractChineseTranscript(transcriptionResult));
        result.setEnglishTranscript(extractEnglishTranscript(transcriptionResult));
        return result;
    }


    public static void main(String[] args) {
        String s = "dsa";
        List<VideoCopyModel> videoCopyModels = extractOriginalTranscript(s);
        for (VideoCopyModel videoCopyModel : videoCopyModels) {
            System.out.println(videoCopyModel);
        }

    }
}
