package com.bq.linkcore.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bq.linkcore.client.speechmatics.model.VideoCopyModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 视频文案提取工具类
 */
@Slf4j
public class SpeechmaticsHelper {

    /**
     * 从 Speechmatics 转录结果中提取中文文案
     *
     * @param transcriptionResult Speechmatics 转录结果 JSON 字符串
     * @return 中文文案列表
     */
    public static List<VideoCopyModel> extractChineseTranscript(String transcriptionResult) {
        return extractTranscript(transcriptionResult, "cmn");
    }

    /**
     * 从 Speechmatics 转录结果中提取英文文案
     *
     * @param transcriptionResult Speechmatics 转录结果 JSON 字符串
     * @return 英文文案列表
     */
    public static List<VideoCopyModel> extractEnglishTranscript(String transcriptionResult) {
        return extractTranscript(transcriptionResult, "en");
    }

    /**
     * 从 Speechmatics 转录结果中提取原文文案
     * 从 results 数组中提取词和标点符号，重新组合成分段的原文
     *
     * @param transcriptionResult Speechmatics 转录结果 JSON 字符串
     * @return 原文文案列表
     */
    public static List<VideoCopyModel> extractOriginalTranscript(String transcriptionResult) {
        List<VideoCopyModel> copyList = new ArrayList<>();

        if (!StringUtils.hasText(transcriptionResult)) {
            log.warn("转录结果为空，无法提取原文");
            return copyList;
        }

        try {
            // 解析 JSON
            JSONObject jsonObject = JSON.parseObject(transcriptionResult);

            // 获取 results 数组
            JSONArray results = jsonObject.getJSONArray("results");
            if (results == null || results.isEmpty()) {
                log.warn("转录结果中未找到 results 字段");
                return copyList;
            }

            // 重新组合原文
            copyList = reconstructOriginalText(results);

            log.info("成功提取原文文案，共 {} 条", copyList.size());

        } catch (Exception e) {
            log.error("提取原文文案异常", e);
        }

        return copyList;
    }

    /**
     * 重新组合原文文本
     * 将 results 数组中的词和标点符号重新组合成分段的文案
     *
     * @param results results 数组
     * @return 重新组合后的原文文案列表
     */
    private static List<VideoCopyModel> reconstructOriginalText(JSONArray results) {
        List<VideoCopyModel> copyList = new ArrayList<>();

        if (results == null || results.isEmpty()) {
            return copyList;
        }

        StringBuilder currentSentence = new StringBuilder();
        Double sentenceStartTime = null;
        Double sentenceEndTime = null;

        for (int i = 0; i < results.size(); i++) {
            JSONObject item = results.getJSONObject(i);

            String type = item.getString("type");
            if (!"word".equals(type) && !"punctuation".equals(type)) {
                continue; // 只处理词和标点符号
            }

            // 获取内容和时间信息
            JSONArray alternatives = item.getJSONArray("alternatives");
            if (alternatives == null || alternatives.isEmpty()) {
                continue;
            }

            JSONObject alternative = alternatives.getJSONObject(0);
            String content = alternative.getString("content");
            Double startTime = item.getDouble("start_time");
            Double endTime = item.getDouble("end_time");

            if (!StringUtils.hasText(content) || startTime == null || endTime == null) {
                continue;
            }

            // 设置句子开始时间
            if (sentenceStartTime == null) {
                sentenceStartTime = startTime;
            }

            // 处理标点符号的附加规则
            String attachesToValue = item.getString("attaches_to");
            Boolean isEos = item.getBoolean("is_eos"); // end of sentence
            boolean attachesToPrevious = "previous".equals(attachesToValue);

            if ("punctuation".equals(type) && attachesToPrevious) {
                // 标点符号直接附加到前面的词，不加空格
                currentSentence.append(content);
            } else if ("word".equals(type)) {
                // 词之间用空格分隔
                if (currentSentence.length() > 0) {
                    currentSentence.append(" ");
                }
                currentSentence.append(content);
            } else {
                // 其他标点符号
                currentSentence.append(content);
            }

            sentenceEndTime = endTime;

            // 判断是否句子结束
            if (Boolean.TRUE.equals(isEos) || isEndOfSentence(content)) {
                // 创建一个文案段落
                if (currentSentence.length() > 0 && sentenceStartTime != null && sentenceEndTime != null) {
                    VideoCopyModel copyModel = new VideoCopyModel();
                    copyModel.setContent(currentSentence.toString().trim());
                    copyModel.setStartTime(sentenceStartTime);
                    copyModel.setEndTime(sentenceEndTime);

                    copyList.add(copyModel);
                }

                // 重置句子构建器
                currentSentence = new StringBuilder();
                sentenceStartTime = null;
                sentenceEndTime = null;
            }
        }

        // 处理最后一个句子（如果没有明确的结束标记）
        if (currentSentence.length() > 0 && sentenceStartTime != null && sentenceEndTime != null) {
            VideoCopyModel copyModel = new VideoCopyModel();
            copyModel.setContent(currentSentence.toString().trim());
            copyModel.setStartTime(sentenceStartTime);
            copyModel.setEndTime(sentenceEndTime);

            copyList.add(copyModel);
        }

        return copyList;
    }

    /**
     * 判断是否为句子结束标点符号
     *
     * @param content 内容
     * @return 是否为句子结束标点符号
     */
    private static boolean isEndOfSentence(String content) {
        if (!StringUtils.hasText(content)) {
            return false;
        }

        // 常见的句子结束标点符号
        return content.equals(".") || content.equals("!") || content.equals("?") ||
               content.equals("。") || content.equals("！") || content.equals("？");
    }

    /**
     * 从 Speechmatics 转录结果中提取指定语言的文案
     *
     * @param transcriptionResult Speechmatics 转录结果 JSON 字符串
     * @param language 语言代码 ("cmn" 为中文, "en" 为英文)
     * @return 文案列表
     */
    public static List<VideoCopyModel> extractTranscript(String transcriptionResult, String language) {
        List<VideoCopyModel> copyList = new ArrayList<>();

        if (!StringUtils.hasText(transcriptionResult) || !StringUtils.hasText(language)) {
            log.warn("转录结果或语言代码为空，无法提取文案");
            return copyList;
        }

        try {
            // 解析 JSON
            JSONObject jsonObject = JSON.parseObject(transcriptionResult);

            // 获取 translations 对象
            JSONObject translations = jsonObject.getJSONObject("translations");
            if (translations == null) {
                log.warn("转录结果中未找到 translations 字段");
                return copyList;
            }

            // 获取指定语言的翻译数组
            JSONArray languageArray = translations.getJSONArray(language);
            if (languageArray == null || languageArray.isEmpty()) {
                log.warn("转录结果中未找到语言 {} 的翻译内容", language);
                return copyList;
            }

            // 遍历翻译数组，提取文案
            for (int i = 0; i < languageArray.size(); i++) {
                JSONObject item = languageArray.getJSONObject(i);

                String content = item.getString("content");
                Double startTime = item.getDouble("start_time");
                Double endTime = item.getDouble("end_time");

                if (StringUtils.hasText(content) && startTime != null && endTime != null) {
                    VideoCopyModel copyModel = new VideoCopyModel();
                    copyModel.setContent(content);
                    copyModel.setStartTime(startTime);
                    copyModel.setEndTime(endTime);

                    copyList.add(copyModel);
                }
            }

            log.info("成功提取 {} 语言文案，共 {} 条", language, copyList.size());

        } catch (Exception e) {
            log.error("提取 {} 语言文案异常", language, e);
        }

        return copyList;
    }

    /**
     * 从 Speechmatics 转录结果中同时提取原文、中英文文案
     *
     * @param transcriptionResult Speechmatics 转录结果 JSON 字符串
     * @return 包含原文、中英文文案的对象
     */
    public static TranscriptResult extractAllTranscripts(String transcriptionResult) {
        TranscriptResult result = new TranscriptResult();
        result.setOriginalTranscript(extractOriginalTranscript(transcriptionResult));
        result.setChineseTranscript(extractChineseTranscript(transcriptionResult));
        result.setEnglishTranscript(extractEnglishTranscript(transcriptionResult));
        return result;
    }

    /**
     * 从 Speechmatics 转录结果中同时提取中英文文案（保持向后兼容）
     *
     * @param transcriptionResult Speechmatics 转录结果 JSON 字符串
     * @return 包含中英文文案的对象
     */
    public static TranscriptResult extractBothLanguages(String transcriptionResult) {
        TranscriptResult result = new TranscriptResult();
        result.setChineseTranscript(extractChineseTranscript(transcriptionResult));
        result.setEnglishTranscript(extractEnglishTranscript(transcriptionResult));
        return result;
    }

    /**
     * 转录结果包装类，包含原文、中英文文案
     */
    public static class TranscriptResult {
        private List<VideoCopyModel> originalTranscript;
        private List<VideoCopyModel> chineseTranscript;
        private List<VideoCopyModel> englishTranscript;

        public List<VideoCopyModel> getOriginalTranscript() {
            return originalTranscript;
        }

        public void setOriginalTranscript(List<VideoCopyModel> originalTranscript) {
            this.originalTranscript = originalTranscript;
        }

        public List<VideoCopyModel> getChineseTranscript() {
            return chineseTranscript;
        }

        public void setChineseTranscript(List<VideoCopyModel> chineseTranscript) {
            this.chineseTranscript = chineseTranscript;
        }

        public List<VideoCopyModel> getEnglishTranscript() {
            return englishTranscript;
        }

        public void setEnglishTranscript(List<VideoCopyModel> englishTranscript) {
            this.englishTranscript = englishTranscript;
        }
    }

    public static void main(String[] args) {
        String s = "{\n" +
                "    \"format\": \"2.9\",\n" +
                "    \"job\": {\n" +
                "        \"created_at\": \"2025-07-24T07:27:06.357Z\",\n" +
                "        \"data_name\": \"\",\n" +
                "        \"duration\": 32,\n" +
                "        \"id\": \"xojb1z70y2\"\n" +
                "    },\n" +
                "    \"metadata\": {\n" +
                "        \"created_at\": \"2025-07-24T07:27:21.260180Z\",\n" +
                "        \"language_identification\": {\n" +
                "            \"predicted_language\": \"en\"\n" +
                "        },\n" +
                "        \"language_pack_info\": {\n" +
                "            \"adapted\": false,\n" +
                "            \"itn\": true,\n" +
                "            \"language_description\": \"English\",\n" +
                "            \"word_delimiter\": \" \",\n" +
                "            \"writing_direction\": \"left-to-right\"\n" +
                "        },\n" +
                "        \"orchestrator_version\": \"2025.07.28781+f8b0678540.HEAD\",\n" +
                "        \"transcription_config\": {\n" +
                "            \"diarization\": \"speaker\",\n" +
                "            \"language\": \"auto\"\n" +
                "        },\n" +
                "        \"translation_config\": {\n" +
                "            \"target_languages\": [\n" +
                "                \"en\",\n" +
                "                \"cmn\"\n" +
                "            ]\n" +
                "        },\n" +
                "        \"type\": \"transcription\"\n" +
                "    },\n" +
                "    \"results\": [\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"Okay\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 0.88,\n" +
                "            \"start_time\": 0.16,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \".\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"attaches_to\": \"previous\",\n" +
                "            \"end_time\": 0.88,\n" +
                "            \"is_eos\": true,\n" +
                "            \"start_time\": 0.88,\n" +
                "            \"type\": \"punctuation\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"How\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 1.24,\n" +
                "            \"start_time\": 1.04,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"cute\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 1.52,\n" +
                "            \"start_time\": 1.24,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"is\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 1.72,\n" +
                "            \"start_time\": 1.52,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"this\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 2.16,\n" +
                "            \"start_time\": 1.72,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"?\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"attaches_to\": \"previous\",\n" +
                "            \"end_time\": 2.16,\n" +
                "            \"is_eos\": true,\n" +
                "            \"start_time\": 2.16,\n" +
                "            \"type\": \"punctuation\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"I\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 2.52,\n" +
                "            \"start_time\": 2.16,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"always\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 2.96,\n" +
                "            \"start_time\": 2.52,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"carry\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 3.32,\n" +
                "            \"start_time\": 2.96,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"my\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 3.56,\n" +
                "            \"start_time\": 3.32,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"wellness\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 4.04,\n" +
                "            \"start_time\": 3.56,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"must\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 4.36,\n" +
                "            \"start_time\": 4.04,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"haves\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 4.72,\n" +
                "            \"start_time\": 4.36,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"in\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 4.96,\n" +
                "            \"start_time\": 4.72,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"this\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 5.2,\n" +
                "            \"start_time\": 4.96,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"pouch\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 5.6,\n" +
                "            \"start_time\": 5.2,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \".\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"attaches_to\": \"previous\",\n" +
                "            \"end_time\": 5.6,\n" +
                "            \"is_eos\": true,\n" +
                "            \"start_time\": 5.6,\n" +
                "            \"type\": \"punctuation\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"First\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 6.0,\n" +
                "            \"start_time\": 5.64,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"up\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 6.2,\n" +
                "            \"start_time\": 6.0,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \",\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"attaches_to\": \"previous\",\n" +
                "            \"end_time\": 6.2,\n" +
                "            \"is_eos\": false,\n" +
                "            \"start_time\": 6.2,\n" +
                "            \"type\": \"punctuation\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"horizon\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 6.96,\n" +
                "            \"start_time\": 6.2,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \".\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"attaches_to\": \"previous\",\n" +
                "            \"end_time\": 6.96,\n" +
                "            \"is_eos\": true,\n" +
                "            \"start_time\": 6.96,\n" +
                "            \"type\": \"punctuation\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"It's\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 7.28,\n" +
                "            \"start_time\": 7.08,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"a\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 7.36,\n" +
                "            \"start_time\": 7.28,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"blend\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 7.68,\n" +
                "            \"start_time\": 7.36,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"of\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 7.8,\n" +
                "            \"start_time\": 7.68,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 0.8,\n" +
                "                    \"content\": \"pre\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 8.08,\n" +
                "            \"start_time\": 7.8,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"plus\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 8.36,\n" +
                "            \"start_time\": 8.08,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 0.96,\n" +
                "                    \"content\": \"probiotics\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 9.0,\n" +
                "            \"start_time\": 8.36,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"with\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 9.24,\n" +
                "            \"start_time\": 9.0,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"enzymes\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 9.72,\n" +
                "            \"start_time\": 9.24,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"that\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 9.88,\n" +
                "            \"start_time\": 9.72,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"keep\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 10.12,\n" +
                "            \"start_time\": 9.88,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"my\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 10.28,\n" +
                "            \"start_time\": 10.12,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"gut\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 10.52,\n" +
                "            \"start_time\": 10.28,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"happy\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 10.96,\n" +
                "            \"start_time\": 10.56,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \".\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"attaches_to\": \"previous\",\n" +
                "            \"end_time\": 10.96,\n" +
                "            \"is_eos\": true,\n" +
                "            \"start_time\": 10.96,\n" +
                "            \"type\": \"punctuation\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"And\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 11.44,\n" +
                "            \"start_time\": 10.96,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"Golden\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 11.88,\n" +
                "            \"start_time\": 11.48,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"are\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 12.08,\n" +
                "            \"start_time\": 11.88,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"my\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 12.28,\n" +
                "            \"start_time\": 12.08,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"go\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 12.44,\n" +
                "            \"start_time\": 12.28,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"to\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 12.56,\n" +
                "            \"start_time\": 12.44,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"antioxidant\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 13.4,\n" +
                "            \"start_time\": 12.6,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"for\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 13.6,\n" +
                "            \"start_time\": 13.4,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"healthy\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 14.0,\n" +
                "            \"start_time\": 13.6,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"inflammation\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 14.72,\n" +
                "            \"start_time\": 14.0,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \",\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"attaches_to\": \"previous\",\n" +
                "            \"end_time\": 14.72,\n" +
                "            \"is_eos\": false,\n" +
                "            \"start_time\": 14.72,\n" +
                "            \"type\": \"punctuation\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"support\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 15.4,\n" +
                "            \"start_time\": 14.72,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"and\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 15.68,\n" +
                "            \"start_time\": 15.4,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"glow\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 16.04,\n" +
                "            \"start_time\": 15.68,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"from\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 16.24,\n" +
                "            \"start_time\": 16.04,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"within\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 16.64,\n" +
                "            \"start_time\": 16.24,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \".\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"attaches_to\": \"previous\",\n" +
                "            \"end_time\": 16.64,\n" +
                "            \"is_eos\": true,\n" +
                "            \"start_time\": 16.64,\n" +
                "            \"type\": \"punctuation\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"And\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 16.92,\n" +
                "            \"start_time\": 16.64,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"finally\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 17.56,\n" +
                "            \"start_time\": 16.92,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"rise\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 18.08,\n" +
                "            \"start_time\": 17.56,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"a\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 18.36,\n" +
                "            \"start_time\": 18.12,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"clean\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 18.64,\n" +
                "            \"start_time\": 18.36,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"multivitamin\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 19.52,\n" +
                "            \"start_time\": 18.64,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"that\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 19.68,\n" +
                "            \"start_time\": 19.52,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"covers\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 20.08,\n" +
                "            \"start_time\": 19.68,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"energy\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 20.64,\n" +
                "            \"start_time\": 20.08,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \",\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"attaches_to\": \"previous\",\n" +
                "            \"end_time\": 20.64,\n" +
                "            \"is_eos\": false,\n" +
                "            \"start_time\": 20.64,\n" +
                "            \"type\": \"punctuation\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"immunity\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 21.44,\n" +
                "            \"start_time\": 20.68,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \",\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"attaches_to\": \"previous\",\n" +
                "            \"end_time\": 21.44,\n" +
                "            \"is_eos\": false,\n" +
                "            \"start_time\": 21.44,\n" +
                "            \"type\": \"punctuation\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"and\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 21.96,\n" +
                "            \"start_time\": 21.68,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"overall\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 22.6,\n" +
                "            \"start_time\": 21.96,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"wellness\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 23.28,\n" +
                "            \"start_time\": 22.6,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \".\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"attaches_to\": \"previous\",\n" +
                "            \"end_time\": 23.28,\n" +
                "            \"is_eos\": true,\n" +
                "            \"start_time\": 23.28,\n" +
                "            \"type\": \"punctuation\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"Here's\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 23.64,\n" +
                "            \"start_time\": 23.28,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 0.97,\n" +
                "                    \"content\": \"the\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 23.8,\n" +
                "            \"start_time\": 23.64,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"supplement\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 24.32,\n" +
                "            \"start_time\": 23.8,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"label\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 24.72,\n" +
                "            \"start_time\": 24.32,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"thoughtfully\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 25.2,\n" +
                "            \"start_time\": 24.72,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"made\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 25.52,\n" +
                "            \"start_time\": 25.2,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"and\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 25.8,\n" +
                "            \"start_time\": 25.52,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"packed\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 26.12,\n" +
                "            \"start_time\": 25.8,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"with\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 26.32,\n" +
                "            \"start_time\": 26.12,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"nutrients\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 26.96,\n" +
                "            \"start_time\": 26.32,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"I\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 27.2,\n" +
                "            \"start_time\": 26.96,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"trust\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 27.64,\n" +
                "            \"start_time\": 27.2,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"every\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 28.0,\n" +
                "            \"start_time\": 27.64,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"day\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 28.52,\n" +
                "            \"start_time\": 28.04,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \".\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"attaches_to\": \"previous\",\n" +
                "            \"end_time\": 28.52,\n" +
                "            \"is_eos\": true,\n" +
                "            \"start_time\": 28.52,\n" +
                "            \"type\": \"punctuation\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"This\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 28.76,\n" +
                "            \"start_time\": 28.52,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"bundle\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 29.16,\n" +
                "            \"start_time\": 28.76,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"is\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 29.4,\n" +
                "            \"start_time\": 29.16,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"everything\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 29.96,\n" +
                "            \"start_time\": 29.4,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"I\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 30.08,\n" +
                "            \"start_time\": 29.96,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"need\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 30.28,\n" +
                "            \"start_time\": 30.08,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"to\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 30.44,\n" +
                "            \"start_time\": 30.28,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"stay\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 30.68,\n" +
                "            \"start_time\": 30.44,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \"balanced\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"end_time\": 31.2,\n" +
                "            \"start_time\": 30.68,\n" +
                "            \"type\": \"word\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"alternatives\": [\n" +
                "                {\n" +
                "                    \"confidence\": 1.0,\n" +
                "                    \"content\": \".\",\n" +
                "                    \"language\": \"en\",\n" +
                "                    \"speaker\": \"S1\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"attaches_to\": \"previous\",\n" +
                "            \"end_time\": 31.2,\n" +
                "            \"is_eos\": true,\n" +
                "            \"start_time\": 31.2,\n" +
                "            \"type\": \"punctuation\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"translations\": {\n" +
                "        \"cmn\": [\n" +
                "            {\n" +
                "                \"content\": \"没事的。\",\n" +
                "                \"end_time\": 0.88,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 0.16\n" +
                "            },\n" +
                "            {\n" +
                "                \"content\": \"这是多么可爱?\",\n" +
                "                \"end_time\": 2.16,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 1.04\n" +
                "            },\n" +
                "            {\n" +
                "                \"content\": \"我总是把我的健康必须在这个袋子里。\",\n" +
                "                \"end_time\": 5.6,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 2.16\n" +
                "            },\n" +
                "            {\n" +
                "                \"content\": \"第一,地平线。\",\n" +
                "                \"end_time\": 6.96,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 5.64\n" +
                "            },\n" +
                "            {\n" +
                "                \"content\": \"它是预先加上含有酶的益生菌混合物,使我的肠道快乐。\",\n" +
                "                \"end_time\": 10.96,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 7.08\n" +
                "            },\n" +
                "            {\n" +
                "                \"content\": \"黄金是我的抗氧化剂,有助于健康的炎症,支持和从内部发光。\",\n" +
                "                \"end_time\": 16.64,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 10.96\n" +
                "            },\n" +
                "            {\n" +
                "                \"content\": \"最后,建立一个清洁的多种维生素,涵盖能量,免疫力和整体健康。\",\n" +
                "                \"end_time\": 23.28,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 16.64\n" +
                "            },\n" +
                "            {\n" +
                "                \"content\": \"这里是精心制作并带有我每天信任的营养素的补充剂标签。\",\n" +
                "                \"end_time\": 28.52,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 23.28\n" +
                "            },\n" +
                "            {\n" +
                "                \"content\": \"这个包裹是我保持平衡所需的一切。\",\n" +
                "                \"end_time\": 31.2,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 28.52\n" +
                "            }\n" +
                "        ],\n" +
                "        \"en\": [\n" +
                "            {\n" +
                "                \"content\": \"Okay.\",\n" +
                "                \"end_time\": 0.88,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 0.16\n" +
                "            },\n" +
                "            {\n" +
                "                \"content\": \"How cute is this?\",\n" +
                "                \"end_time\": 2.16,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 1.04\n" +
                "            },\n" +
                "            {\n" +
                "                \"content\": \"I always carry my wellness must haves in this pouch.\",\n" +
                "                \"end_time\": 5.6,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 2.16\n" +
                "            },\n" +
                "            {\n" +
                "                \"content\": \"First up, horizon.\",\n" +
                "                \"end_time\": 6.96,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 5.64\n" +
                "            },\n" +
                "            {\n" +
                "                \"content\": \"It's a blend of pre plus probiotics with enzymes that keep my gut happy.\",\n" +
                "                \"end_time\": 10.96,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 7.08\n" +
                "            },\n" +
                "            {\n" +
                "                \"content\": \"And Golden are my go to antioxidant for healthy inflammation, support and glow from within.\",\n" +
                "                \"end_time\": 16.64,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 10.96\n" +
                "            },\n" +
                "            {\n" +
                "                \"content\": \"And finally rise a clean multivitamin that covers energy, immunity, and overall wellness.\",\n" +
                "                \"end_time\": 23.28,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 16.64\n" +
                "            },\n" +
                "            {\n" +
                "                \"content\": \"Here's the supplement label thoughtfully made and packed with nutrients I trust every day.\",\n" +
                "                \"end_time\": 28.52,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 23.28\n" +
                "            },\n" +
                "            {\n" +
                "                \"content\": \"This bundle is everything I need to stay balanced.\",\n" +
                "                \"end_time\": 31.2,\n" +
                "                \"speaker\": \"S1\",\n" +
                "                \"start_time\": 28.52\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        System.out.println(extractOriginalTranscript(s));
    }
}
