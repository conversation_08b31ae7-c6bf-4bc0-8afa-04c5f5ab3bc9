package com.bq.linkcore.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bq.linkcore.client.speechmatics.model.VideoCopyModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 视频文案提取工具类
 */
@Slf4j
public class SpeechmaticsHelper {

    /**
     * 从 Speechmatics 转录结果中提取中文文案
     *
     * @param transcriptionResult Speechmatics 转录结果 JSON 字符串
     * @return 中文文案列表
     */
    public static List<VideoCopyModel> extractChineseTranscript(String transcriptionResult) {
        return extractTranscript(transcriptionResult, "cmn");
    }

    /**
     * 从 Speechmatics 转录结果中提取英文文案
     *
     * @param transcriptionResult Speechmatics 转录结果 JSON 字符串
     * @return 英文文案列表
     */
    public static List<VideoCopyModel> extractEnglishTranscript(String transcriptionResult) {
        return extractTranscript(transcriptionResult, "en");
    }

    /**
     * 从 Speechmatics 转录结果中提取指定语言的文案
     *
     * @param transcriptionResult Speechmatics 转录结果 JSON 字符串
     * @param language 语言代码 ("cmn" 为中文, "en" 为英文)
     * @return 文案列表
     */
    public static List<VideoCopyModel> extractTranscript(String transcriptionResult, String language) {
        List<VideoCopyModel> copyList = new ArrayList<>();

        if (!StringUtils.hasText(transcriptionResult) || !StringUtils.hasText(language)) {
            log.warn("转录结果或语言代码为空，无法提取文案");
            return copyList;
        }

        try {
            // 解析 JSON
            JSONObject jsonObject = JSON.parseObject(transcriptionResult);

            // 获取 translations 对象
            JSONObject translations = jsonObject.getJSONObject("translations");
            if (translations == null) {
                log.warn("转录结果中未找到 translations 字段");
                return copyList;
            }

            // 获取指定语言的翻译数组
            JSONArray languageArray = translations.getJSONArray(language);
            if (languageArray == null || languageArray.isEmpty()) {
                log.warn("转录结果中未找到语言 {} 的翻译内容", language);
                return copyList;
            }

            // 遍历翻译数组，提取文案
            for (int i = 0; i < languageArray.size(); i++) {
                JSONObject item = languageArray.getJSONObject(i);

                String content = item.getString("content");
                Double startTime = item.getDouble("start_time");
                Double endTime = item.getDouble("end_time");

                if (StringUtils.hasText(content) && startTime != null && endTime != null) {
                    VideoCopyModel copyModel = new VideoCopyModel();
                    copyModel.setContent(content);
                    copyModel.setStartTime(startTime);
                    copyModel.setEndTime(endTime);

                    copyList.add(copyModel);
                }
            }

            log.info("成功提取 {} 语言文案，共 {} 条", language, copyList.size());

        } catch (Exception e) {
            log.error("提取 {} 语言文案异常", language, e);
        }

        return copyList;
    }

    /**
     * 从 Speechmatics 转录结果中同时提取中英文文案
     *
     * @param transcriptionResult Speechmatics 转录结果 JSON 字符串
     * @return 包含中英文文案的对象
     */
    public static TranscriptResult extractBothLanguages(String transcriptionResult) {
        TranscriptResult result = new TranscriptResult();
        result.setChineseTranscript(extractChineseTranscript(transcriptionResult));
        result.setEnglishTranscript(extractEnglishTranscript(transcriptionResult));
        return result;
    }

    /**
     * 转录结果包装类，包含中英文文案
     */
    public static class TranscriptResult {
        private List<VideoCopyModel> chineseTranscript;
        private List<VideoCopyModel> englishTranscript;

        public List<VideoCopyModel> getChineseTranscript() {
            return chineseTranscript;
        }

        public void setChineseTranscript(List<VideoCopyModel> chineseTranscript) {
            this.chineseTranscript = chineseTranscript;
        }

        public List<VideoCopyModel> getEnglishTranscript() {
            return englishTranscript;
        }

        public void setEnglishTranscript(List<VideoCopyModel> englishTranscript) {
            this.englishTranscript = englishTranscript;
        }
    }
}
