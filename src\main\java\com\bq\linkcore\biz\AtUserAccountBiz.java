package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.AtAccountUserSettingDO;
import com.bq.linkcore.common.PlatformEnum;
import com.bq.linkcore.dao.mapper.AtAccountUserSettingMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class AtUserAccountBiz {

    @Resource
    private AtAccountUserSettingMapper atAccountUserSettingMapper;

    /**
     * 根据用户ID查询用户关联的TikTok账户列表
     * @param userId 用户ID
     * @return 用户账户设置列表
     */
    public List<AtAccountUserSettingDO> queryUserTiktokAccountsByUserId(Long userId) {
        LambdaQueryWrapper<AtAccountUserSettingDO> queryWrapper = new LambdaQueryWrapper<AtAccountUserSettingDO>()
                .eq(AtAccountUserSettingDO::getUserId, userId)
                .eq(AtAccountUserSettingDO::getPlatform, "tiktok")
                .eq(AtAccountUserSettingDO::getIsDel, 0)
                .orderByDesc(AtAccountUserSettingDO::getCreateTime);
        return atAccountUserSettingMapper.selectList(queryWrapper);
    }

    /**
     * 根据用户ID和账户唯一ID查询用户账户设置
     * @param userId 用户ID
     * @param atUniqueId 账户唯一ID
     * @return 用户账户设置
     */
    public AtAccountUserSettingDO queryUserAccountByUserIdAndUniqueId(Long userId, String atUniqueId) {
        LambdaQueryWrapper<AtAccountUserSettingDO> queryWrapper = new LambdaQueryWrapper<AtAccountUserSettingDO>()
                .eq(AtAccountUserSettingDO::getUserId, userId)
                .eq(AtAccountUserSettingDO::getAtUniqueId, atUniqueId)
                .eq(AtAccountUserSettingDO::getPlatform, PlatformEnum.tiktok.getCode())
                .eq(AtAccountUserSettingDO::getIsDel, 0);
        return atAccountUserSettingMapper.selectOne(queryWrapper);
    }

    /**
     * 插入用户账户设置
     * @param userAccountSetting 用户账户设置实体
     * @return 插入影响的行数
     */
    public int insertUserAccountSetting(AtAccountUserSettingDO userAccountSetting) {
        return atAccountUserSettingMapper.insert(userAccountSetting);
    }

    /**
     * 更新用户账户设置
     * @param userAccountSetting 用户账户设置实体（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateUserAccountSetting(AtAccountUserSettingDO userAccountSetting) {
        return atAccountUserSettingMapper.updateById(userAccountSetting);
    }

    /**
     * 逻辑删除用户账户设置
     * @param userId 用户ID
     * @param atUniqueId 账户唯一ID
     * @return 更新影响的行数
     */
    public int logicalDeleteUserAccount(Long userId, String atUniqueId) {
        LambdaQueryWrapper<AtAccountUserSettingDO> queryWrapper = new LambdaQueryWrapper<AtAccountUserSettingDO>()
                .eq(AtAccountUserSettingDO::getUserId, userId)
                .eq(AtAccountUserSettingDO::getAtUniqueId, atUniqueId)
                .eq(AtAccountUserSettingDO::getPlatform, "tiktok")
                .eq(AtAccountUserSettingDO::getIsDel, 0);

        AtAccountUserSettingDO updateEntity = new AtAccountUserSettingDO();
        updateEntity.setIsDel(1);
        updateEntity.setUpdateTime(java.time.LocalDateTime.now());

        return atAccountUserSettingMapper.update(updateEntity, queryWrapper);
    }

    /**
     * 检查是否还有其他用户在监控该账户
     * @param atUniqueId 账户唯一ID
     * @param excludeUserId 排除的用户ID（当前操作的用户）
     * @return 是否还有其他用户在监控
     */
    public boolean hasOtherUsersMonitoring(String atUniqueId, Long excludeUserId) {
        LambdaQueryWrapper<AtAccountUserSettingDO> queryWrapper = new LambdaQueryWrapper<AtAccountUserSettingDO>()
                .eq(AtAccountUserSettingDO::getAtUniqueId, atUniqueId)
                .eq(AtAccountUserSettingDO::getPlatform, "tiktok")
                .eq(AtAccountUserSettingDO::getIsDel, 0)
                .ne(AtAccountUserSettingDO::getUserId, excludeUserId);

        return atAccountUserSettingMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 检查是否还有任何用户在监控该账户
     * @param atUniqueId 账户唯一ID
     * @return 是否还有用户在监控
     */
    public boolean hasAnyUserMonitoring(String atUniqueId) {
        LambdaQueryWrapper<AtAccountUserSettingDO> queryWrapper = new LambdaQueryWrapper<AtAccountUserSettingDO>()
                .eq(AtAccountUserSettingDO::getAtUniqueId, atUniqueId)
                .eq(AtAccountUserSettingDO::getPlatform, "tiktok")
                .eq(AtAccountUserSettingDO::getIsDel, 0);

        return atAccountUserSettingMapper.selectCount(queryWrapper) > 0;
    }

}
