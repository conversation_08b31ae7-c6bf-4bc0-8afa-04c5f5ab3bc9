package com.bq.linkcore.services.impl.monitor.core;

import com.bq.linkcore.bean.dto.AuthorTaggingModel;
import com.bq.linkcore.common.PlatformEnum;
import com.bq.linkcore.services.impl.monitor.provider.tiktok.TiktokMonitoringProvider;
import com.bq.linkcore.services.pool.ThreadRejectPolicy;
import com.bq.linkcore.services.pool.WorkThreadFactory;
import com.bq.linkcore.services.pool.WorkThreadPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 监控核心服务 - 任务编排与通用逻辑
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
@Slf4j
@Service
public class MonitoringCoreService {

    private final static String CORE_THREAD_TAG = "monitoring-core";

    /**
     * 核心业务线程池
     */
    private final WorkThreadPool coreThreadPool = new WorkThreadPool(
            5,
            10,
            3L,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(10000),
            new WorkThreadFactory(CORE_THREAD_TAG),
            new ThreadRejectPolicy(CORE_THREAD_TAG)
    );

    @Resource
    private TiktokMonitoringProvider tiktokMonitoringProvider;

    /**
     * 执行监控任务 - 主入口
     * 
     * @param platformEnum 任务类型 (douyin/tiktok)
     * @param authorTaggingModels 用户唯一标识列表
     */
    public void executeMonitoringTask(PlatformEnum platformEnum,
                                      List<AuthorTaggingModel> authorTaggingModels,
                                      Integer isDaily) {
        log.info("开始执行监控任务，任务类型: {}, 用户数量: {}", platformEnum, authorTaggingModels.size());
        
        // 提交到核心线程池进行任务编排
        coreThreadPool.addTask(() -> {
            try {
                dispatchTask(platformEnum, authorTaggingModels, isDaily);
            } catch (Exception e) {
                log.error("执行监控任务异常，任务类型: {}", platformEnum, e);
            }
        });
    }

    /**
     * 任务分派逻辑
     */
    private void dispatchTask(PlatformEnum platformEnum, List<AuthorTaggingModel> authorTaggingModels, Integer isDaily) {
        switch (platformEnum) {
            case tiktok:
                log.info("分派TikTok监控任务，用户数量: {}", authorTaggingModels.size());
                tiktokMonitoringProvider.processAuthorAndWorksBatchSync(authorTaggingModels, isDaily);
                break;

            default:
                log.warn("未知的任务类型: {}", platformEnum);
                break;
        }
    }

    /**
     * 执行作者信息监控任务
     */
    public void executeAuthorMonitoringTask(String taskType, List<AuthorTaggingModel> authorTaggingModels, Integer isDaily) {
        log.info("开始执行作者信息监控任务，任务类型: {}, 用户数量: {}", taskType, authorTaggingModels.size());
        
        coreThreadPool.addTask(() -> {
            try {
                dispatchAuthorTask(taskType, authorTaggingModels, isDaily);
            } catch (Exception e) {
                log.error("执行作者信息监控任务异常，任务类型: {}", taskType, e);
            }
        });
    }

    /**
     * 作者信息任务分派
     */
    private void dispatchAuthorTask(String taskType, List<AuthorTaggingModel> authorTaggingModels, Integer isDaily) {
        switch (taskType.toLowerCase()) {
            case "tiktok":
                tiktokMonitoringProvider.processAuthorProfilesBatchSync(authorTaggingModels, isDaily);
                break;

            default:
                log.warn("未知的作者信息任务类型: {}", taskType);
                break;
        }
    }

    /**
     * 执行作品监控任务
     */
    public void executeWorkMonitoringTask(String taskType, List<String> uniqueIds) {
        log.info("开始执行作品监控任务，任务类型: {}, 用户数量: {}", taskType, uniqueIds.size());
        
        coreThreadPool.addTask(() -> {
            try {
                dispatchWorkTask(taskType, uniqueIds);
            } catch (Exception e) {
                log.error("执行作品监控任务异常，任务类型: {}", taskType, e);
            }
        });
    }

    /**
     * 作品任务分派
     */
    private void dispatchWorkTask(String taskType, List<String> uniqueIds) {
        switch (taskType.toLowerCase()) {
            case "tiktok":
                tiktokMonitoringProvider.processWorksBatch(uniqueIds);
                break;
                
            default:
                log.warn("未知的作品任务类型: {}", taskType);
                break;
        }
    }

    /**
     * 获取核心服务状态
     */
    public MonitoringCoreStatus getCoreStatus() {
        return MonitoringCoreStatus.builder()
                .threadPoolTaskCount(coreThreadPool.getTaskCount())
                .threadPoolThreadCount(coreThreadPool.getThreadCount())
//                .tiktokProviderStatus(tiktokMonitoringProvider.getProviderStatus())
                .build();
    }

    /**
     * 停止核心服务
     */
    public void shutdown() {
        log.info("开始停止监控核心服务");
        coreThreadPool.stop();
        tiktokMonitoringProvider.shutdown();
        log.info("监控核心服务已停止");
    }
}
