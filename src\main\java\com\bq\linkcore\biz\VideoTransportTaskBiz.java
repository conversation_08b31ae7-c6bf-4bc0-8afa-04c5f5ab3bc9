package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.bq.linkcore.bean.entity.VideoTranscriptsTaskDO;
import com.bq.linkcore.dao.mapper.VideoTranscriptsTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class VideoTransportTaskBiz {

    @Resource
    private VideoTranscriptsTaskMapper videoTranscriptsTaskMapper;

    /**
     * 插入视频转写任务
     *
     * @param taskDO 任务实体
     * @return 插入影响的行数
     */
    public int insertVideoTranscriptsTask(VideoTranscriptsTaskDO taskDO) {
        if (taskDO == null) {
            log.warn("插入视频转写任务失败：任务实体为空");
            return 0;
        }

        // 设置默认值
        if (taskDO.getCreateTime() == null) {
            taskDO.setCreateTime(LocalDateTime.now());
        }
        if (taskDO.getUpdateTime() == null) {
            taskDO.setUpdateTime(LocalDateTime.now());
        }
        if (taskDO.getIsDel() == null) {
            taskDO.setIsDel(0);
        }
        if (taskDO.getStatus() == null) {
            taskDO.setStatus(0); // 默认状态：分析中
        }

        try {
            int result = videoTranscriptsTaskMapper.insert(taskDO);
            log.info("成功插入视频转写任务，workId: {}, taskId: {}, 影响行数: {}",
                    taskDO.getWorkId(), taskDO.getTaskId(), result);
            return result;
        } catch (Exception e) {
            log.error("插入视频转写任务异常，workId: {}, taskId: {}",
                    taskDO.getWorkId(), taskDO.getTaskId(), e);
            return 0;
        }
    }

    /**
     * 创建新的视频转写任务
     *
     * @param workId 作品唯一标识
     * @param videoUrl 视频链接
     * @param videoDuration 视频时长（毫秒）
     * @param taskId 任务ID
     * @param platform 平台（dy、xhs等）
     * @return 插入影响的行数
     */
    public int createVideoTranscriptsTask(String workId, String videoUrl, Long videoDuration,
                                         String taskId, String platform) {
        if (!StringUtils.hasText(workId) || !StringUtils.hasText(taskId)) {
            log.warn("创建视频转写任务失败：workId或taskId为空");
            return 0;
        }

        VideoTranscriptsTaskDO taskDO = VideoTranscriptsTaskDO.builder()
                .workId(workId)
                .videoUrl(videoUrl)
                .videoDuration(videoDuration)
                .taskId(taskId)
                .platform(platform)
                .status(0) // 0-分析中
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .isDel(0)
                .build();

        return insertVideoTranscriptsTask(taskDO);
    }

    /**
     * 根据workId查询视频转写任务
     *
     * @param workId 作品唯一标识
     * @return 任务实体
     */
    public VideoTranscriptsTaskDO queryVideoTranscriptsTaskByWorkId(String workId) {
        if (!StringUtils.hasText(workId)) {
            log.warn("查询视频转写任务失败：workId为空");
            return null;
        }

        try {
            QueryWrapper<VideoTranscriptsTaskDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("work_id", workId)
                       .eq("is_del", 0)
                       .orderByDesc("create_time")
                       .last("LIMIT 1");

            VideoTranscriptsTaskDO result = videoTranscriptsTaskMapper.selectOne(queryWrapper);
            log.debug("查询视频转写任务，workId: {}, 结果: {}", workId, result != null ? "找到" : "未找到");
            return result;
        } catch (Exception e) {
            log.error("查询视频转写任务异常，workId: {}", workId, e);
            return null;
        }
    }

    /**
     * 根据taskId查询视频转写任务
     *
     * @param taskId 任务ID
     * @return 任务实体
     */
    public VideoTranscriptsTaskDO queryVideoTranscriptsTaskByTaskId(String taskId) {
        if (!StringUtils.hasText(taskId)) {
            log.warn("查询视频转写任务失败：taskId为空");
            return null;
        }

        try {
            QueryWrapper<VideoTranscriptsTaskDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("task_id", taskId)
                       .eq("is_del", 0);

            VideoTranscriptsTaskDO result = videoTranscriptsTaskMapper.selectOne(queryWrapper);
            log.debug("查询视频转写任务，taskId: {}, 结果: {}", taskId, result != null ? "找到" : "未找到");
            return result;
        } catch (Exception e) {
            log.error("查询视频转写任务异常，taskId: {}", taskId, e);
            return null;
        }
    }

    /**
     * 查询所有正在执行中的视频转写任务（status = 0）
     *
     * @return 正在执行中的任务列表
     */
    public List<VideoTranscriptsTaskDO> queryRunningVideoTranscriptsTasks() {
        try {
            QueryWrapper<VideoTranscriptsTaskDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", 0) // 0-分析中
                       .eq("is_del", 0)
                       .orderByAsc("create_time"); // 按创建时间升序，优先处理早期任务

            List<VideoTranscriptsTaskDO> result = videoTranscriptsTaskMapper.selectList(queryWrapper);
            log.debug("查询正在执行中的视频转写任务，共找到: {} 个任务", result.size());
            return result;
        } catch (Exception e) {
            log.error("查询正在执行中的视频转写任务异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 新状态 (0-分析中, 1-分析完成, 2-分析失败)
     * @return 更新影响的行数
     */
    public int updateTaskStatus(String taskId, Integer status) {
        if (!StringUtils.hasText(taskId) || status == null) {
            log.warn("更新任务状态失败：taskId或status为空");
            return 0;
        }

        try {
            UpdateWrapper<VideoTranscriptsTaskDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("task_id", taskId)
                        .eq("is_del", 0)
                        .set("status", status)
                        .set("update_time", LocalDateTime.now());

            int result = videoTranscriptsTaskMapper.update(null, updateWrapper);
            log.info("更新任务状态，taskId: {}, status: {}, 影响行数: {}", taskId, status, result);
            return result;
        } catch (Exception e) {
            log.error("更新任务状态异常，taskId: {}, status: {}", taskId, status, e);
            return 0;
        }
    }

    /**
     * 更新任务状态和结果
     *
     * @param taskId 任务ID
     * @param status 新状态 (0-分析中, 1-分析完成, 2-分析失败)
     * @param result 分析结果
     * @param resultJson 分析结果JSON
     * @return 更新影响的行数
     */
    public int updateTaskStatusAndResult(String taskId, Integer status, String result, String resultJson) {
        if (!StringUtils.hasText(taskId) || status == null) {
            log.warn("更新任务状态和结果失败：taskId或status为空");
            return 0;
        }

        try {
            UpdateWrapper<VideoTranscriptsTaskDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("task_id", taskId)
                        .eq("is_del", 0)
                        .set("status", status)
                        .set("update_time", LocalDateTime.now());

            if (StringUtils.hasText(result)) {
                updateWrapper.set("result", result);
            }
            if (StringUtils.hasText(resultJson)) {
                updateWrapper.set("result_json", resultJson);
            }

            int updateResult = videoTranscriptsTaskMapper.update(null, updateWrapper);
            log.info("更新任务状态和结果，taskId: {}, status: {}, 影响行数: {}", taskId, status, updateResult);
            return updateResult;
        } catch (Exception e) {
            log.error("更新任务状态和结果异常，taskId: {}, status: {}", taskId, status, e);
            return 0;
        }
    }

    /**
     * 更新任务失败状态和错误信息
     *
     * @param taskId 任务ID
     * @param errMsg 错误信息
     * @return 更新影响的行数
     */
    public int updateTaskFailure(String taskId, String errMsg) {
        if (!StringUtils.hasText(taskId)) {
            log.warn("更新任务失败状态失败：taskId为空");
            return 0;
        }

        try {
            UpdateWrapper<VideoTranscriptsTaskDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("task_id", taskId)
                        .eq("is_del", 0)
                        .set("status", 2) // 2-分析失败
                        .set("update_time", LocalDateTime.now());

            if (StringUtils.hasText(errMsg)) {
                updateWrapper.set("err_msg", errMsg);
            }

            int result = videoTranscriptsTaskMapper.update(null, updateWrapper);
            log.info("更新任务失败状态，taskId: {}, errMsg: {}, 影响行数: {}", taskId, errMsg, result);
            return result;
        } catch (Exception e) {
            log.error("更新任务失败状态异常，taskId: {}, errMsg: {}", taskId, errMsg, e);
            return 0;
        }
    }

    /**
     * 更新任务完成状态和结果
     *
     * @param taskId 任务ID
     * @param result 分析结果
     * @param resultJson 分析结果JSON
     * @return 更新影响的行数
     */
    public int updateTaskSuccess(String taskId, String result, String resultJson) {
        return updateTaskStatusAndResult(taskId, 1, result, resultJson); // 1-分析完成
    }

    /**
     * 修改视频转写任务
     *
     * @param taskDO 任务实体（必须包含ID或taskId）
     * @return 更新影响的行数
     */
    public int updateVideoTranscriptsTask(VideoTranscriptsTaskDO taskDO) {
        if (taskDO == null) {
            log.warn("修改视频转写任务失败：任务实体为空");
            return 0;
        }

        if (taskDO.getId() == null && !StringUtils.hasText(taskDO.getTaskId())) {
            log.warn("修改视频转写任务失败：ID和taskId都为空");
            return 0;
        }

        try {
            // 设置更新时间
            taskDO.setUpdateTime(LocalDateTime.now());

            int result;
            if (taskDO.getId() != null) {
                // 根据ID更新
                result = videoTranscriptsTaskMapper.updateById(taskDO);
                log.info("根据ID修改视频转写任务，id: {}, 影响行数: {}", taskDO.getId(), result);
            } else {
                // 根据taskId更新
                UpdateWrapper<VideoTranscriptsTaskDO> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("task_id", taskDO.getTaskId())
                           .eq("is_del", 0);

                result = videoTranscriptsTaskMapper.update(taskDO, updateWrapper);
                log.info("根据taskId修改视频转写任务，taskId: {}, 影响行数: {}", taskDO.getTaskId(), result);
            }

            return result;
        } catch (Exception e) {
            log.error("修改视频转写任务异常，taskDO: {}", taskDO, e);
            return 0;
        }
    }

    /**
     * 软删除视频转写任务
     *
     * @param taskId 任务ID
     * @return 删除影响的行数
     */
    public int deleteVideoTranscriptsTask(String taskId) {
        if (!StringUtils.hasText(taskId)) {
            log.warn("删除视频转写任务失败：taskId为空");
            return 0;
        }

        try {
            UpdateWrapper<VideoTranscriptsTaskDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("task_id", taskId)
                        .eq("is_del", 0)
                        .set("is_del", 1)
                        .set("update_time", LocalDateTime.now());

            int result = videoTranscriptsTaskMapper.update(null, updateWrapper);
            log.info("软删除视频转写任务，taskId: {}, 影响行数: {}", taskId, result);
            return result;
        } catch (Exception e) {
            log.error("软删除视频转写任务异常，taskId: {}", taskId, e);
            return 0;
        }
    }
}
