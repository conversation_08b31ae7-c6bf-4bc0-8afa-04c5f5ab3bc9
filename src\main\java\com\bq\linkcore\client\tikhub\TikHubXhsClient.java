package com.bq.linkcore.client.tikhub;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bq.linkcore.client.tikhub.models.ArticleDetailModel;
import com.bq.linkcore.utils.DateTimeUtil;
import com.bq.linkcore.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @date 2023/9/29 16:32
 * @className TikHubXhsClient
 * @description
 */
@Slf4j
@Component
public class TikHubXhsClient {
    @Autowired
    private TikHubHttpRequester tikHubHttpRequester;

    public ArticleDetailModel getXhsDetailV2(String workId) {
        String url = "/api/v1/xiaohongshu/web_v2/fetch_feed_notes?note_id=";
        url += workId;

        JSONObject object = tikHubHttpRequester.callGet(url);
        if (object == null) {
            return null;
        }

        ArticleDetailModel articleDetailModel = new ArticleDetailModel();
        try {
            JSONObject data = object.getJSONObject("data");
            Integer code = data.getInteger("code");
            if (code != 0) {
                return null;
            }

            JSONArray d = data.getJSONArray("data");
            if (d.size() == 0) {
                return null;
            }

            JSONObject notes = d.getJSONObject(0);

            JSONObject user = notes.getJSONObject("user");
            String author_id = user.getString("userid");
            articleDetailModel.setAuthorId(author_id);
            articleDetailModel.setAuthorIdentity(author_id);
            articleDetailModel.setAuthorName(StringUtil.parseStr(user, "name"));
            articleDetailModel.setAuthorAvatar(StringUtil.parseStr(user, "image"));
            articleDetailModel.setAuthorUrl("https://www.xiaohongshu.com/user/profile/" + author_id);

            JSONArray noteList = notes.getJSONArray("note_list");
            if (CollectionUtils.isEmpty(noteList)) {
                return null;
            }

            JSONObject note = noteList.getJSONObject(0);
            articleDetailModel.setWorkId(workId);
            articleDetailModel.setWorkUuid(workId);
            articleDetailModel.setUrl("https://www.xiaohongshu.com/discovery/item/" + workId);
            articleDetailModel.setTitle(StringUtil.parseStr(note, "title"));
            articleDetailModel.setContent(StringUtil.parseStr(note, "desc"));
            articleDetailModel.setDigest(StringUtil.parseStr(note, "desc"));

            Long ts = note.getLong("time");
            articleDetailModel.setPublishTime(DateTimeUtil.covertDateTime(ts));
            articleDetailModel.setLocationIp(StringUtil.parseStr(note, "ip_location"));

            articleDetailModel.setCollectCount(StringUtil.parseInt(note, "collected_count"));
            articleDetailModel.setLikeCount(StringUtil.parseInt(note, "liked_count"));
            articleDetailModel.setCommentCount(StringUtil.parseInt(note, "comments_count"));
            articleDetailModel.setShareCount(StringUtil.parseInt(note, "shared_count"));

            try {
                JSONArray topics = note.getJSONArray("topics");
                JSONObject topic = topics.getJSONObject(0);
                articleDetailModel.setThumbnailLink(StringUtil.parseStr(topic, "image"));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }

        return articleDetailModel;
    }

    public JSONObject searchXhsNotePageV1(String url,
                                          String key,
                                          Integer pageNo,
                                          String sortType,
                                          String noteType) {
        try {
            url = url + "page=" + pageNo + "&sort=" + sortType +
                    "&noteType=" + noteType + "&keyword=" + key;

            JSONObject data = tikHubHttpRequester.callGet(url);
            if (data == null) {
                data = tikHubHttpRequester.callGet(url);
            }

            if (data == null) {
                return null;
            }

            Integer code = data.getInteger("code");
            if (!code.equals(200)) {
                return null;
            }

            return data;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }


    public JSONObject searchXhsNotePageV2(String url,
                                          String key,
                                          Integer pageNo,
                                          String sortType,
                                          String noteType,
                                          String noteTime) {
        try {
            url = url + "&page=" + pageNo + "&sort_type=" + sortType +
                    "&note_type=" + noteType + "&keywords=" + URLEncoder.encode(key, "UTF-8");

            JSONObject data = tikHubHttpRequester.callGet(url);
            if (data == null) {
                data = tikHubHttpRequester.callGet(url);
            }

            return data;
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }

    private String convertCity(String key) {
        try {
            if (StringUtils.isBlank(key)) {
                return "";
            }

            if (StringUtil.containsChinese(key)) {
                return key;
            }

            return StringUtil.convertCity(key);
        } catch (Exception e) {
            return "";
        }
    }


}
