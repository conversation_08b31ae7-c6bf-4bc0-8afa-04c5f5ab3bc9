package com.bq.linkcore.bean.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TenantAddReqVo {

    @ApiModelProperty(value = "租户名称")
    @NotNull(message = "租户名称不能为空")
    private String tenantName;

    @ApiModelProperty(value = "指定的管理员手机号")
    @NotNull(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "指定的管理员邮箱")
    private String email;

    @ApiModelProperty(value = "0/null = 指定新用户， 1 指定已经存在用户")
    @Max(1)
    @Min(0)
    @NotNull(message = "type 不能为空")
    private Integer type;

    @ApiModelProperty(value = "创建者所拥有的权限")
    private List<String> permissionCodes;

    @ApiModelProperty(value = "昵称")
    private String nickname;

}
