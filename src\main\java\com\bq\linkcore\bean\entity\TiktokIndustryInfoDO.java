package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-18 02:51:06
 * @ClassName: TiktokIndustryInfoDO
 * @Description: TikTok行业信息表
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("tiktok_industry_info")
public class TiktokIndustryInfoDO implements Serializable{

    private static final long serialVersionUID = 1L;

    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 行业ID
     */
    private Long industryId;

    /**
     * 行业名称
     */
    private String industryValue;

    /**
     * 行业标签
     */
    private String industryLabel;

    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;




}
