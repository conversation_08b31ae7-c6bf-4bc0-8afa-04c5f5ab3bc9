package com.bq.linkcore.common;

public class ServiceException extends RuntimeException {

    protected String errorCode;
    protected Object data;

    public ServiceException(ResponseMsg responseMsg) {
        this(responseMsg.getCode(), responseMsg.getMsg(), null, null);
    }

    public ServiceException(String errorCode, String message, Object data, Throwable e) {
        super(message, e);
        this.errorCode = errorCode;
        this.data = data;
    }

    public ServiceException(String errorCode, String message, Object data) {
        this(errorCode, message, data, null);
    }

    public ServiceException(String errorCode, String message) {
        this(errorCode, message, null, null);
    }

    public ServiceException(String message, Throwable e) {
        this(null, message, null, e);
    }

    public ServiceException(String code) {
        this(code, null, null, null);
    }

    public ServiceException() {

    }

    public ServiceException(Throwable e) {
        super(e);
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
