package com.bq.linkcore.client;

import com.bq.data.cache.NewRedisson;
import com.bq.data.cache.OverallRRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2022/2/22 17:02
 * @className RateLimiterFactory
 * @description
 */
@Component
public class RateLimiterFactory {
    private final static String RATE_LIMITER_KEY = "rate_limiter_";
    private final ConcurrentHashMap<String, OverallRRateLimiter> rateLimiterMap = new ConcurrentHashMap<>();

    @Autowired
    private RedissonClient redissonClient;

    public boolean getToken(String key, int defaultRate, RateIntervalUnit unit) {
        NewRedisson newRedisson = (NewRedisson) redissonClient;

        String limitKey = RATE_LIMITER_KEY + key;

        OverallRRateLimiter rRateLimiter = (OverallRRateLimiter) newRedisson.getRateLimiter(limitKey, RateType.OVERALL);

        //根据业务选择 RateType 参数
        rRateLimiter.trySetRate(defaultRate, 1, unit);
        return rRateLimiter.tryAcquire(1);
    }
}
