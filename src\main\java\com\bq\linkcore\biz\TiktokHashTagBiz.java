package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.TiktokHashtagInfoDO;
import com.bq.linkcore.bean.entity.TiktokIndustryInfoDO;
import com.bq.linkcore.dao.mapper.TiktokHashtagInfoMapper;
import com.bq.linkcore.dao.mapper.TiktokIndustryInfoMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class TiktokHashTagBiz {

    @Resource
    private TiktokHashtagInfoMapper hashtagInfoMapper;

    public int insert(TiktokHashtagInfoDO tiktokHashtagInfoDO) {
        return hashtagInfoMapper.insert(tiktokHashtagInfoDO);
    }

    public int update(TiktokHashtagInfoDO hashtagInfoDO) {
        return hashtagInfoMapper.updateById(hashtagInfoDO);
    }

    public TiktokHashtagInfoDO queryById(Long hashtagId) {
        LambdaQueryWrapper<TiktokHashtagInfoDO> queryWrapper = new LambdaQueryWrapper<TiktokHashtagInfoDO>()
                .eq(TiktokHashtagInfoDO::getHashtagId, hashtagId);

        return hashtagInfoMapper.selectOne(queryWrapper);
    }

    public TiktokHashtagInfoDO queryByName(Long hashtagName) {
        LambdaQueryWrapper<TiktokHashtagInfoDO> queryWrapper = new LambdaQueryWrapper<TiktokHashtagInfoDO>()
                .eq(TiktokHashtagInfoDO::getHashtagName, hashtagName);

        return hashtagInfoMapper.selectOne(queryWrapper);
    }

}
