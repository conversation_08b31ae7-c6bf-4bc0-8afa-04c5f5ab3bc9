package com.bq.linkcore.services.impl.aiAction;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bq.linkcore.services.impl.aiAction.models.ContentAnalysisVo;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ReportGenerator 测试类
 */
public class ReportGeneratorTest {

    @Test
    public void testContentAnalysisJsonParsing() {
        // 模拟大模型返回的JSON字符串
        String jsonStr = "[" +
                "  {" +
                "    \"美妆护肤类\": \"占比50%，贡献63%的播放量\"," +
                "    \"content\": \"内容定位：专注于美妆和护肤产品的评测、推荐和使用体验分享。\\n内容特点：注重产品效果展示，强调实际使用感受和个人体验。\\n典型选题：护肤品试用、美妆产品测评、护肤步骤分享等。\\n数据表现：此类视频平均播放量较高，用户互动活跃。\"," +
                "    \"relatedVideos\": [\"https://www.tiktok.com/@mia_manhattan_ny/video/7525091112302693646\", \"https://www.tiktok.com/@mia_manhattan_ny/video/7524174251218226445\", \"https://www.tiktok.com/@mia_manhattan_ny/video/7524176288538479886\"]" +
                "  }," +
                "  {" +
                "    \"生活分享类\": \"占比30%，贡献25%的播放量\"," +
                "    \"content\": \"内容定位：分享日常生活中的点点滴滴和个人感悟。\\n内容特点：真实自然，贴近生活，容易引起共鸣。\\n典型选题：日常vlog、生活小技巧、个人感悟分享等。\\n数据表现：此类视频互动率较高，但播放量相对较低。\"," +
                "    \"relatedVideos\": [\"https://www.tiktok.com/@example/video/1\", \"https://www.tiktok.com/@example/video/2\"]" +
                "  }" +
                "]";

        // 解析JSON
        List<ContentAnalysisVo.ContentType> contentTypes = new ArrayList<>();

        try {
            // 使用FastJSON解析JSON数组
            JSONArray jsonArray = JSON.parseArray(jsonStr);

            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject item = jsonArray.getJSONObject(i);

                // 找到第一个不是"content"和"relatedVideos"的键作为内容类型标题
                String contentTitle = null;
                String contentDescription = null;
                List<String> relatedVideos = new ArrayList<>();

                for (String key : item.keySet()) {
                    Object value = item.get(key);

                    if ("content".equals(key)) {
                        contentDescription = value != null ? value.toString() : "";
                    } else if ("relatedVideos".equals(key)) {
                        if (value instanceof JSONArray) {
                            JSONArray videoArray = (JSONArray) value;
                            for (int j = 0; j < videoArray.size(); j++) {
                                Object video = videoArray.get(j);
                                if (video != null) {
                                    relatedVideos.add(video.toString());
                                }
                            }
                        }
                    } else {
                        // 这是内容类型的标题和占比信息
                        contentTitle = key + "：" + (value != null ? value.toString() : "");
                    }
                }

                // 组合完整的内容描述
                String fullContent = "";
                if (contentTitle != null) {
                    fullContent = contentTitle;
                    if (contentDescription != null && !contentDescription.isEmpty()) {
                        fullContent += "\n" + contentDescription;
                    }
                } else if (contentDescription != null) {
                    fullContent = contentDescription;
                }

                ContentAnalysisVo.ContentType contentType = ContentAnalysisVo.ContentType.builder()
                        .content(fullContent)
                        .relatedVideos(relatedVideos)
                        .build();
                contentTypes.add(contentType);
            }

        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }

        // 验证解析结果
        assertEquals(2, contentTypes.size(), "应该解析出2个内容类型");

        // 验证第一个内容类型
        ContentAnalysisVo.ContentType firstType = contentTypes.get(0);
        assertTrue(firstType.getContent().startsWith("美妆护肤类：占比50%，贡献63%的播放量"), "第一个内容类型标题应该正确");
        assertTrue(firstType.getContent().contains("内容定位：专注于美妆和护肤产品的评测"), "第一个内容类型应该包含详细描述");
        assertEquals(3, firstType.getRelatedVideos().size(), "第一个内容类型应该有3个相关视频");
        assertTrue(firstType.getRelatedVideos().get(0).contains("7525091112302693646"), "第一个视频链接应该正确");

        // 验证第二个内容类型
        ContentAnalysisVo.ContentType secondType = contentTypes.get(1);
        assertTrue(secondType.getContent().startsWith("生活分享类：占比30%，贡献25%的播放量"), "第二个内容类型标题应该正确");
        assertTrue(secondType.getContent().contains("内容定位：分享日常生活中的点点滴滴"), "第二个内容类型应该包含详细描述");
        assertEquals(2, secondType.getRelatedVideos().size(), "第二个内容类型应该有2个相关视频");

        System.out.println("测试通过！成功解析了" + contentTypes.size() + "个内容类型");
        for (int i = 0; i < contentTypes.size(); i++) {
            ContentAnalysisVo.ContentType type = contentTypes.get(i);
            System.out.println("内容类型 " + (i + 1) + ":");
            System.out.println("  内容: " + type.getContent());
            System.out.println("  相关视频数量: " + type.getRelatedVideos().size());
        }
    }

    @Test
    public void testHitVideoAnalysisJsonParsing() {
        // 模拟大模型返回的JSON字符串
        String jsonStr = "[" +
                "  {" +
                "    \"名称\": \"产品推荐\"," +
                "    \"爆款特征\": \"展示产品特点，分享使用体验，吸引观众购买兴趣。\"," +
                "    \"黄金3S\": \"在视频的前3秒内展示产品的最吸引人的特点或效果。\"," +
                "    \"爆款模型\": \"快速展示产品 -> 分享个人使用体验 -> 强调产品优势 -> 呼吁行动（如购买链接）。\"," +
                "    \"案例\": [\"https://www.tiktok.com/@mia_manhattan_ny/video/7525091112302693646\", \"https://www.tiktok.com/@mia_manhattan_ny/video/7524725604172811534\", \"https://www.tiktok.com/@mia_manhattan_ny/video/7526403072898174221\"]" +
                "  }," +
                "  {" +
                "    \"名称\": \"生活分享\"," +
                "    \"爆款特征\": \"真实自然，贴近生活，容易引起共鸣。\"," +
                "    \"黄金3S\": \"开头直接展示生活场景或问题。\"," +
                "    \"爆款模型\": \"提出问题 -> 展示解决过程 -> 分享心得体会 -> 引发讨论。\"," +
                "    \"案例\": [\"https://www.tiktok.com/@example/video/1\", \"https://www.tiktok.com/@example/video/2\"]" +
                "  }" +
                "]";

        // 解析JSON - 这里我们需要模拟私有方法的逻辑
        List<Object> hitModels = new ArrayList<>();

        try {
            // 使用FastJSON解析JSON数组
            JSONArray jsonArray = JSON.parseArray(jsonStr);

            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject item = jsonArray.getJSONObject(i);

                // 提取各个字段
                String modelName = item.getString("名称");
                String features = item.getString("爆款特征");
                String golden3S = item.getString("黄金3S");
                String modelDescription = item.getString("爆款模型");

                // 组合完整的内容描述
                StringBuilder contentBuilder = new StringBuilder();
                if (modelName != null && !modelName.isEmpty()) {
                    contentBuilder.append(modelName);
                }
                if (features != null && !features.isEmpty()) {
                    contentBuilder.append("\n爆款特征：").append(features);
                }
                if (golden3S != null && !golden3S.isEmpty()) {
                    contentBuilder.append("\n黄金3S：").append(golden3S);
                }
                if (modelDescription != null && !modelDescription.isEmpty()) {
                    contentBuilder.append("\n爆款模型：").append(modelDescription);
                }

                // 提取案例视频
                List<String> relatedVideos = new ArrayList<>();
                JSONArray caseArray = item.getJSONArray("案例");
                if (caseArray != null) {
                    for (int j = 0; j < caseArray.size(); j++) {
                        Object video = caseArray.get(j);
                        if (video != null) {
                            relatedVideos.add(video.toString());
                        }
                    }
                }

                // 创建模拟对象来验证数据
                Object hitModel = new Object() {
                    public String content = contentBuilder.toString();
                    public List<String> videos = relatedVideos;
                };
                hitModels.add(hitModel);
            }

        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }

        // 验证解析结果
        assertEquals(2, hitModels.size(), "应该解析出2个爆款模型");

        System.out.println("爆款视频分析测试通过！成功解析了" + hitModels.size() + "个爆款模型");
        for (int i = 0; i < hitModels.size(); i++) {
            System.out.println("爆款模型 " + (i + 1) + " 解析成功");
        }
    }
}
