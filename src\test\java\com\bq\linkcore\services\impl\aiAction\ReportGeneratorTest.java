package com.bq.linkcore.services.impl.aiAction;

import com.bq.linkcore.services.impl.aiAction.models.ContentAnalysisVo;
import com.google.gson.Gson;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ReportGenerator 测试类
 */
public class ReportGeneratorTest {

    private final Gson gson = new Gson();

    @Test
    public void testContentAnalysisJsonParsing() {
        // 模拟大模型返回的JSON字符串
        String jsonStr = "[" +
                "  {" +
                "    \"美妆护肤类\": \"占比50%，贡献63%的播放量\"," +
                "    \"content\": \"内容定位：专注于美妆和护肤产品的评测、推荐和使用体验分享。\\n内容特点：注重产品效果展示，强调实际使用感受和个人体验。\\n典型选题：护肤品试用、美妆产品测评、护肤步骤分享等。\\n数据表现：此类视频平均播放量较高，用户互动活跃。\"," +
                "    \"relatedVideos\": [\"https://www.tiktok.com/@mia_manhattan_ny/video/7525091112302693646\", \"https://www.tiktok.com/@mia_manhattan_ny/video/7524174251218226445\", \"https://www.tiktok.com/@mia_manhattan_ny/video/7524176288538479886\"]" +
                "  }," +
                "  {" +
                "    \"生活分享类\": \"占比30%，贡献25%的播放量\"," +
                "    \"content\": \"内容定位：分享日常生活中的点点滴滴和个人感悟。\\n内容特点：真实自然，贴近生活，容易引起共鸣。\\n典型选题：日常vlog、生活小技巧、个人感悟分享等。\\n数据表现：此类视频互动率较高，但播放量相对较低。\"," +
                "    \"relatedVideos\": [\"https://www.tiktok.com/@example/video/1\", \"https://www.tiktok.com/@example/video/2\"]" +
                "  }" +
                "]";

        // 解析JSON
        List<ContentAnalysisVo.ContentType> contentTypes = new ArrayList<>();

        try {
            // 解析JSON为Map数组
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> jsonArray = (List<Map<String, Object>>) gson.fromJson(jsonStr, List.class);

            for (Map<String, Object> item : jsonArray) {
                // 找到第一个不是"content"和"relatedVideos"的键作为内容类型标题
                String contentTitle = null;
                String contentDescription = null;
                List<String> relatedVideos = new ArrayList<>();

                for (Map.Entry<String, Object> entry : item.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();

                    if ("content".equals(key)) {
                        contentDescription = value != null ? value.toString() : "";
                    } else if ("relatedVideos".equals(key)) {
                        if (value instanceof List) {
                            List<?> videoList = (List<?>) value;
                            for (Object video : videoList) {
                                if (video != null) {
                                    relatedVideos.add(video.toString());
                                }
                            }
                        }
                    } else {
                        // 这是内容类型的标题和占比信息
                        contentTitle = key + "：" + (value != null ? value.toString() : "");
                    }
                }

                // 组合完整的内容描述
                String fullContent = "";
                if (contentTitle != null) {
                    fullContent = contentTitle;
                    if (contentDescription != null && !contentDescription.isEmpty()) {
                        fullContent += "\n" + contentDescription;
                    }
                } else if (contentDescription != null) {
                    fullContent = contentDescription;
                }

                ContentAnalysisVo.ContentType contentType = ContentAnalysisVo.ContentType.builder()
                        .content(fullContent)
                        .relatedVideos(relatedVideos)
                        .build();
                contentTypes.add(contentType);
            }

        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }

        // 验证解析结果
        assertEquals(2, contentTypes.size(), "应该解析出2个内容类型");

        // 验证第一个内容类型
        ContentAnalysisVo.ContentType firstType = contentTypes.get(0);
        assertTrue(firstType.getContent().startsWith("美妆护肤类：占比50%，贡献63%的播放量"), "第一个内容类型标题应该正确");
        assertTrue(firstType.getContent().contains("内容定位：专注于美妆和护肤产品的评测"), "第一个内容类型应该包含详细描述");
        assertEquals(3, firstType.getRelatedVideos().size(), "第一个内容类型应该有3个相关视频");
        assertTrue(firstType.getRelatedVideos().get(0).contains("7525091112302693646"), "第一个视频链接应该正确");

        // 验证第二个内容类型
        ContentAnalysisVo.ContentType secondType = contentTypes.get(1);
        assertTrue(secondType.getContent().startsWith("生活分享类：占比30%，贡献25%的播放量"), "第二个内容类型标题应该正确");
        assertTrue(secondType.getContent().contains("内容定位：分享日常生活中的点点滴滴"), "第二个内容类型应该包含详细描述");
        assertEquals(2, secondType.getRelatedVideos().size(), "第二个内容类型应该有2个相关视频");

        System.out.println("测试通过！成功解析了" + contentTypes.size() + "个内容类型");
        for (int i = 0; i < contentTypes.size(); i++) {
            ContentAnalysisVo.ContentType type = contentTypes.get(i);
            System.out.println("内容类型 " + (i + 1) + ":");
            System.out.println("  内容: " + type.getContent());
            System.out.println("  相关视频数量: " + type.getRelatedVideos().size());
        }
    }
}
