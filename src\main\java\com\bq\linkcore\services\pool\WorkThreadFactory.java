package com.bq.linkcore.services.pool;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2022/5/21 22:10
 * @className WorkThreadFactory
 * @description 工作线程默认添加前缀
 */
public class WorkThreadFactory implements ThreadFactory {
    /**
     * 设置线程名称
     */
    private final String prefix;

    /**
     * 线程index
     */
    private final AtomicInteger threadIndex = new AtomicInteger(0);

    public WorkThreadFactory(String prefix) {
        this.prefix = prefix;
    }

    @Override
    public Thread newThread(Runnable r) {
        Thread thread = new Thread(r);
        thread.setName(prefix + "-" + threadIndex.getAndIncrement());
        return thread;
    }
}
