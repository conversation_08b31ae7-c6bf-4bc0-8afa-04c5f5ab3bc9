package com.bq.linkcore.controller;

import com.bq.data.base.bean.BaseEnterpriseUser;
import com.bq.data.base.code.SecurityUtils;
import com.bq.linkcore.bean.dto.UserMemberInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import static com.bq.linkcore.common.Constants.USER_MEMBER_INFO_;


/**
 * <AUTHOR>
 * @date 2024/02/22 15:22
 * @className BaseController
 * @description
 */
@Slf4j
@RestController
public class BaseController {
    @Autowired
    private RedissonClient redissonClient;

    public Long getTokenUserId() {
        RBucket<BaseEnterpriseUser> bucketLoginUser = redissonClient.getBucket(SecurityUtils.getAuthorization());
        return bucketLoginUser.get().getId();
    }

    public Long getTokenUserId(String token) {
        RBucket<BaseEnterpriseUser> bucketLoginUser = redissonClient.getBucket(token);
        if (!bucketLoginUser.isExists()) {
            return null;
        }

        return bucketLoginUser.get().getId();
    }

    public String getToken() {
        return SecurityUtils.getAuthorization();
    }

    public BaseEnterpriseUser getTokenBaseUser() {
        String token = SecurityUtils.getAuthorization();
        if (StringUtils.isBlank(token)) {
            return null;
        }

        RBucket<BaseEnterpriseUser> bucketLoginUser = redissonClient.getBucket(token);
        return bucketLoginUser.get();
    }

    public BaseEnterpriseUser getTokenBaseUser(String token) {
        RBucket<BaseEnterpriseUser> bucketLoginUser = redissonClient.getBucket(token);
        return bucketLoginUser.get();
    }

    public UserMemberInfo queryTxUserMemberInfo(Long userId) {
        String key = USER_MEMBER_INFO_ + userId;
        RBucket<UserMemberInfo> bucketLoginUser = redissonClient.getBucket(key);

        return bucketLoginUser.get();
    }
}
