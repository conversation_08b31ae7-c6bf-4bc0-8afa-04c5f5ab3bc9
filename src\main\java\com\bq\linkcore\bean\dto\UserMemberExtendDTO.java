package com.bq.linkcore.bean.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/4/23 17:40
 * @className UserMemberInfo
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserMemberExtendDTO {

    /**
     * 抖音最大监控达人数量
     */
    private Integer maxDouyinOAuthCount;

    /**
     * 抖音视频统计的最大天数
     */
    private Integer maxDouyinVideoDays;

    /**
     * 可以监控抖音视频任务最大数量
     */
    private Integer maxDouyinVideoMonitorCount;

    /**
     * 授权用户后是否读取评论数据
     */
    private Integer isInitComment = 0;

    /**
     * 授权用户后初始读取的评论数量
     */
    private Integer maxInitCommentCount = 100;

    /**
     * 用户添加评论监控时，默认导出的评论数量
     */
    private Integer defaultCommentCount = 1000;
}
