server:
  port: 9077
spring:
  application:
    name: link-core-service
  cloud:
    nacos:
      discovery:
        server-addr: 47.121.197.92:8848
        namespace: ${spring.profiles.active}
      config:
        server-addr: 47.121.197.92:8848
        file-extension: yml
        namespace: ${spring.profiles.active}


feign:
  sentinel:
    enabled: true
  httpclient:
    enabled: true
management:
  endpoints:
    web:
      exposure:
        include: '*'

springfox:
  documentation:
    swagger:
      use-model-v3: false


sms:
  aliyun:
    accessKeyId: LTAI5tJcK3cDTtaw6arLyTHs
    accessKeySecret: ******************************
    endpoint: dysmsapi.aliyuncs.com
    signName: 今索索
    dataCoreMessage: SMS_468575018
    quotaMessage: SMS_479770115

llm:
  deepseek:
    api-name: deepseek
    api-key: ***********************************
    model: deepseek-chat
    base-url: https://api.deepseek.com
  ali:
    api-key: ***********************************



qingbo:
  projectId: 10312
  sign: 886dbdc01c9127c2d49af566ce3dd73e

jinsuosuo:
  experience:
    tenant: b4c77cd4-bf22-4e8a-8fc1-cba2e23ae2fa
    roleCode: role_9afbe002-8adb-4f27-bb1c-f57ae715bb80_1740910324078
