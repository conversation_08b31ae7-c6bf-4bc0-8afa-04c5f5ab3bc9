package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-13 19:52:16
 * @ClassName: UserMemberInfoDO
 * @Description: 会员表
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("user_member_info")
public class UserMemberInfoDO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 自增长ID
     */
    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 客户级别，0：普通用户；1：vip-1用户
     */
    private Integer level;

    /**
     * 最大监控达人数量
     */
    private Integer maxAuthorCount;

    /**
     * 统计的视频最大天数
     */
    private Integer maxVideoDays;

    /**
     * 监控视频任务最大数量
     */
    private Integer maxVideoMonitorCount;

    /**
     * 会员每月直播监控时长
     */
    private Integer liveMonitorDuration;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 会员开始时间
     */
    private LocalDateTime startTime;

    /**
     * 会员结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Long updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;




}
