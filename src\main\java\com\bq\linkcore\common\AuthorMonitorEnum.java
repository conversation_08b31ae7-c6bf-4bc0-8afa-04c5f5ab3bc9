package com.bq.linkcore.common;

public enum AuthorMonitorEnum {

    Monitoring(1, "监控中"),
    Unmonitored(0, "未监控"),

    ;

    private int code;

    private String desc;

    AuthorMonitorEnum(int flag, String desc) {
        this.code = flag;
        this.desc = desc;
    }


    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
