package com.bq.linkcore.common;


import com.bq.data.base.bean.ResponseData;

/**
 * @ClassName: RD
 * @Description:
 * @author: lmy
 */
public class RD extends ResponseData {

    /**
     * 根据code获取去value
     *
     * @param code
     * @return
     */
    public static ResponseData fail(String code) {
        ResponseData responseData = new ResponseData();
        responseData.setCode(code);
        responseData.setMsg(ResponseMsg.FAIL.getMsg());
        for (ResponseMsg obj : ResponseMsg.values()) {
            if (code.equals(obj.getCode())) {
                responseData.setMsg(obj.getMsg());
                break;
            }
        }
        return responseData;
    }

    /**
     * 根据code获取去value
     *
     * @param msg
     * @return
     */
    public static ResponseData fail(ResponseMsg msg) {
        ResponseData responseData = new ResponseData();
        responseData.setCode(msg.getCode());
        responseData.setMsg(msg.getMsg());

        return responseData;
    }

    public static ResponseData error(String msg) {
        ResponseData responseData = new ResponseData();
        responseData.setCode(ResponseMsg.FAIL.getMsg());
        responseData.setMsg(msg);
        return responseData;
    }


    /**
     * 根据code获取去value
     *
     * @param msg
     * @return
     */
    public static ResponseData ok(ResponseMsg msg) {
        ResponseData responseData = new ResponseData();
        responseData.setCode(msg.getCode());
        responseData.setMsg(msg.getMsg());

        return responseData;
    }
}