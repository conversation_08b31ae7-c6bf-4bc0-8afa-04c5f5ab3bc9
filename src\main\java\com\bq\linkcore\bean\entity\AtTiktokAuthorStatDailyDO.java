package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-15 17:40:50
 * @ClassName: AtTiktokAuthorStatDailyDO
 * @Description: TikTok达人历史信息统计表
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("at_tiktok_author_stat_daily")
public class AtTiktokAuthorStatDailyDO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 发布账号ID
     */
    private String authorId;

    /**
     * 用户名
     */
    private String uniqueId;

    /**
     * 粉丝数
     */
    private Integer followerCount;

    /**
     * 关注数
     */
    private Integer followingCount;

    /**
     * 总获赞数
     */
    private Integer heartCount;

    /**
     * 发布视频数
     */
    private Integer videoCount;

    /**
     * 好友数
     */
    private Integer friendCount;

    /**
     * 播放量
     */
    private Integer playCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 转发数
     */
    private Integer shareCount;

    /**
     * 收藏数
     */
    private Integer collectCount;

    /**
     * 统计时间日期（日）
     */
    private String statDay;

    /**
     * 数据统计时间
     */
    private LocalDateTime statTime;

    /**
     * 是否删除（0未删除，1已删除）
     */
    private Integer isDel;




}
