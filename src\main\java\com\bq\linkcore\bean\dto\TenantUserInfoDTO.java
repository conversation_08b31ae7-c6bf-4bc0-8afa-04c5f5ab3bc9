package com.bq.linkcore.bean.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


@ApiModel(description = "登录接口请求参数对象")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TenantUserInfoDTO {

    @ApiModelProperty(value = "账号，手机号或邮箱")
    private String phone;

    private String email;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "密码")
    private String tenantCode;

    @ApiModelProperty(value = "角色code")
    private String roleCode;

    @ApiModelProperty(value = "用户名称")
    private String username;
}
