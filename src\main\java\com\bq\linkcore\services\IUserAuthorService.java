package com.bq.linkcore.services;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.vo.AtTtIncrStatVo;
import com.bq.linkcore.bean.vo.UserMonitorSettingVo;
import com.bq.linkcore.bean.vo.UserPageRequestVo;
import com.bq.linkcore.client.tikhub.models.AuthorInfo;
import com.bq.linkcore.common.PageResultVO;

public interface IUserAuthorService {

    void initTxUserSetting(Long userId);

    ResponseData updateTxUserMonitorSetting(Long userId, UserMonitorSettingVo vo);

    /**
     * 搜索达人
     * @param userId
     * @param homeUrl
     * @return
     */
    ResponseData<AuthorInfo> searchAuthor(Long userId, String homeUrl);

    /**
     * 添加达人
     * @param userId
     * @param atUniqueId
     * @return
     */
    ResponseData addTxAuthorMonitor(Long userId, String atUniqueId);

    ResponseData<PageResultVO<AtTtIncrStatVo>> queryAccountMonitorInCrList(Long userId, UserPageRequestVo vo);

    ResponseData removeTxMonitor(Long userId, String atUniqueId);

    ResponseData updateTxAuthor(Long userId, String atUniqueId);

    ResponseData queryUpdateInfo(Long userId, String atUniqueId);
}
